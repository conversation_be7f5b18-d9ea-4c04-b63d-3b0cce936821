import mongoose, { Document, Schema } from "mongoose";

export interface IWishlist extends Document {
  userId: mongoose.Types.ObjectId;
  boatId: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const WishlistSchema = new Schema<IWishlist>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    boatId: {
      type: Schema.Types.ObjectId,
      ref: "Boat",
      required: true,
    },
  },
  { timestamps: true },
);

// Create a compound index to ensure a user can only wishlist a boat once
WishlistSchema.index({ userId: 1, boatId: 1 }, { unique: true });

const Wishlist = mongoose.model<IWishlist>("Wishlist", WishlistSchema);

export default Wishlist;
