import express from "express";
import { celebrate } from "celebrate";
import * as commissionController from "../controller/commission.controller";
import * as commissionValidator from "../validator/commission.validator";
import { adminAccess } from "../../../shared/middleware/admin";

const router = express.Router();

// Create or update commission rate (admin only)
router.post(
  "/commission-rates",
  adminAccess,
  celebrate(commissionValidator.createOrUpdateCommissionRateSchema),
  commissionController.createOrUpdateCommissionRate,
);

// Get commission fees (available to all authenticated users)
router.get("/commission-fees", commissionController.getCommissionFees);

export default router;
