import { Router } from "express";
import {
  createBoat,
  deleteBoat,
  getAllBoats,
  getBoat,
  getBoatForBooking,
  getBoatTypes,
  getMyBoats,
  setAvailability,
  updateBoat,
  uploadAttachment,
  getPopularDestinations,
  getPopularLocations,
  getNearbyBoats,
  addBoatImages,
  removeBoatImages,
  removeBoatVideos,
  getOwnerActivities,
} from "../controller/boat.controller";
import {
  idParamValidator,
  setAvailabilityValidator,
  uploadAttachmentValidator,
} from "../validator/boat.validator";
import {
  uploadFiles,
  uploadMultipleFields,
} from "../../../shared/middleware/fileUpload.middleware";
import multer from "multer";
import path from "path";
import fs from "fs";
import recommendedPlaceRoutes from "./recommendedPlace.routes";
import facilityRoutes from "./facility.routes";
import activityRoutes from "./activity.routes";

const router = Router();

// Additional routes
router.use("/places", recommendedPlaceRoutes);
router.use("/facilities", facilityRoutes);
router.use("/activities", activityRoutes);

// Dynamic multer middleware that accepts any field pattern for unlimited facilities and places
const dynamicUpload = (req: any, res: any, next: any) => {
  const storage = multer.diskStorage({
    destination: (req: any, file: any, cb: any) => {
      const uploadPath = path.resolve(
        __dirname,
        "../../../shared/uploads/boats"
      );
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }
      cb(null, uploadPath);
    },
    filename: (req: any, file: any, cb: any) => {
      cb(null, `${Date.now()}-${file.originalname.replace(/\s+/g, "-")}`);
    },
  });

  // Define expected fields for boat creation
  const fields = [
    { name: "images", maxCount: 10 },
    { name: "videos", maxCount: 5 },
    // Dynamic facility image fields
    { name: "facilities[0].images", maxCount: 5 },
    { name: "facilities[1].images", maxCount: 5 },
    { name: "facilities[2].images", maxCount: 5 },
    { name: "facilities[3].images", maxCount: 5 },
    { name: "facilities[4].images", maxCount: 5 },
    // Dynamic recommended place image fields
    { name: "recommendedPlaces[0].images", maxCount: 5 },
    { name: "recommendedPlaces[1].images", maxCount: 5 },
    { name: "recommendedPlaces[2].images", maxCount: 5 },
    { name: "recommendedPlaces[3].images", maxCount: 5 },
    { name: "recommendedPlaces[4].images", maxCount: 5 },
  ];

  const upload = multer({ storage }).fields(fields);
  upload(req, res, next);
};

// Boat management routes with dynamic file upload handling
router.post("/", dynamicUpload, createBoat);

router.get("/", getAllBoats);
router.get("/my-boats", getMyBoats);
router.get("/my-activities", getOwnerActivities);
router.get("/types", getBoatTypes);
router.get("/popular-destinations", getPopularDestinations);
router.get("/popular-locations", getPopularLocations);
router.get("/nearby", getNearbyBoats);
router.get("/:id/booking", idParamValidator, getBoatForBooking);
router.get("/:id", idParamValidator, getBoat);
router.put(
  "/:id",
  idParamValidator,
  uploadMultipleFields("boats", [
    { name: "images" },
    { name: "videos" },
    // Allow form-data to include image files for places and facilities during updates
    { name: "facilities[0].images" },
    { name: "facilities[1].images" },
    { name: "facilities[2].images" },
    { name: "facilities[3].images" },
    { name: "facilities[4].images" },
    { name: "recommendedPlaces[0].images" },
    { name: "recommendedPlaces[1].images" },
    { name: "recommendedPlaces[2].images" },
    { name: "recommendedPlaces[3].images" },
    { name: "recommendedPlaces[4].images" },
  ]),
  updateBoat
);
router.delete("/:id", idParamValidator, deleteBoat);

// Boat availability routes
router.post(
  "/:id/availability",
  idParamValidator,
  setAvailabilityValidator,
  setAvailability
);

// Boat attachment routes - keep ability to upload individual attachments
// Note: for documents, use the document upload API in user-ms
router.post(
  "/:id/attachment/:type",
  uploadAttachmentValidator,
  uploadFiles("boats", "file"),
  uploadAttachment
);

// Image and video management routes
router.post("/:id/images", idParamValidator, uploadFiles("boats", "images"), addBoatImages);
router.delete("/:id/images", idParamValidator, removeBoatImages);
router.delete("/:id/videos", idParamValidator, removeBoatVideos);

export default router;
