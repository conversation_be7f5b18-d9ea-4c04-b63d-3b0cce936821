interface ServiceConfig {
  PORT: number;
}

interface AppConfig {
  db: string;
  PORT: number;
  FILE_BASE_URL: string;
  JWT_SECRET: string;
  JWT_EXIPIRATION_TIME: string;
  JWT_REFRESH_SECRET: string;
  JWT_REFRESH_EXIPIRATION_TIME: string;
  IS_EMAIL_USE_SMTP: string;
  OTP_EXPIRE_TIME: number;
  services: {
    [key: string]: ServiceConfig;
  };
  SMTP_EMAIL: string;
  SMTP_USER: string;
  SMTP_PASSWORD: string;
  SMTP_HOST: string;
  SMTP_PORT: number;
  FROM_EMAIL: string;
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY: string;
  AWS_REGION: string;
  AWS_S3_BUCKET: string;
  ONESIGNAL_APP_ID: string;
  ONESIGNAL_API_KEY: string;
  FIREBASE_PROJECT_ID: string;
  FIREBASE_PRIVATE_KEY: string;
  FIREBASE_CLIENT_EMAIL: string;
  STRIPE_PUBLISHABLE_KEY: string;
  STRIPE_SECRET_KEY: string;
}

declare global {
  var config: AppConfig;
}

export {};
