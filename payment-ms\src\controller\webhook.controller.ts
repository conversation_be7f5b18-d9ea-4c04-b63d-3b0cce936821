import { Request, Response } from 'express';
import Stripe from 'stripe';
import logger from '../../../shared/services/logger.service';
import Booking, { BookingStatus } from '../../../booking-ms/src/models/Booking';
import Payment, { PaymentStatus, PaymentMethod } from '../models/Payment';
import Wallet from '../../../wallet-ms/src/models/Wallet';
import Transaction, { 
  TransactionStatus, 
  TransactionType, 
  TransactionSource 
} from '../../../wallet-ms/src/models/Transaction';
import User from '../../../user-ms/src/models/User';
import Reward from '../../../shared/models/Reward';

const stripe = new Stripe(global.config?.STRIPE_SECRET_KEY || "sk_test_...", {
      apiVersion: "2023-10-16",
});

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export const handleStripeWebhook = async (req: Request, res: Response) => {
  const sig = req.headers['stripe-signature'] as string;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err: any) {
    logger.error(`Webhook signature verification failed: ${err.message}`, {
      service: 'payment-ms',
      error: err
    });
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  logger.info(`Received Stripe webhook event: ${event.type}`, {
    service: 'payment-ms',
    eventId: event.id,
    eventType: event.type
  });

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'payment_intent.canceled':
        await handlePaymentIntentCanceled(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'charge.succeeded':
        await handleChargeSucceeded(event.data.object as Stripe.Charge);
        break;
      
      case 'charge.failed':
        await handleChargeFailed(event.data.object as Stripe.Charge);
        break;
      
      default:
        logger.info(`Unhandled event type: ${event.type}`, {
          service: 'payment-ms',
          eventId: event.id
        });
    }

    res.json({ received: true });
  } catch (error) {
    logger.error(`Error processing webhook event: ${event.type}`, {
      service: 'payment-ms',
      eventId: event.id,
      error: error
    });
    res.status(500).json({ error: 'Webhook processing failed' });
  }
};

/**
 * Handle successful payment intent
 */
async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    const bookingId = paymentIntent.metadata?.bookingId;
    
    if (!bookingId) {
      logger.warn('Payment intent succeeded but no booking ID in metadata', {
        service: 'payment-ms',
        paymentIntentId: paymentIntent.id
      });
      return;
    }

    // Find the booking
    const booking: any = await Booking.findById(bookingId).populate('boatId', 'name ownerId');
    
    if (!booking) {
      logger.error('Booking not found for successful payment intent', {
        service: 'payment-ms',
        paymentIntentId: paymentIntent.id,
        bookingId: bookingId
      });
      return;
    }

    // Update booking status
    await Booking.findByIdAndUpdate(bookingId, {
      paymentIntentStatus: 'succeeded',
      status: BookingStatus.Accepted
    });

    // Extract metadata for payment details
    const metadata = paymentIntent.metadata || {};
    const originalAmount = parseFloat(metadata.originalAmount || '0');
    const discountAmount = parseFloat(metadata.discountAmount || '0');
    const pointsUsed = parseInt(metadata.usePoints || '0');
    const welcomeDiscountApplied = metadata.useWelcomeDiscount === 'true';
    const paymentCurrency = metadata.paymentCurrency || paymentIntent.currency;
    const bookingCurrency = metadata.bookingCurrency || paymentIntent.currency;

    // Create or update payment record
    let payment = await Payment.findOne({ paymentReference: paymentIntent.id });

    if (!payment) {
      payment = new Payment({
        bookingId: booking._id,
        userId: booking.userId,
        boatOwnerId: booking.boatId?.ownerId,
        amount: paymentIntent.amount / 100, // Convert from cents
        adminFee: booking.adminFee || 0,
        ownerAmount: booking.netAmount || (paymentIntent.amount / 100),
        status: PaymentStatus.COMPLETED,
        paymentMethod: PaymentMethod.CARD,
        paymentReference: paymentIntent.id,
        currency: paymentCurrency.toUpperCase(), // Use payment currency from metadata
        ownerCurrency: booking.ownerCurrency || bookingCurrency.toUpperCase(),
        pointsUsed: pointsUsed,
        welcomeDiscountApplied: welcomeDiscountApplied,
        originalAmount: originalAmount || (paymentIntent.amount / 100),
        discountAmount: discountAmount
      });
    } else {
      payment.status = PaymentStatus.COMPLETED;
      payment.amount = paymentIntent.amount / 100;
      payment.pointsUsed = pointsUsed;
      payment.welcomeDiscountApplied = welcomeDiscountApplied;
      payment.originalAmount = originalAmount || (paymentIntent.amount / 100);
      payment.discountAmount = discountAmount;
    }

    await payment.save();

    // Update booking with payment ID
    await Booking.findByIdAndUpdate(bookingId, {
      paymentId: payment._id
    });

    const getUserPayments = await Payment.find({ userId: booking.userId, status: PaymentStatus.COMPLETED });

    if(getUserPayments.length > 2){
      const findUserRewards = await Reward.findOne({ userId: booking.userId });
      if(!findUserRewards?.welcomeDiscounts.length){
        findUserRewards?.welcomeDiscounts.push({used:false})
        await findUserRewards?.save();
      }
    }

    // Create wallet transaction for the user (debit)
    await createWalletTransaction(
      booking.userId,
      paymentIntent.amount / 100,
      TransactionType.DEBIT,
      TransactionSource.BOOKING_PAYMENT,
      `Payment for booking ${bookingId}`,
      bookingId,
      paymentIntent.currency.toUpperCase()
    );

    logger.info('Payment intent succeeded - booking and payment updated', {
      service: 'payment-ms',
      paymentIntentId: paymentIntent.id,
      bookingId: bookingId,
      paymentId: payment._id,
      amount: paymentIntent.amount / 100
    });
  } catch (error) {
    logger.error('Error handling payment intent succeeded', {
      service: 'payment-ms',
      paymentIntentId: paymentIntent.id,
      error: error
    });
  }
}

/**
 * Handle failed payment intent
 */
async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    const bookingId = paymentIntent.metadata?.bookingId;
    
    if (!bookingId) {
      logger.warn('Payment intent failed but no booking ID in metadata', {
        service: 'payment-ms',
        paymentIntentId: paymentIntent.id
      });
      return;
    }

    // Update booking status
    await Booking.findByIdAndUpdate(bookingId, {
      paymentIntentStatus: 'failed',
      status: BookingStatus.Cancelled,
      rejectionReason: 'Payment failed'
    });

    // Update payment record if exists
    await Payment.findOneAndUpdate(
      { paymentReference: paymentIntent.id },
      { status: PaymentStatus.FAILED }
    );

    logger.info('Payment intent failed - booking cancelled', {
      service: 'payment-ms',
      paymentIntentId: paymentIntent.id,
      bookingId: bookingId
    });
  } catch (error) {
    logger.error('Error handling payment intent failed', {
      service: 'payment-ms',
      paymentIntentId: paymentIntent.id,
      error: error
    });
  }
}

/**
 * Handle canceled payment intent
 */
async function handlePaymentIntentCanceled(paymentIntent: Stripe.PaymentIntent) {
  try {
    const bookingId = paymentIntent.metadata?.bookingId;
    
    if (!bookingId) {
      logger.warn('Payment intent canceled but no booking ID in metadata', {
        service: 'payment-ms',
        paymentIntentId: paymentIntent.id
      });
      return;
    }

    // Update booking status
    await Booking.findByIdAndUpdate(bookingId, {
      paymentIntentStatus: 'canceled',
      status: BookingStatus.Cancelled,
      rejectionReason: 'Payment canceled'
    });

    // Update payment record if exists
    await Payment.findOneAndUpdate(
      { paymentReference: paymentIntent.id },
      { status: PaymentStatus.CANCELLED }
    );

    logger.info('Payment intent canceled - booking cancelled', {
      service: 'payment-ms',
      paymentIntentId: paymentIntent.id,
      bookingId: bookingId
    });
  } catch (error) {
    logger.error('Error handling payment intent canceled', {
      service: 'payment-ms',
      paymentIntentId: paymentIntent.id,
      error: error
    });
  }
}

/**
 * Handle successful charge (for additional processing)
 */
async function handleChargeSucceeded(charge: Stripe.Charge) {
  try {
    logger.info('Charge succeeded', {
      service: 'payment-ms',
      chargeId: charge.id,
      amount: charge.amount / 100,
      currency: charge.currency
    });
    
    // Additional charge processing can be added here if needed
  } catch (error) {
    logger.error('Error handling charge succeeded', {
      service: 'payment-ms',
      chargeId: charge.id,
      error: error
    });
  }
}

/**
 * Handle failed charge
 */
async function handleChargeFailed(charge: Stripe.Charge) {
  try {
    logger.info('Charge failed', {
      service: 'payment-ms',
      chargeId: charge.id,
      amount: charge.amount / 100,
      currency: charge.currency,
      failureCode: charge.failure_code,
      failureMessage: charge.failure_message
    });
    
    // Additional charge failure processing can be added here if needed
  } catch (error) {
    logger.error('Error handling charge failed', {
      service: 'payment-ms',
      chargeId: charge.id,
      error: error
    });
  }
}

/**
 * Create wallet transaction
 */
async function createWalletTransaction(
  userId: string,
  amount: number,
  type: TransactionType,
  source: TransactionSource,
  description: string,
  referenceId: string,
  currency: string
) {
  try {
    // Get or create user's wallet
    let wallet = await Wallet.findOne({ userId: userId });
    if (!wallet) {
      wallet = new Wallet({
        userId: userId,
        balance: 0,
        currency: currency
      });
      await wallet.save();
    }

    // Create transaction
    const transaction = new Transaction({
      userId: userId,
      amount: amount,
      type: type,
      source: source,
      status: TransactionStatus.COMPLETED,
      description: description,
      referenceId: referenceId,
      currency: currency
    });

    await transaction.save();

    // Update wallet balance
    const balanceChange = type === TransactionType.CREDIT ? amount : -amount;
    await Wallet.findByIdAndUpdate(wallet._id, {
      $inc: { balance: balanceChange }
    });

    logger.info('Wallet transaction created', {
      service: 'payment-ms',
      userId: userId,
      transactionId: transaction._id,
      amount: amount,
      type: type,
      newBalance: wallet.balance + balanceChange
    });
  } catch (error) {
    logger.error('Error creating wallet transaction', {
      service: 'payment-ms',
      userId: userId,
      amount: amount,
      type: type,
      error: error
    });
  }
}
