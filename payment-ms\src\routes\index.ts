import express from "express";
import {
  processPayment,
  getPaymentDetails,
  getPaymentHistory,
  generateClientToken,
  testStripePayment,
} from "../controller/payment.controller";
import { paymentValidator } from "../validator/payment.validator";

const router = express.Router();

// Generate setup intent for Stripe (replaces Braintree client token)
router.get("/setup-intent", generateClientToken);

// Process a payment for a booking
router.post("/", paymentValidator, processPayment);

// Get payment history (must come before /:id route)
router.get("/history", getPaymentHistory);

// Get payment details (specific ID route)
router.get("/:id", getPaymentDetails);

// Test endpoint for different Stripe payment methods
router.post("/test-stripe", testStripePayment);

// Test card token validation (server-side only)
router.post("/test-card-token", async (req: any, res: any) => {
  try {
    const { cardToken, amount = 10 } = req.body;

    if (!cardToken) {
      return res.status(400).json({
        success: false,
        message: "Card token is required",
      });
    }

    const StripeService = require("../services/stripe.service").default;

    // Test card token validation
    const result = await StripeService.processPaymentWithToken(
      amount,
      cardToken,
      {
        currency: "usd",
        description: "Test payment with card token",
        metadata: { test: "true", method: "card_token" },
      }
    );

    return res.json({
      success: result.success,
      message: result.success ? "Card token processed successfully" : "Card token processing failed",
      data: result,
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
});

export default router;
