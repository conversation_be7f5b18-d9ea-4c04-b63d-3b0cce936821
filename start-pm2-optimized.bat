@echo off
setlocal

REM Set default environment to dev if not provided
IF "%1"=="" (
    SET ENV=dev
) ELSE (
    SET ENV=%1
)

echo Starting Sea Escape Microservices with PM2 (Optimized) in %ENV% environment...
echo.

REM Stop and delete any existing PM2 processes to avoid conflicts
call pm2 delete all

echo.
echo Starting microservices in stages to prevent memory overload...
echo.

REM Start API service first to ensure proper port allocation
echo Starting API gateway...
call pm2 start ecosystem.config.js --only api --env %ENV%
timeout /t 10 > nul

REM Start critical services first
echo Starting critical services (auth, user, payment)...
call pm2 start ecosystem.config.js --only auth-ms,user-ms,payment-ms --env %ENV%
timeout /t 8 > nul

REM Start core business services
echo Starting core business services (boat, booking, wallet)...
call pm2 start ecosystem.config.js --only boat-ms,booking-ms,wallet-ms --env %ENV%
timeout /t 8 > nul

REM Start secondary services
echo Starting secondary services (wishlist, reviews, card)...
call pm2 start ecosystem.config.js --only wishlist-ms,reviews-ms,card-ms --env %ENV%
timeout /t 8 > nul

REM Start communication services
echo Starting communication services (notification, mail, chat)...
call pm2 start ecosystem.config.js --only notification-ms,mail-ms,chat-ms --env %ENV%
timeout /t 8 > nul

REM Start content services
echo Starting content services (newsletter, faq, affiliate)...
call pm2 start ecosystem.config.js --only newsletter-ms,faq-ms,affiliate-ms --env %ENV%
timeout /t 8 > nul

REM Start static content services
echo Starting static content services (about-us, privacy-policy, terms-condition, contact-us)...
call pm2 start ecosystem.config.js --only about-us-ms,privacy-policy-ms,terms-condition-ms,contact-us-ms --env %ENV%
timeout /t 8 > nul

REM Start remaining services
echo Starting remaining services (social-media, changelogs)...
call pm2 start ecosystem.config.js --only social-media-ms,changelogs-ms --env %ENV%

echo.
echo All microservices started successfully with PM2!
echo.
echo Resource optimization tips:
echo - Use "pm2 monit" to monitor resource usage in real-time
echo - Use "pm2 reload [service-name]" to restart services with zero downtime
echo - Use "pm2 scale [service-name] [number]" to adjust instances if needed
echo - Services are configured to restart automatically at 12-hour intervals
echo.
echo Commands:
echo - "npm run pm2:logs" to view logs
echo - "npm run pm2:status" to check status
echo - "npm run pm2:monitor" to monitor in real-time
echo.

REM Pause so the user can see the output
pause