import cron from 'node-cron';
import mongoose from 'mongoose';
import logger from '../../../shared/services/logger.service';
import Booking, { BookingStatus } from '../models/Booking';
import { BookingNotificationService } from '../services/notification.service';

export class BookingCronService {
  private static instance: BookingCronService;
  private isRunning = false;

  private constructor() {}

  public static getInstance(): BookingCronService {
    if (!BookingCronService.instance) {
      BookingCronService.instance = new BookingCronService();
    }
    return BookingCronService.instance;
  }

  /**
   * Start all cron jobs
   */
  public startCronJobs(): void {
    if (this.isRunning) {
      logger.info('Booking cron jobs are already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting booking cron jobs');

    // Run every 5 minutes to check for expired pending bookings
    cron.schedule('*/5 * * * *', async () => {
      await this.rejectExpiredPendingBookings();
    });

    // Run every 5 minutes to check for completed bookings
    cron.schedule('*/5 * * * *', async () => {
      await this.completeFinishedBookings();
    });

    logger.info('Booking cron jobs started successfully');
  }

  /**
   * Stop all cron jobs
   */
  public stopCronJobs(): void {
    this.isRunning = false;
    logger.info('Booking cron jobs stopped');
  }

  /**
   * Automatically reject pending bookings that have passed their start date
   */
  private async rejectExpiredPendingBookings(): Promise<void> {
    try {
      const now = new Date();
      
      // Find pending bookings where start date has passed
      const expiredBookings = await Booking.find({
        status: BookingStatus.Pending,
        startDate: { $lt: now }
      }).populate('boatId', 'name ownerId');

      logger.info(`Found ${expiredBookings.length} expired pending bookings to reject`);

      for (const booking of expiredBookings) {
        await this.rejectExpiredBooking(booking);
      }
    } catch (error) {
      logger.error('Error rejecting expired pending bookings', {
        service: 'booking-ms',
        error: error
      });
    }
  }

  /**
   * Automatically complete accepted bookings that have passed their end date
   */
  private async completeFinishedBookings(): Promise<void> {
    try {
      const now = new Date();
      
      // Find accepted bookings where end date has passed
      const finishedBookings = await Booking.find({
        status: BookingStatus.Accepted,
        endDate: { $lt: now }
      }).populate('boatId', 'name ownerId');

      logger.info(`Found ${finishedBookings.length} finished bookings to complete`);

      for (const booking of finishedBookings) {
        await this.completeFinishedBooking(booking);
      }
    } catch (error) {
      logger.error('Error completing finished bookings', {
        service: 'booking-ms',
        error: error
      });
    }
  }

  /**
   * Reject an expired pending booking
   */
  private async rejectExpiredBooking(booking: any): Promise<void> {
    try {
      booking.status = BookingStatus.Rejected;
      booking.rejectionReason = 'Booking automatically rejected - start date has passed without owner acceptance';
      booking.updatedAt = new Date();
      
      await booking.save();

      logger.info(`Automatically rejected expired booking ${booking._id}`, {
        service: 'booking-ms',
        bookingId: booking._id,
        startDate: booking.startDate
      });

      // Send notification to user about rejection
      await BookingNotificationService.sendStatusChangeNotification(booking);
    } catch (error) {
      logger.error(`Error rejecting expired booking ${booking._id}`, {
        service: 'booking-ms',
        bookingId: booking._id,
        error: error
      });
    }
  }

  /**
   * Complete a finished booking
   */
  private async completeFinishedBooking(booking: any): Promise<void> {
    try {
      booking.status = BookingStatus.Completed;
      booking.updatedAt = new Date();
      
      await booking.save();

      logger.info(`Automatically completed finished booking ${booking._id}`, {
        service: 'booking-ms',
        bookingId: booking._id,
        endDate: booking.endDate
      });

      // Send notification to user about completion
      await BookingNotificationService.sendStatusChangeNotification(booking);
    } catch (error) {
      logger.error(`Error completing finished booking ${booking._id}`, {
        service: 'booking-ms',
        bookingId: booking._id,
        error: error
      });
    }
  }
}

export default BookingCronService;
