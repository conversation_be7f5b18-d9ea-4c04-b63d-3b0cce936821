#!/usr/bin/env node

/**
 * Test Script for Date Filtering in Boat-MS APIs
 * 
 * This script tests the new date filtering functionality for both
 * boats and activities APIs.
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Adjust as needed
const AUTH_TOKEN = 'your-auth-token-here'; // Replace with actual token

// Test data
const testCases = [
  {
    name: 'Boats - Date Range Filter',
    endpoint: '/v1/boat',
    params: {
      fromDate: '2024-07-01',
      toDate: '2024-07-31',
      limit: 5
    }
  },
  {
    name: 'Activities - Date Range Filter',
    endpoint: '/v1/boat/activities',
    params: {
      fromDate: '2024-07-01',
      toDate: '2024-07-31',
      limit: 5
    }
  },
  {
    name: 'Boats - Combined Filters',
    endpoint: '/v1/boat',
    params: {
      fromDate: '2024-06-01',
      toDate: '2024-12-31',
      type: 'Yacht',
      minPrice: 500,
      maxPrice: 2000,
      location: 'Miami',
      limit: 3
    }
  },
  {
    name: 'Activities - Combined Filters',
    endpoint: '/v1/boat/activities',
    params: {
      fromDate: '2024-06-01',
      toDate: '2024-12-31',
      search: 'diving',
      minPrice: 50,
      maxPrice: 300,
      sortBy: 'price',
      sortOrder: 'asc',
      limit: 3
    }
  },
  {
    name: 'Boats - Location-based Search',
    endpoint: '/v1/boat',
    params: {
      lat: 25.7617,
      lng: -80.1318,
      radius: 20,
      fromDate: '2024-07-01',
      limit: 5
    }
  }
];

async function runTest(testCase) {
  console.log(`\n🧪 Testing: ${testCase.name}`);
  console.log(`📍 Endpoint: ${testCase.endpoint}`);
  console.log(`📋 Parameters:`, testCase.params);
  
  try {
    const response = await axios.get(`${BASE_URL}${testCase.endpoint}`, {
      params: testCase.params,
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`✅ Status: ${response.status}`);
    console.log(`📊 Results: ${response.data.data?.boats?.length || response.data.data?.activities?.length || 0} items`);
    
    if (response.data.data?.pagination) {
      console.log(`📄 Pagination:`, response.data.data.pagination);
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Error: ${error.response?.status || 'Network Error'}`);
    console.log(`💬 Message: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Date Filtering Tests for Boat-MS APIs\n');
  console.log('=' .repeat(60));
  
  let passed = 0;
  let failed = 0;
  
  for (const testCase of testCases) {
    const success = await runTest(testCase);
    if (success) {
      passed++;
    } else {
      failed++;
    }
    console.log('-'.repeat(40));
  }
  
  console.log('\n📈 Test Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Date filtering is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the implementation.');
  }
}

// Validation function for date parameters
function validateDateParams() {
  console.log('\n🔍 Validating Date Parameter Examples:\n');
  
  const examples = [
    { fromDate: '2024-07-01', toDate: '2024-07-31', valid: true },
    { fromDate: '2024-13-01', toDate: '2024-07-31', valid: false },
    { fromDate: '2024-07-31', toDate: '2024-07-01', valid: false },
    { fromDate: 'invalid-date', toDate: '2024-07-31', valid: false }
  ];
  
  examples.forEach((example, index) => {
    console.log(`Example ${index + 1}:`);
    console.log(`  fromDate: ${example.fromDate}`);
    console.log(`  toDate: ${example.toDate}`);
    console.log(`  Expected: ${example.valid ? '✅ Valid' : '❌ Invalid'}`);
    console.log('');
  });
}

// Main execution
if (require.main === module) {
  console.log('📋 Date Filtering Test Suite for Boat-MS');
  console.log('🔧 Make sure to update AUTH_TOKEN and BASE_URL before running\n');
  
  validateDateParams();
  
  if (AUTH_TOKEN === 'your-auth-token-here') {
    console.log('⚠️  Please update AUTH_TOKEN in the script before running tests');
    process.exit(1);
  }
  
  runAllTests().catch(console.error);
}

module.exports = { runTest, runAllTests, validateDateParams };
