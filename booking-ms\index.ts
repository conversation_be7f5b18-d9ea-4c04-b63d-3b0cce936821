import express from "express";
import cors from "cors";
import * as config from "../shared/config.json";
import i18n from "./src/services/i18n.service";
import bookingRoutes from "./src/routes/booking.routes";
import commissionRoutes from "./src/routes/commission.routes";
import connectDB from "../shared/db";
import { authenticateJWT } from "../shared/middleware/auth";
import responseFormatter from "../shared/middleware/responseFormatter.middleware";
import logger from "../shared/services/logger.service";
import HandleErrorMessage from "../shared/middleware/validator";
// Import Boat model to register schema
import "../boat-ms/src/models/Boat";
import BookingCronService from "./src/helpers/bookingCronService";

const app = express();

require("dotenv").config();

const environment = process.env.NODE_ENV! || "dev";
const envConfig: any = JSON.parse(JSON.stringify(config))[environment];

try {
  connectDB();
  logger.info("Database connected successfully", { service: "booking-ms" });
} catch (error: any) {
  logger.error(`Database connection failed: ${error.message}`, {
    service: "booking-ms",
    error,
  });
  process.exit(1);
}

global.config = envConfig;

// Enable CORS for all origins
app.use(cors({ origin: "*" }));

app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(i18n.init);

// Apply response formatter middleware
app.use(responseFormatter);

// Regular API routes - require JWT authentication
app.use("/", authenticateJWT, commissionRoutes);
app.use("/", authenticateJWT, bookingRoutes);

// Celebrate error handler
app.use(HandleErrorMessage);

app.listen(envConfig.services["booking-ms"].PORT, () => {
  logger.info(
    `Booking microservice is running on port ${envConfig.services["booking-ms"].PORT}`,
    {
      service: "booking-ms",
      port: envConfig.services["booking-ms"].PORT,
    },
  );

  // Start booking cron jobs
  BookingCronService.startCronJobs();
});

export default app;
