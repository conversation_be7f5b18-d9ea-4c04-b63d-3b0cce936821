const fs = require('fs');
const path = require('path');

// Common messages that should be in ALL microservices
const REQUIRED_COMMON_MESSAGES = [
  'TOKEN_REQUIRED',
  'FAIL_TOKEN_EXPIRED',
  'USER_NOT_FOUND',
  'ACCOUNT_INACTIVE',
  'SOMETHING_WENT_WRONG',
  'ACCESS_DENIED',
  'FORBIDDEN',
  'INTERNAL_SERVER_ERROR',
  'INVALID_ID',
  'INVALID_PAGINATION_PARAMS',
  'INVALID_FILTER_PARAMS'
];

// All microservices to check
const MICROSERVICES = [
  'auth-ms',
  'user-ms',
  'boat-ms',
  'booking-ms',
  'wishlist-ms',
  'affiliate-ms',
  'notification-ms',
  'newsletter-ms',
  'faq-ms',
  'card-ms',
  'contact-us-ms',
  'privacy-policy-ms',
  'terms-condition-ms',
  'about-us-ms',
  'social-media-ms',
  'payment-ms',
  'wallet-ms',
  'reviews-ms',
  'chat-ms',
  'changelogs-ms',
  'mail-ms'
];

const LOCALES = ['en', 'es', 'de'];

function validateLocalizationFiles() {
  console.log('🔍 VALIDATING LOCALIZATION FILES ACROSS ALL MICROSERVICES\n');
  
  const results = {
    totalMicroservices: MICROSERVICES.length,
    validatedMicroservices: 0,
    missingFiles: [],
    typos: [],
    missingMessages: [],
    summary: {}
  };

  MICROSERVICES.forEach(microservice => {
    console.log(`📁 Checking ${microservice}...`);
    
    const msResults = {
      hasAllFiles: true,
      missingFiles: [],
      typos: [],
      missingMessages: {}
    };

    LOCALES.forEach(locale => {
      const filePath = path.join(microservice, 'src', 'locales', `${locale}.json`);
      
      if (!fs.existsSync(filePath)) {
        msResults.hasAllFiles = false;
        msResults.missingFiles.push(`${locale}.json`);
        results.missingFiles.push(`${microservice}/${locale}.json`);
        return;
      }

      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const messages = JSON.parse(content);

        // Check for typos
        if (messages['TOEKN_REQUIRED']) {
          msResults.typos.push(`${locale}.json: TOEKN_REQUIRED (should be TOKEN_REQUIRED)`);
          results.typos.push(`${microservice}/${locale}.json: TOEKN_REQUIRED`);
        }

        // Check for missing common messages
        const missing = REQUIRED_COMMON_MESSAGES.filter(msg => !messages[msg]);
        if (missing.length > 0) {
          msResults.missingMessages[locale] = missing;
          results.missingMessages.push({
            file: `${microservice}/${locale}.json`,
            missing: missing
          });
        }

      } catch (error) {
        console.log(`  ❌ Error reading ${filePath}: ${error.message}`);
      }
    });

    if (msResults.hasAllFiles && msResults.typos.length === 0 && Object.keys(msResults.missingMessages).length === 0) {
      console.log(`  ✅ ${microservice} - All localization files are complete and valid`);
      results.validatedMicroservices++;
    } else {
      console.log(`  ⚠️  ${microservice} - Issues found:`);
      if (msResults.missingFiles.length > 0) {
        console.log(`    📄 Missing files: ${msResults.missingFiles.join(', ')}`);
      }
      if (msResults.typos.length > 0) {
        console.log(`    🔤 Typos: ${msResults.typos.join(', ')}`);
      }
      if (Object.keys(msResults.missingMessages).length > 0) {
        Object.entries(msResults.missingMessages).forEach(([locale, missing]) => {
          console.log(`    🔍 ${locale}.json missing: ${missing.join(', ')}`);
        });
      }
    }

    results.summary[microservice] = msResults;
  });

  // Print comprehensive summary
  console.log('\n📊 COMPREHENSIVE VALIDATION SUMMARY:');
  console.log('=====================================');
  console.log(`✅ Fully validated microservices: ${results.validatedMicroservices}/${results.totalMicroservices}`);
  console.log(`📄 Missing localization files: ${results.missingFiles.length}`);
  console.log(`🔤 Typos found: ${results.typos.length}`);
  console.log(`🔍 Files with missing messages: ${results.missingMessages.length}`);

  if (results.typos.length > 0) {
    console.log('\n🔤 TYPOS FOUND:');
    results.typos.forEach(typo => console.log(`  - ${typo}`));
  }

  if (results.missingMessages.length > 0) {
    console.log('\n🔍 MISSING COMMON MESSAGES:');
    results.missingMessages.forEach(item => {
      console.log(`  📁 ${item.file}:`);
      item.missing.forEach(msg => console.log(`    - ${msg}`));
    });
  }

  const completionPercentage = Math.round((results.validatedMicroservices / results.totalMicroservices) * 100);
  console.log(`\n🎯 COMPLETION STATUS: ${completionPercentage}% of microservices have complete localization`);

  return results;
}

// Run validation
validateLocalizationFiles();
