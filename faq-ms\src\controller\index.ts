import { StatusCodes } from "http-status-codes";
import FAQ, { FAQStatus } from "../models/FAQ";
import logger from "../../../shared/services/logger.service";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";

/**
 * Create a new FAQ
 * @param req Request
 * @param res Response
 */
export const createFAQ = async (req: any, res: any): Promise<any> => {
  try {
    const { question, answer, order, status } = req.body;
    const userId = req.user._id;

    const faq = new FAQ({
      question,
      answer,
      order,
      status: status || FAQStatus.Draft,
    });

    await faq.save();

    // Log the creation
    const changes = validateAndGetChanges({}, faq.toJSON());
    await createActivityLog("faqs", faq._id, "CREATE", changes, userId);

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: res.__("FAQ_CREATED"),
      data: faq,
    });
  } catch (error: unknown) {
    logger.error(
      `Error creating FAQ: ${error instanceof Error ? error.message : "Unknown error"}`,
      {
        service: "faq-ms",
        error,
      },
    );

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: error instanceof Error ? error.message : "Unknown error occurred",
    });
  }
};

/**
 * Get all FAQs
 * @param req Request
 * @param res Response
 */
export const getAllFAQs = async (req: any, res: any): Promise<any> => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filter by status if provided
    if (status) {
      query.status = status;
    } else {
      // By default, exclude deleted FAQs
      query.status = { $ne: FAQStatus.Deleted };
    }

    const faqs = await FAQ.find(query)
      .sort({ order: 1 })
      .skip(skip)
      .limit(Number(limit));

    const totalCount = await FAQ.countDocuments(query);
    const totalPages = Math.ceil(totalCount / Number(limit));

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("FAQS_FETCHED"),
      data: {
        faqs,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount,
          totalPages,
        },
      },
    });
  } catch (error: unknown) {
    logger.error(
      `Error getting FAQs: ${error instanceof Error ? error.message : "Unknown error"}`,
      {
        service: "faq-ms",
        error,
      },
    );

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: error instanceof Error ? error.message : "Unknown error occurred",
    });
  }
};

/**
 * Get FAQ by ID
 * @param req Request
 * @param res Response
 */
export const getFAQById = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;

    const faq = await FAQ.findOne({
      _id: id,
      status: { $ne: FAQStatus.Deleted },
    });

    if (!faq) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("FAQ_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("FAQ_FETCHED"),
      data: faq,
    });
  } catch (error: unknown) {
    logger.error(
      `Error getting FAQ by ID: ${error instanceof Error ? error.message : "Unknown error"}`,
      {
        service: "faq-ms",
        error,
      },
    );

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: error instanceof Error ? error.message : "Unknown error occurred",
    });
  }
};

/**
 * Update FAQ
 * @param req Request
 * @param res Response
 */
export const updateFAQ = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const { question, answer, order, status } = req.body;
    const userId = req.user._id;

    const faq = await FAQ.findOne({
      _id: id,
      status: { $ne: FAQStatus.Deleted },
    });

    if (!faq) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("FAQ_NOT_FOUND"),
      });
    }

    // Get changes before update
    const oldData = faq.toJSON();

    // Update fields if provided
    if (question !== undefined) faq.question = question;
    if (answer !== undefined) faq.answer = answer;
    if (order !== undefined) faq.order = order;
    if (status !== undefined) faq.status = status;

    await faq.save();

    // Log the changes
    const changes = validateAndGetChanges(oldData, faq.toJSON());
    if (changes.length > 0) {
      await createActivityLog("faqs", id, "UPDATE", changes, userId);
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("FAQ_UPDATED"),
      data: faq,
    });
  } catch (error: unknown) {
    logger.error(
      `Error updating FAQ: ${error instanceof Error ? error.message : "Unknown error"}`,
      {
        service: "faq-ms",
        error,
      },
    );

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: error instanceof Error ? error.message : "Unknown error occurred",
    });
  }
};

/**
 * Delete FAQ (Soft delete)
 * @param req Request
 * @param res Response
 */
export const deleteFAQ = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    const faq = await FAQ.findOne({
      _id: id,
      status: { $ne: FAQStatus.Deleted },
    });

    if (!faq) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("FAQ_NOT_FOUND"),
      });
    }

    // Get changes before update
    const oldData = faq.toJSON();

    // Soft delete by updating status
    faq.status = FAQStatus.Deleted;
    await faq.save();

    // Log the deletion
    const changes = validateAndGetChanges(oldData, faq.toJSON());
    await createActivityLog("faqs", id, "DELETE", changes, userId);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("FAQ_DELETED"),
    });
  } catch (error: unknown) {
    logger.error(
      `Error deleting FAQ: ${error instanceof Error ? error.message : "Unknown error"}`,
      {
        service: "faq-ms",
        error,
      },
    );

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: error instanceof Error ? error.message : "Unknown error occurred",
    });
  }
};
