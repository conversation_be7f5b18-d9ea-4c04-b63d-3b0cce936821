import cron from 'node-cron';
import mongoose from 'mongoose';
import logger from '../../../shared/services/logger.service';
import Booking, { BookingStatus } from '../../../booking-ms/src/models/Booking';
import Payment, { PaymentStatus, PaymentMethod } from '../models/Payment';
import StripeService from '../services/stripe.service';
import Wallet from '../../../wallet-ms/src/models/Wallet';
import Transaction, { 
  TransactionStatus, 
  TransactionType, 
  TransactionSource 
} from '../../../wallet-ms/src/models/Transaction';
import User from '../../../user-ms/src/models/User';

export class PaymentCronService {
  private static instance: PaymentCronService;
  private isRunning = false;

  private constructor() {}

  public static getInstance(): PaymentCronService {
    if (!PaymentCronService.instance) {
      PaymentCronService.instance = new PaymentCronService();
    }
    return PaymentCronService.instance;
  }

  /**
   * Start all cron jobs
   */
  public startCronJobs(): void {
    if (this.isRunning) {
      logger.info('Payment cron jobs are already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting payment cron jobs');

    // Run every 5 minutes to check for bookings that need payment capture
    cron.schedule('*/5 * * * *', async () => {
      await this.processBookingPayments();
    });

    // Run every 10 minutes to check for completed bookings that need transfers
    cron.schedule('*/10 * * * *', async () => {
      await this.processOwnerTransfers();
    });

    logger.info('Payment cron jobs started successfully');
  }

  /**
   * Stop all cron jobs
   */
  public stopCronJobs(): void {
    this.isRunning = false;
    logger.info('Payment cron jobs stopped');
  }

  /**
   * Process bookings that have reached start time and capture payments
   */
  private async processBookingPayments(): Promise<void> {
    try {
      const now = new Date();
      
      // Find bookings that:
      // 1. Have payment intent but not captured
      // 2. Start time has arrived (within 5 minutes)
      // 3. Are not cancelled
      const bookingsToProcess = await Booking.find({
        paymentIntentId: { $exists: true, $ne: null },
        paymentIntentStatus: { $ne: 'succeeded' },
        status: { $in: [BookingStatus.ReadyForPayment, BookingStatus.Accepted] },
        startDate: { 
          $lte: new Date(now.getTime() + 5 * 60 * 1000) // Within 5 minutes
        }
      }).populate('boatId', 'name ownerId');

      logger.info(`Found ${bookingsToProcess.length} bookings to process for payment capture`);

      for (const booking of bookingsToProcess) {
        await this.captureBookingPayment(booking);
      }
    } catch (error) {
      logger.error('Error processing booking payments', {
        service: 'payment-ms',
        error: error
      });
    }
  }

  /**
   * Process completed bookings and transfer payments to boat owners
   */
  private async processOwnerTransfers(): Promise<void> {
    try {
      const now = new Date();
      const transferTime = new Date(now.getTime() - 10 * 60 * 1000); // 10 minutes ago
      
      // Find bookings that:
      // 1. Have been completed (end date + 10 minutes has passed)
      // 2. Payment was captured successfully
      // 3. Transfer hasn't been processed yet
      const bookingsToTransfer = await Booking.find({
        paymentIntentStatus: 'succeeded',
        status: { $ne: BookingStatus.Cancelled },
        endDate: { $lte: transferTime },
        // Add a field to track if transfer was processed
        transferProcessed: { $ne: true }
      }).populate('boatId', 'name ownerId')
        .populate('paymentId');

      logger.info(`Found ${bookingsToTransfer.length} bookings to process for owner transfer`);

      for (const booking of bookingsToTransfer) {
        await this.transferToBoatOwner(booking);
      }
    } catch (error) {
      logger.error('Error processing owner transfers', {
        service: 'payment-ms',
        error: error
      });
    }
  }

  /**
   * Capture payment for a specific booking
   */
  private async captureBookingPayment(booking: any): Promise<void> {
    try {
      logger.info(`Capturing payment for booking ${booking._id}`, {
        service: 'payment-ms',
        bookingId: booking._id,
        paymentIntentId: booking.paymentIntentId
      });

      // Capture the payment intent
      const captureResult = await StripeService.capturePaymentIntent(
        booking.paymentIntentId,
        booking.totalAmount
      );

      if (captureResult.success) {
        // Update booking status
        await Booking.findByIdAndUpdate(booking._id, {
          paymentIntentStatus: 'succeeded',
          status: BookingStatus.Accepted
        });

        // Extract payment metadata if available
        let pointsUsed = 0;
        let welcomeDiscountApplied = false;
        let originalAmount = booking.totalAmount;
        let discountAmount = 0;

        try {
          // Try to get payment intent details to extract metadata
          const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
          const paymentIntent = await stripe.paymentIntents.retrieve(booking.paymentIntentId);

          if (paymentIntent.metadata) {
            pointsUsed = parseInt(paymentIntent.metadata.usePoints || '0');
            welcomeDiscountApplied = paymentIntent.metadata.useWelcomeDiscount === 'true';
            originalAmount = parseFloat(paymentIntent.metadata.originalAmount || booking.totalAmount.toString());
            discountAmount = parseFloat(paymentIntent.metadata.discountAmount || '0');
          }
        } catch (error) {
          logger.warn(`Could not retrieve payment intent metadata for booking ${booking._id}`, {
            service: 'payment-ms',
            error: error
          });
        }

        // Create payment record
        const payment = new Payment({
          bookingId: booking._id,
          userId: booking.userId,
          boatOwnerId: booking.boatId.ownerId,
          amount: booking.totalAmount,
          adminFee: booking.adminFee,
          ownerAmount: booking.netAmount,
          status: PaymentStatus.COMPLETED,
          paymentMethod: PaymentMethod.CARD,
          paymentReference: booking.paymentIntentId,
          currency: booking.currency,
          ownerCurrency: booking.ownerCurrency,
          pointsUsed: pointsUsed,
          welcomeDiscountApplied: welcomeDiscountApplied,
          originalAmount: originalAmount,
          discountAmount: discountAmount
        });

        await payment.save();

        // Update booking with payment ID
        await Booking.findByIdAndUpdate(booking._id, {
          paymentId: payment._id
        });

        logger.info(`Payment captured successfully for booking ${booking._id}`, {
          service: 'payment-ms',
          bookingId: booking._id,
          paymentId: payment._id,
          amount: booking.totalAmount
        });
      } else {
        logger.error(`Failed to capture payment for booking ${booking._id}`, {
          service: 'payment-ms',
          bookingId: booking._id,
          error: captureResult.error
        });

        // Update booking status to indicate payment failure
        await Booking.findByIdAndUpdate(booking._id, {
          status: BookingStatus.Cancelled,
          rejectionReason: `Payment capture failed: ${captureResult.error}`
        });
      }
    } catch (error) {
      logger.error(`Error capturing payment for booking ${booking._id}`, {
        service: 'payment-ms',
        bookingId: booking._id,
        error: error
      });
    }
  }

  /**
   * Transfer payment to boat owner
   */
  private async transferToBoatOwner(booking: any): Promise<void> {
    try {
      logger.info(`Processing transfer to boat owner for booking ${booking._id}`, {
        service: 'payment-ms',
        bookingId: booking._id,
        ownerId: booking.boatId.ownerId,
        amount: booking.netAmount
      });

      // Get boat owner details
      const boatOwner = await User.findById(booking.boatId.ownerId);
      if (!boatOwner) {
        logger.error(`Boat owner not found for booking ${booking._id}`);
        return;
      }

      // Get or create boat owner's wallet
      let wallet = await Wallet.findOne({ userId: booking.boatId.ownerId });
      if (!wallet) {
        wallet = new Wallet({
          userId: booking.boatId.ownerId,
          balance: 0,
          currency: booking.ownerCurrency || 'USD'
        });
        await wallet.save();
      }

      // Create transaction for the transfer
      const transaction = new Transaction({
        userId: booking.boatId.ownerId,
        amount: booking.netAmount,
        type: TransactionType.CREDIT,
        source: TransactionSource.BOOKING_PAYMENT,
        status: TransactionStatus.COMPLETED,
        description: `Payment received for booking ${booking._id}`,
        referenceId: booking._id,
        currency: booking.ownerCurrency || 'USD'
      });

      await transaction.save();

      // Update wallet balance
      await Wallet.findByIdAndUpdate(wallet._id, {
        $inc: { balance: booking.netAmount }
      });

      // Mark booking as transfer processed
      await Booking.findByIdAndUpdate(booking._id, {
        transferProcessed: true
      });

      logger.info(`Transfer completed successfully for booking ${booking._id}`, {
        service: 'payment-ms',
        bookingId: booking._id,
        ownerId: booking.boatId.ownerId,
        amount: booking.netAmount,
        transactionId: transaction._id
      });
    } catch (error) {
      logger.error(`Error transferring to boat owner for booking ${booking._id}`, {
        service: 'payment-ms',
        bookingId: booking._id,
        error: error
      });
    }
  }
}

export default PaymentCronService.getInstance();
