import mongoose, { Document, Schema } from "mongoose";
import { UserCurrency } from "../../../user-ms/src/models/User";

export enum WithdrawalStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  REJECTED = "rejected",
  FAILED = "failed",
}

export interface IWithdrawal extends Document {
  userId: mongoose.Types.ObjectId;
  amount: number;
  bankHolderName: string;
  bankName: string;
  accountNumber: string;
  routingNumber?: string;
  currency: string;
  status: WithdrawalStatus;
  transactionId?: mongoose.Types.ObjectId;
  externalReferenceId?: string;
  rejectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

const WithdrawalSchema = new Schema<IWithdrawal>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 1,
    },
    bankHolderName: {
      type: String,
      required: true,
    },
    bankName: {
      type: String,
      required: true,
    },
    accountNumber: {
      type: String,
      required: true,
    },
    routingNumber: {
      type: String,
      required: false,
    },
    currency: {
      type: String,
      enum: Object.values(UserCurrency),
      default: UserCurrency.USD,
      required: true,
      description: "Currency for the withdrawal",
    },
    status: {
      type: String,
      enum: Object.values(WithdrawalStatus),
      default: WithdrawalStatus.PENDING,
    },
    transactionId: {
      type: Schema.Types.ObjectId,
      ref: "Transaction",
      required: false,
    },
    externalReferenceId: {
      type: String,
      required: false,
    },
    rejectionReason: {
      type: String,
      required: false,
    },
  },
  { timestamps: true },
);

// Add indexes for faster queries
WithdrawalSchema.index({ userId: 1 });
WithdrawalSchema.index({ status: 1 });
WithdrawalSchema.index({ createdAt: -1 });

const Withdrawal = mongoose.model<IWithdrawal>("Withdrawal", WithdrawalSchema);
export default Withdrawal;
