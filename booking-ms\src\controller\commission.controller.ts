import { StatusCodes } from "http-status-codes";
import CommissionRate, { CommissionRateStatus } from "../models/CommissionRate";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";
import logger from "../../../shared/services/logger.service";

/**
 * Create or update commission rates
 * If a rate already exists, it will be updated, otherwise a new one will be created
 */
export const createOrUpdateCommissionRate = async (req: any, res: any) => {
  try {
    const { adminFee, affiliateRate } = req.body;
    const userId = req.user._id;

    // Check if there's an existing active commission rate
    const existingRate = await CommissionRate.findOne({
      status: CommissionRateStatus.ACTIVE,
    });

    let response;
    let action;

    if (existingRate) {
      // Update existing rate
      const previousState = existingRate.toJSON();

      existingRate.adminFee = adminFee;
      existingRate.affiliateRate = affiliateRate;
      existingRate.updatedBy = userId;

      await existingRate.save();

      // Log the changes
      const changes = validateAndGetChanges(
        previousState,
        existingRate.toJSON(),
      );

      if (changes.length > 0) {
        await createActivityLog(
          "commission_rates",
          existingRate._id,
          "UPDATE",
          changes,
          userId,
        );
      }

      response = {
        success: true,
        message: req.__("COMMISSION_RATE_UPDATED_SUCCESS"),
        data: existingRate,
      };
      action = "Updated";
    } else {
      // Create new rate
      const newRate = new CommissionRate({
        adminFee,
        affiliateRate,
        createdBy: userId,
      });

      await newRate.save();

      // Log the creation
      const changes = validateAndGetChanges({}, newRate.toJSON());
      await createActivityLog(
        "commission_rates",
        newRate._id,
        "CREATE",
        changes,
        userId,
      );

      response = {
        success: true,
        message: req.__("COMMISSION_RATE_CREATED_SUCCESS"),
        data: newRate,
      };
      action = "Created";
    }

    logger.info(`Commission rate ${action.toLowerCase()} by user ${userId}`, {
      service: "booking-ms",
      adminFee,
      affiliateRate,
      userId,
    });

    return res.status(StatusCodes.OK).json(response);
  } catch (error) {
    logger.error("Error in createOrUpdateCommissionRate", {
      service: "booking-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Get active commission fees
 */
export const getCommissionFees = async (req: any, res: any) => {
  try {
    const commissionRate = await CommissionRate.findOne({
      status: CommissionRateStatus.ACTIVE,
    });

    if (!commissionRate) {
      // Return default values if no commission rate is set
      return res.status(StatusCodes.OK).json({
        success: true,
        message: req.__("COMMISSION_RATE_NOT_FOUND"),
        data: {
          adminFee: 10, // Default admin fee (10%)
          affiliateRate: 5, // Default affiliate rate (5%)
        },
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("COMMISSION_RATE_FETCH_SUCCESS"),
      data: commissionRate,
    });
  } catch (error) {
    logger.error("Error in getCommissionFees", {
      service: "booking-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
