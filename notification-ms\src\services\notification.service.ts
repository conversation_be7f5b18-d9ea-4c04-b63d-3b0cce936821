import sendPushNotification from "../../../shared/services/sendNotification.service";
import Notification, { INotification } from "../models/notification.model";
import {
  NOTIFICATION_MESSAGES,
  NOTIFICATION_TYPES,
} from "../constant/notificationConstant";
import User from "../../../user-ms/src/models/User";
import mongoose, { PipelineStage } from "mongoose";

// Define a more specific type for notification messages
interface NotificationMessage {
  header: string;
  content: string | ((param?: any) => string);
}

export class NotificationService {
  static async createAndSendNotification(
    toUser: string,
    fromUser: string | undefined,
    type: keyof typeof NOTIFICATION_TYPES,
    extraData: any = {},
    deviceId?: string,
  ): Promise<INotification> {
    const message = NOTIFICATION_MESSAGES[type] as NotificationMessage;

    const user = await User.findById(toUser);

    const fromUserData = await User.findById(fromUser);
    if (!user) {
      throw new Error("User not found");
    }
    deviceId = user?.deviceToken;
    
    // Handle content properly based on whether it's a function or string
    const content = typeof message.content === 'function' 
      ? message.content(fromUserData?.username) 
      : message.content;
    
    // Create notification in database
    const notification = await Notification.create({
      header: message.header,
      content,
      toUser,
      fromUser,
      extraData,
      isRead: false,
    });

    // Send push notification if FCM token is provided
    if (deviceId) {
      try {
        const pushResult = await sendPushNotification(
          deviceId,
          content,
          { ...extraData, notificationId: notification._id },
          type,
          message.header,
        );

        // Log the result for debugging
        if (pushResult.success) {
          console.log(`Push notification sent successfully to ${pushResult.successCount} device(s)`);
          if (pushResult.failureCount > 0) {
            console.warn(`Failed to send to ${pushResult.failureCount} device(s):`, pushResult.failedTokens);
          }
        } else {
          console.error("Failed to send push notification:", pushResult.error);
        }
      } catch (error) {
        console.error("Error sending push notification:", error);
      }
    }

    return notification;
  }

  static async getNotifications(
    userId: string,
    page: number = 1,
    limit: number = 10,
  ) {
    try {
      // Validate userId format
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error("Invalid user ID format");
      }

      const skip = (page - 1) * limit;

      // Use aggregation to get notifications with enhanced user profile and online status
      const pipeline: PipelineStage[] = [
        { $match: { toUser: new mongoose.Types.ObjectId(userId) } },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: limit },
        {
          $lookup: {
            from: "users",
            localField: "fromUser",
            foreignField: "_id",
            as: "fromUserData"
          }
        },
        {
          $lookup: {
            from: "files",
            localField: "fromUserData.avatar",
            foreignField: "_id",
            as: "fromUserAvatar"
          }
        },
        {
          $unwind: { path: "$fromUserData", preserveNullAndEmptyArrays: true }
        },
        {
          $unwind: { path: "$fromUserAvatar", preserveNullAndEmptyArrays: true }
        },
        {
          $addFields: {
            fromUserProfile: {
              $cond: {
                if: "$fromUserData",
                then: {
                  id: "$fromUserData._id",
                  username: "$fromUserData.username",
                  email: "$fromUserData.email",
                  avatar: {
                    $cond: {
                      if: "$fromUserAvatar",
                      then: { $concat: [global.config.FILE_BASE_URL || "", "$fromUserAvatar.location"] },
                      else: null
                    }
                  },
                  isOnline: {
                    $cond: {
                      if: "$fromUserData.deviceToken",
                      then: true,
                      else: false
                    }
                  },
                  status: "$fromUserData.status"
                },
                else: null
              }
            }
          }
        },
        {
          $project: {
            _id: 1,
            header: 1,
            content: 1,
            fromUserProfile: 1,
            extraData: 1,
            isRead: 1,
            createdAt: 1,
            updatedAt: 1
          }
        }
      ];

      const notifications = await Notification.aggregate(pipeline as any);
      const total = await Notification.countDocuments({ toUser: new mongoose.Types.ObjectId(userId) });

      return {
        notifications,
        pagination: {
          page,
          size: limit,
          totalCount: total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error: any) {
      console.error("Error in getNotifications:", error);
      throw error;
    }
  }

  static async markAsRead(
    notificationId: string,
    userId: string,
  ): Promise<INotification | null> {
    try {
      // Validate IDs format
      if (!mongoose.Types.ObjectId.isValid(notificationId)) {
        throw new Error("Invalid notification ID format");
      }
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error("Invalid user ID format");
      }

      return await Notification.findOneAndUpdate(
        { _id: new mongoose.Types.ObjectId(notificationId), toUser: new mongoose.Types.ObjectId(userId) },
        { isRead: true },
        { new: true },
      );
    } catch (error: any) {
      console.error("Error in markAsRead:", error);
      throw error;
    }
  }

  static async markAllAsRead(userId: string): Promise<void> {
    try {
      // Validate userId format
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error("Invalid user ID format");
      }

      await Notification.updateMany(
        { toUser: new mongoose.Types.ObjectId(userId), isRead: false },
        { isRead: true },
      );
    } catch (error: any) {
      console.error("Error in markAllAsRead:", error);
      throw error;
    }
  }
}
