import { celebrate, Joi, Segments } from "celebrate";
import { FacilityStatus } from "../models/Facility";

export const facilityCreateBodyValidator = Joi.object().keys({
  name: Joi.string().required(),
  price: Joi.number().required(),
  images: Joi.array().items(Joi.string()).optional(),
  status: Joi.string()
    .valid(...Object.values(FacilityStatus))
    .optional(),
});

export const createFacilityValidator = celebrate({
  [Segments.BODY]: facilityCreateBodyValidator,
});

export const facilityUpdateBodyValidator = Joi.object().keys({
  name: Joi.string().optional(),
  price: Joi.number().optional(),
  images: Joi.array().items(Joi.string()).optional(),
  status: Joi.string()
    .valid(...Object.values(FacilityStatus))
    .optional(),
});

export const updateFacilityValidator = celebrate({
  [Segments.BODY]: facilityUpdateBodyValidator,
});

export const idParamValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string().required(),
  }),
});

export const uploadImageValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string().required(),
  }),
});
