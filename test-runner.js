#!/usr/bin/env node

/**
 * Comprehensive API Test Runner for Sea Escape Backend
 * Tests ALL endpoints across ALL microservices
 * Run with: node test-runner.js
 */

const axios = require('axios');
const colors = require('colors');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
let AUTH_TOKEN = '';
let ADMIN_TOKEN = '';
let TEST_RESULTS = [];
let TEST_DATA = {
  userId: '',
  boatId: '',
  bookingId: '',
  affiliateId: '',
  cardId: '',
  notificationId: '',
  facilityId: '',
  placeId: '',
  activityId: '',
  faqId: '',
  newsletterId: '',
  reviewId: '',
  documentId: ''
};

// Test utilities
const log = {
  success: (msg) => console.log(`✅ ${msg}`.green),
  error: (msg) => console.log(`❌ ${msg}`.red),
  info: (msg) => console.log(`ℹ️  ${msg}`.blue),
  warning: (msg) => console.log(`⚠️  ${msg}`.yellow),
  section: (msg) => console.log(`\n🔷 ${msg}`.cyan.bold)
};

// HTTP client with error handling
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 15000,
  validateStatus: () => true // Don't throw on HTTP errors
});

// Test result tracker
function recordTest(name, passed, details = '') {
  TEST_RESULTS.push({ name, passed, details });
  if (passed) {
    log.success(`${name}`);
  } else {
    log.error(`${name} - ${details}`);
  }
}

// Helper to create test file for uploads
function createTestFile(filename = 'test-image.jpg') {
  const testFilePath = path.join(__dirname, filename);
  if (!fs.existsSync(testFilePath)) {
    // Create a minimal test file
    fs.writeFileSync(testFilePath, 'test file content for upload testing');
  }
  return testFilePath;
}

// ==================== AUTHENTICATION TESTS ====================

async function testAuthentication() {
  log.section('AUTHENTICATION MICROSERVICE TESTS');

  // Test user registration
  try {
    const registerResponse = await api.post('/v1/auth/register', {
      email: '<EMAIL>',
      password: 'password123',
      username: 'testuser',
      currency: 'USD',
      language: 'en'
    });
    recordTest('Auth - Register User', registerResponse.status === 201 || registerResponse.status === 200,
      registerResponse.status === 200 ? '' : `Status: ${registerResponse.status}`);
  } catch (error) {
    recordTest('Auth - Register User', false, error.message);
  }

  // Test user login
  try {
    const loginResponse = await api.post('/v1/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.status === 200 && loginResponse.data.data?.token) {
      AUTH_TOKEN = loginResponse.data.data.token;
      TEST_DATA.userId = loginResponse.data.data.user?._id || '';
      api.defaults.headers.common['Authorization'] = `Bearer ${AUTH_TOKEN}`;
      recordTest('Auth - Login User', true);
    } else {
      recordTest('Auth - Login User', false, `Status: ${loginResponse.status}`);
      return false;
    }
  } catch (error) {
    recordTest('Auth - Login User', false, error.message);
    return false;
  }

  // Test forgot password
  try {
    const forgotResponse = await api.post('/v1/auth/forgot-password', {
      email: '<EMAIL>'
    });
    recordTest('Auth - Forgot Password', forgotResponse.status === 200,
      forgotResponse.status === 200 ? '' : `Status: ${forgotResponse.status}`);
  } catch (error) {
    recordTest('Auth - Forgot Password', false, error.message);
  }

  // Test send OTP
  try {
    const otpResponse = await api.post('/v1/auth/send-otp', {
      email: '<EMAIL>'
    });
    recordTest('Auth - Send OTP', otpResponse.status === 200,
      otpResponse.status === 200 ? '' : `Status: ${otpResponse.status}`);
  } catch (error) {
    recordTest('Auth - Send OTP', false, error.message);
  }

  // Test refresh token
  try {
    const refreshResponse = await api.post('/v1/auth/refresh-token', {
      refreshToken: 'dummy_refresh_token'
    });
    recordTest('Auth - Refresh Token', refreshResponse.status === 200 || refreshResponse.status === 401,
      'Expected response (may fail with invalid token)');
  } catch (error) {
    recordTest('Auth - Refresh Token', false, error.message);
  }

  return true;
}

// ==================== USER MICROSERVICE TESTS ====================

async function testUserMicroservice() {
  log.section('USER MICROSERVICE TESTS');

  // Test get user profile
  try {
    const profileResponse = await api.get('/v1/user/profile');
    if (profileResponse.status === 200) {
      const user = profileResponse.data.data;
      const hasRequiredFields = user._id && user.email && user.username;
      recordTest('User - Get Profile', hasRequiredFields,
        hasRequiredFields ? '' : 'Missing required fields');
    } else {
      recordTest('User - Get Profile', false, `Status: ${profileResponse.status}`);
    }
  } catch (error) {
    recordTest('User - Get Profile', false, error.message);
  }

  // Test update profile
  try {
    const updateResponse = await api.put('/v1/user/profile', {
      username: 'updated_testuser',
      currency: 'EUR'
    });
    recordTest('User - Update Profile', updateResponse.status === 200,
      updateResponse.status === 200 ? '' : `Status: ${updateResponse.status}`);
  } catch (error) {
    recordTest('User - Update Profile', false, error.message);
  }

  // Test change password
  try {
    const passwordResponse = await api.post('/v1/user/change-password', {
      currentPassword: 'password123',
      newPassword: 'newpassword123'
    });
    recordTest('User - Change Password', passwordResponse.status === 200,
      passwordResponse.status === 200 ? '' : `Status: ${passwordResponse.status}`);
  } catch (error) {
    recordTest('User - Change Password', false, error.message);
  }

  // Test switch role
  try {
    const roleResponse = await api.put('/v1/user/switch-role', {
      role: 'owner'
    });
    recordTest('User - Switch Role', roleResponse.status === 200,
      roleResponse.status === 200 ? '' : `Status: ${roleResponse.status}`);
  } catch (error) {
    recordTest('User - Switch Role', false, error.message);
  }

  // Test update device ID
  try {
    const deviceResponse = await api.put('/v1/user/update-device-id', {
      deviceToken: 'test_device_token_123',
      deviceType: 'ios'
    });
    recordTest('User - Update Device ID', deviceResponse.status === 200,
      deviceResponse.status === 200 ? '' : `Status: ${deviceResponse.status}`);
  } catch (error) {
    recordTest('User - Update Device ID', false, error.message);
  }

  // Test get user rewards
  try {
    const rewardsResponse = await api.get('/v1/user/rewards');
    if (rewardsResponse.status === 200) {
      const rewards = rewardsResponse.data.data;
      const hasRewardsStructure = rewards.hasOwnProperty('points') &&
                                 rewards.hasOwnProperty('pointsHistory');
      recordTest('User - Get Rewards', hasRewardsStructure,
        hasRewardsStructure ? '' : 'Missing rewards structure');
    } else {
      recordTest('User - Get Rewards', false, `Status: ${rewardsResponse.status}`);
    }
  } catch (error) {
    recordTest('User - Get Rewards', false, error.message);
  }

  // Test user documents upload
  try {
    const testFile = createTestFile('test-document.pdf');
    const formData = new FormData();
    formData.append('documents', fs.createReadStream(testFile));

    const uploadResponse = await api.post('/v1/user/documents/upload', formData, {
      headers: formData.getHeaders()
    });

    if (uploadResponse.status === 200 && uploadResponse.data.data?.documents) {
      TEST_DATA.documentId = uploadResponse.data.data.documents[0]?._id || '';
    }
    recordTest('User - Upload Documents', uploadResponse.status === 200,
      uploadResponse.status === 200 ? '' : `Status: ${uploadResponse.status}`);
  } catch (error) {
    recordTest('User - Upload Documents', false, error.message);
  }

  // Test get user documents
  try {
    const documentsResponse = await api.get('/v1/user/documents');
    recordTest('User - Get Documents', documentsResponse.status === 200,
      documentsResponse.status === 200 ? '' : `Status: ${documentsResponse.status}`);
  } catch (error) {
    recordTest('User - Get Documents', false, error.message);
  }

  // Test logout
  try {
    const logoutResponse = await api.post('/v1/user/logout');
    recordTest('User - Logout', logoutResponse.status === 200,
      logoutResponse.status === 200 ? '' : `Status: ${logoutResponse.status}`);
  } catch (error) {
    recordTest('User - Logout', false, error.message);
  }
}

// ==================== BOAT MICROSERVICE TESTS ====================

async function testBoatMicroservice() {
  log.section('BOAT MICROSERVICE TESTS');

  // Test create boat with file upload
  try {
    const testFile = createTestFile('boat-image.jpg');
    const formData = new FormData();
    formData.append('name', 'Test Boat');
    formData.append('type', 'Yacht');
    formData.append('location', 'Miami Beach');
    formData.append('lat', '25.7617');
    formData.append('lng', '-80.1918');
    formData.append('price', '500');
    formData.append('capacity', '8');
    formData.append('description', 'A beautiful test boat');
    formData.append('images', fs.createReadStream(testFile));

    const createResponse = await api.post('/v1/boat', formData, {
      headers: formData.getHeaders()
    });

    if (createResponse.status === 201 && createResponse.data.data?._id) {
      TEST_DATA.boatId = createResponse.data.data._id;
    }
    recordTest('Boat - Create Boat', createResponse.status === 201,
      createResponse.status === 201 ? '' : `Status: ${createResponse.status}`);
  } catch (error) {
    recordTest('Boat - Create Boat', false, error.message);
  }

  // Test get all boats
  try {
    const boatsResponse = await api.get('/v1/boat?page=1&limit=10');
    if (boatsResponse.status === 200) {
      const data = boatsResponse.data.data;
      const hasCorrectStructure = data.boats && Array.isArray(data.boats) &&
                                 data.totalBoats !== undefined;

      if (hasCorrectStructure && data.boats.length > 0) {
        const boat = data.boats[0];
        const hasRequiredFields = boat._id && boat.name &&
                                 boat.hasOwnProperty('lat') &&
                                 boat.hasOwnProperty('lng') &&
                                 boat.hasOwnProperty('isBooked') &&
                                 boat.hasOwnProperty('isWishlisted') &&
                                 boat.hasOwnProperty('averageRating') &&
                                 boat.ownerInfo;

        recordTest('Boat - Get All Boats', hasRequiredFields,
          hasRequiredFields ? '' : 'Missing required boat fields');

        if (!TEST_DATA.boatId && boat._id) {
          TEST_DATA.boatId = boat._id;
        }
      } else {
        recordTest('Boat - Get All Boats', hasCorrectStructure,
          hasCorrectStructure ? 'No boats found' : 'Incorrect response structure');
      }
    } else {
      recordTest('Boat - Get All Boats', false, `Status: ${boatsResponse.status}`);
    }
  } catch (error) {
    recordTest('Boat - Get All Boats', false, error.message);
  }

  // Test search boats
  try {
    const searchResponse = await api.get('/v1/boat?search=yacht&page=1&limit=5');
    recordTest('Boat - Search Boats', searchResponse.status === 200,
      searchResponse.status === 200 ? '' : `Status: ${searchResponse.status}`);
  } catch (error) {
    recordTest('Boat - Search Boats', false, error.message);
  }

  // Test filter boats by date
  try {
    const filterResponse = await api.get('/v1/boat?fromDate=2024-01-01&toDate=2024-12-31');
    recordTest('Boat - Filter by Date', filterResponse.status === 200,
      filterResponse.status === 200 ? '' : `Status: ${filterResponse.status}`);
  } catch (error) {
    recordTest('Boat - Filter by Date', false, error.message);
  }

  // Test get my boats
  try {
    const myBoatsResponse = await api.get('/v1/boat/my-boats?page=1&limit=10');
    recordTest('Boat - Get My Boats', myBoatsResponse.status === 200,
      myBoatsResponse.status === 200 ? '' : `Status: ${myBoatsResponse.status}`);
  } catch (error) {
    recordTest('Boat - Get My Boats', false, error.message);
  }

  // Test get my activities
  try {
    const activitiesResponse = await api.get('/v1/boat/my-activities');
    recordTest('Boat - Get My Activities', activitiesResponse.status === 200,
      activitiesResponse.status === 200 ? '' : `Status: ${activitiesResponse.status}`);
  } catch (error) {
    recordTest('Boat - Get My Activities', false, error.message);
  }

  // Test get boat types
  try {
    const typesResponse = await api.get('/v1/boat/types');
    recordTest('Boat - Get Boat Types', typesResponse.status === 200,
      typesResponse.status === 200 ? '' : `Status: ${typesResponse.status}`);
  } catch (error) {
    recordTest('Boat - Get Boat Types', false, error.message);
  }

  // Test get popular destinations
  try {
    const destinationsResponse = await api.get('/v1/boat/popular-destinations?limit=5');
    if (destinationsResponse.status === 200) {
      const destinations = destinationsResponse.data.data;
      const isValidStructure = Array.isArray(destinations);
      recordTest('Boat - Get Popular Destinations', isValidStructure,
        isValidStructure ? '' : 'Invalid destinations structure');
    } else {
      recordTest('Boat - Get Popular Destinations', false, `Status: ${destinationsResponse.status}`);
    }
  } catch (error) {
    recordTest('Boat - Get Popular Destinations', false, error.message);
  }

  // Test get nearby boats
  try {
    const nearbyResponse = await api.get('/v1/boat/nearby?lat=40.7128&lng=-74.0060&radius=50');
    if (nearbyResponse.status === 200) {
      const data = nearbyResponse.data.data;
      const hasGeoData = data.searchCenter &&
                        data.searchCenter.lat &&
                        data.searchCenter.lng;
      recordTest('Boat - Get Nearby Boats', hasGeoData,
        hasGeoData ? '' : 'Missing geolocation data');
    } else {
      recordTest('Boat - Get Nearby Boats', false, `Status: ${nearbyResponse.status}`);
    }
  } catch (error) {
    recordTest('Boat - Get Nearby Boats', false, error.message);
  }

  // Test get single boat
  if (TEST_DATA.boatId) {
    try {
      const boatResponse = await api.get(`/v1/boat/${TEST_DATA.boatId}`);
      if (boatResponse.status === 200) {
        const boat = boatResponse.data.data;
        const hasCompleteData = boat.attachments && boat.facilities &&
                               boat.recommendedPlaces && boat.ownerInfo;
        recordTest('Boat - Get Single Boat', hasCompleteData,
          hasCompleteData ? '' : 'Missing complete boat data');
      } else {
        recordTest('Boat - Get Single Boat', false, `Status: ${boatResponse.status}`);
      }
    } catch (error) {
      recordTest('Boat - Get Single Boat', false, error.message);
    }
  }
}

// ==================== BOOKING MICROSERVICE TESTS ====================

async function testBookingMicroservice() {
  log.section('BOOKING MICROSERVICE TESTS');

  // Test create booking
  if (TEST_DATA.boatId) {
    try {
      const bookingResponse = await api.post('/v1/booking', {
        boatId: TEST_DATA.boatId,
        startDate: '2024-06-01',
        endDate: '2024-06-03',
        totalPrice: 1500,
        guests: 4,
        specialRequests: 'Test booking request'
      });

      if (bookingResponse.status === 201 && bookingResponse.data.data?._id) {
        TEST_DATA.bookingId = bookingResponse.data.data._id;
      }
      recordTest('Booking - Create Booking', bookingResponse.status === 201,
        bookingResponse.status === 201 ? '' : `Status: ${bookingResponse.status}`);
    } catch (error) {
      recordTest('Booking - Create Booking', false, error.message);
    }
  }

  // Test get my bookings
  try {
    const myBookingsResponse = await api.get('/v1/booking/me?page=1&limit=10');
    recordTest('Booking - Get My Bookings', myBookingsResponse.status === 200,
      myBookingsResponse.status === 200 ? '' : `Status: ${myBookingsResponse.status}`);
  } catch (error) {
    recordTest('Booking - Get My Bookings', false, error.message);
  }

  // Test get boat owner bookings
  try {
    const ownerBookingsResponse = await api.get('/v1/booking/boat-owner?page=1&limit=10');
    recordTest('Booking - Get Boat Owner Bookings', ownerBookingsResponse.status === 200,
      ownerBookingsResponse.status === 200 ? '' : `Status: ${ownerBookingsResponse.status}`);
  } catch (error) {
    recordTest('Booking - Get Boat Owner Bookings', false, error.message);
  }

  // Test get booking by ID
  if (TEST_DATA.bookingId) {
    try {
      const bookingDetailResponse = await api.get(`/v1/booking/${TEST_DATA.bookingId}`);
      recordTest('Booking - Get Booking by ID', bookingDetailResponse.status === 200,
        bookingDetailResponse.status === 200 ? '' : `Status: ${bookingDetailResponse.status}`);
    } catch (error) {
      recordTest('Booking - Get Booking by ID', false, error.message);
    }
  }

  // Test commission fees
  try {
    const commissionResponse = await api.get('/v1/booking/commission-fees');
    recordTest('Booking - Get Commission Fees', commissionResponse.status === 200,
      commissionResponse.status === 200 ? '' : `Status: ${commissionResponse.status}`);
  } catch (error) {
    recordTest('Booking - Get Commission Fees', false, error.message);
  }
}

// ==================== WISHLIST MICROSERVICE TESTS ====================

async function testWishlistMicroservice() {
  log.section('WISHLIST MICROSERVICE TESTS');

  // Test add to wishlist
  if (TEST_DATA.boatId) {
    try {
      const addResponse = await api.post('/v1/wishlist', {
        boatId: TEST_DATA.boatId,
        type: 'Boat'
      });
      recordTest('Wishlist - Add to Wishlist', addResponse.status === 200 || addResponse.status === 201,
        addResponse.status === 200 ? '' : `Status: ${addResponse.status}`);
    } catch (error) {
      recordTest('Wishlist - Add to Wishlist', false, error.message);
    }
  }

  // Test get wishlist
  try {
    const wishlistResponse = await api.get('/v1/wishlist?page=1&limit=10');
    if (wishlistResponse.status === 200) {
      const data = wishlistResponse.data.data;
      const hasCorrectStructure = data.wishlisted !== undefined &&
                                 data.pagination !== undefined;
      recordTest('Wishlist - Get Wishlist', hasCorrectStructure,
        hasCorrectStructure ? '' : 'Missing wishlist structure');
    } else {
      recordTest('Wishlist - Get Wishlist', false, `Status: ${wishlistResponse.status}`);
    }
  } catch (error) {
    recordTest('Wishlist - Get Wishlist', false, error.message);
  }

  // Test check wishlist status
  if (TEST_DATA.boatId) {
    try {
      const statusResponse = await api.get(`/v1/wishlist/status/${TEST_DATA.boatId}`);
      recordTest('Wishlist - Check Status', statusResponse.status === 200,
        statusResponse.status === 200 ? '' : `Status: ${statusResponse.status}`);
    } catch (error) {
      recordTest('Wishlist - Check Status', false, error.message);
    }
  }

  // Test remove from wishlist
  if (TEST_DATA.boatId) {
    try {
      const removeResponse = await api.delete(`/v1/wishlist/${TEST_DATA.boatId}`);
      recordTest('Wishlist - Remove from Wishlist', removeResponse.status === 200,
        removeResponse.status === 200 ? '' : `Status: ${removeResponse.status}`);
    } catch (error) {
      recordTest('Wishlist - Remove from Wishlist', false, error.message);
    }
  }
}

// ==================== AFFILIATE MICROSERVICE TESTS ====================

async function testAffiliateMicroservice() {
  log.section('AFFILIATE MICROSERVICE TESTS');

  // Test create affiliate registration
  try {
    const affiliateResponse = await api.post('/v1/affiliate/register', {
      name: 'Test Affiliate',
      email: '<EMAIL>',
      phoneNo: '+*********0',
      accountHolderName: 'Test Affiliate',
      accountNumber: '*********0',
      bankName: 'Test Bank',
      routingNumber: '*********',
      paypalEmail: '<EMAIL>'
    });

    if (affiliateResponse.status === 201 && affiliateResponse.data.data?._id) {
      TEST_DATA.affiliateId = affiliateResponse.data.data._id;
    }
    recordTest('Affiliate - Register Affiliate', affiliateResponse.status === 201,
      affiliateResponse.status === 201 ? '' : `Status: ${affiliateResponse.status}`);
  } catch (error) {
    recordTest('Affiliate - Register Affiliate', false, error.message);
  }

  // Test get my affiliate status
  try {
    const statusResponse = await api.get('/v1/affiliate/me');
    recordTest('Affiliate - Get My Status', statusResponse.status === 200,
      statusResponse.status === 200 ? '' : `Status: ${statusResponse.status}`);
  } catch (error) {
    recordTest('Affiliate - Get My Status', false, error.message);
  }

  // Test get my affiliated boats
  try {
    const boatsResponse = await api.get('/v1/affiliate/me/boats?page=1&limit=10');
    recordTest('Affiliate - Get My Boats', boatsResponse.status === 200,
      boatsResponse.status === 200 ? '' : `Status: ${boatsResponse.status}`);
  } catch (error) {
    recordTest('Affiliate - Get My Boats', false, error.message);
  }

  // Test get my affiliate bookings
  try {
    const bookingsResponse = await api.get('/v1/affiliate/me/bookings?page=1&limit=10');
    recordTest('Affiliate - Get My Bookings', bookingsResponse.status === 200,
      bookingsResponse.status === 200 ? '' : `Status: ${bookingsResponse.status}`);
  } catch (error) {
    recordTest('Affiliate - Get My Bookings', false, error.message);
  }

  // Test validate referral name
  try {
    const validateResponse = await api.post('/v1/affiliate/validate-referral', {
      referralName: 'test-referral'
    });
    recordTest('Affiliate - Validate Referral', validateResponse.status === 200,
      validateResponse.status === 200 ? '' : `Status: ${validateResponse.status}`);
  } catch (error) {
    recordTest('Affiliate - Validate Referral', false, error.message);
  }
}

// ==================== NOTIFICATION MICROSERVICE TESTS ====================

async function testNotificationMicroservice() {
  log.section('NOTIFICATION MICROSERVICE TESTS');

  // Test get notifications
  try {
    const notificationsResponse = await api.get('/v1/notifications/list?page=1&limit=10');
    if (notificationsResponse.status === 200) {
      const data = notificationsResponse.data.data;
      if (data.notifications && data.notifications.length > 0) {
        TEST_DATA.notificationId = data.notifications[0]._id;
      }
    }
    recordTest('Notification - Get Notifications', notificationsResponse.status === 200,
      notificationsResponse.status === 200 ? '' : `Status: ${notificationsResponse.status}`);
  } catch (error) {
    recordTest('Notification - Get Notifications', false, error.message);
  }

  // Test mark notification as read
  if (TEST_DATA.notificationId) {
    try {
      const markReadResponse = await api.put(`/v1/notifications/${TEST_DATA.notificationId}/read`);
      recordTest('Notification - Mark as Read', markReadResponse.status === 200,
        markReadResponse.status === 200 ? '' : `Status: ${markReadResponse.status}`);
    } catch (error) {
      recordTest('Notification - Mark as Read', false, error.message);
    }
  }

  // Test mark all notifications as read
  try {
    const markAllReadResponse = await api.put('/v1/notifications/read-all');
    recordTest('Notification - Mark All as Read', markAllReadResponse.status === 200,
      markAllReadResponse.status === 200 ? '' : `Status: ${markAllReadResponse.status}`);
  } catch (error) {
    recordTest('Notification - Mark All as Read', false, error.message);
  }
}

// ==================== ADDITIONAL MICROSERVICE TESTS ====================

async function testAdditionalMicroservices() {
  log.section('ADDITIONAL MICROSERVICES TESTS');

  // Test Payment Microservice
  try {
    const clientTokenResponse = await api.get('/v1/payment/client-token');
    recordTest('Payment - Get Client Token', clientTokenResponse.status === 200,
      clientTokenResponse.status === 200 ? '' : `Status: ${clientTokenResponse.status}`);
  } catch (error) {
    recordTest('Payment - Get Client Token', false, error.message);
  }

  // Test Wallet Microservice
  try {
    const balanceResponse = await api.get('/v1/wallet/balance');
    recordTest('Wallet - Get Balance', balanceResponse.status === 200,
      balanceResponse.status === 200 ? '' : `Status: ${balanceResponse.status}`);

    const transactionsResponse = await api.get('/v1/wallet/transactions');
    recordTest('Wallet - Get Transactions', transactionsResponse.status === 200,
      transactionsResponse.status === 200 ? '' : `Status: ${transactionsResponse.status}`);
  } catch (error) {
    recordTest('Wallet - Tests', false, error.message);
  }

  // Test Reviews Microservice
  try {
    const reviewsResponse = await api.get('/v1/reviews?page=1&limit=10');
    recordTest('Reviews - Get Reviews', reviewsResponse.status === 200,
      reviewsResponse.status === 200 ? '' : `Status: ${reviewsResponse.status}`);
  } catch (error) {
    recordTest('Reviews - Get Reviews', false, error.message);
  }

  // Test FAQ Microservice
  try {
    const faqsResponse = await api.get('/v1/faqs');
    recordTest('FAQ - Get FAQs', faqsResponse.status === 200,
      faqsResponse.status === 200 ? '' : `Status: ${faqsResponse.status}`);
  } catch (error) {
    recordTest('FAQ - Get FAQs', false, error.message);
  }

  // Test Contact Us Microservice
  try {
    const contactResponse = await api.post('/v1/contact-us', {
      name: 'Test User',
      email: '<EMAIL>',
      subject: 'Test Subject',
      message: 'Test message'
    });
    recordTest('Contact Us - Submit Form', contactResponse.status === 200 || contactResponse.status === 201,
      contactResponse.status === 200 ? '' : `Status: ${contactResponse.status}`);
  } catch (error) {
    recordTest('Contact Us - Submit Form', false, error.message);
  }

  // Test About Us Microservice
  try {
    const aboutResponse = await api.get('/v1/about-us');
    recordTest('About Us - Get About Us', aboutResponse.status === 200,
      aboutResponse.status === 200 ? '' : `Status: ${aboutResponse.status}`);
  } catch (error) {
    recordTest('About Us - Get About Us', false, error.message);
  }

  // Test Privacy Policy Microservice
  try {
    const privacyResponse = await api.get('/v1/privacy-policy');
    recordTest('Privacy Policy - Get Privacy Policy', privacyResponse.status === 200,
      privacyResponse.status === 200 ? '' : `Status: ${privacyResponse.status}`);
  } catch (error) {
    recordTest('Privacy Policy - Get Privacy Policy', false, error.message);
  }

  // Test Terms & Conditions Microservice
  try {
    const termsResponse = await api.get('/v1/terms-condition');
    recordTest('Terms & Conditions - Get Terms', termsResponse.status === 200,
      termsResponse.status === 200 ? '' : `Status: ${termsResponse.status}`);
  } catch (error) {
    recordTest('Terms & Conditions - Get Terms', false, error.message);
  }

  // Test Social Media Microservice
  try {
    const socialResponse = await api.get('/v1/social-media');
    recordTest('Social Media - Get Social Media', socialResponse.status === 200,
      socialResponse.status === 200 ? '' : `Status: ${socialResponse.status}`);
  } catch (error) {
    recordTest('Social Media - Get Social Media', false, error.message);
  }
}

// ==================== MAIN TEST RUNNER ====================

async function runAllTests() {
  console.log('🚀 Starting Comprehensive Sea Escape Backend API Tests...\n'.cyan.bold);
  console.log('Testing ALL endpoints across ALL microservices\n'.yellow);

  // Test authentication first
  const authSuccess = await testAuthentication();
  if (!authSuccess) {
    log.error('Authentication failed. Cannot proceed with other tests.');
    return;
  }

  // Run all microservice tests
  await testUserMicroservice();
  await testBoatMicroservice();
  await testBookingMicroservice();
  await testWishlistMicroservice();
  await testAffiliateMicroservice();
  await testNotificationMicroservice();
  await testAdditionalMicroservices();

  // Print comprehensive summary
  console.log('\n📊 COMPREHENSIVE TEST RESULTS SUMMARY:'.cyan.bold);
  const passed = TEST_RESULTS.filter(t => t.passed).length;
  const total = TEST_RESULTS.length;

  console.log(`✅ Passed: ${passed}/${total} (${Math.round((passed/total)*100)}%)`.green);
  console.log(`❌ Failed: ${total - passed}/${total} (${Math.round(((total-passed)/total)*100)}%)`.red);

  // Group results by microservice
  const microservices = {};
  TEST_RESULTS.forEach(test => {
    const service = test.name.split(' - ')[0];
    if (!microservices[service]) {
      microservices[service] = { passed: 0, total: 0 };
    }
    microservices[service].total++;
    if (test.passed) microservices[service].passed++;
  });

  console.log('\n📋 Results by Microservice:'.cyan.bold);
  Object.entries(microservices).forEach(([service, stats]) => {
    const percentage = Math.round((stats.passed / stats.total) * 100);
    const status = percentage === 100 ? '✅' : percentage >= 80 ? '⚠️' : '❌';
    console.log(`${status} ${service}: ${stats.passed}/${stats.total} (${percentage}%)`);
  });

  if (passed === total) {
    console.log('\n🎉 ALL TESTS PASSED! Sea Escape Backend is working perfectly!'.green.bold);
  } else {
    console.log('\n⚠️  Some tests failed. Check the details above.'.yellow.bold);
    console.log('\nFailed tests:'.red);
    TEST_RESULTS.filter(t => !t.passed).forEach(test => {
      console.log(`  - ${test.name}: ${test.details}`.red);
    });
  }

  console.log(`\n🔍 Total Endpoints Tested: ${total}`.blue);
  console.log(`📊 Success Rate: ${Math.round((passed/total)*100)}%`.blue);
}

// Cleanup function to remove test files
function cleanup() {
  try {
    const testFiles = ['test-image.jpg', 'test-document.pdf', 'boat-image.jpg'];
    testFiles.forEach(file => {
      const filePath = path.join(__dirname, file);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    });
  } catch (error) {
    log.warning('Could not clean up test files');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests()
    .then(() => {
      cleanup();
      process.exit(0);
    })
    .catch(error => {
      console.error('Test runner failed:', error);
      cleanup();
      process.exit(1);
    });
}

module.exports = { runAllTests };
