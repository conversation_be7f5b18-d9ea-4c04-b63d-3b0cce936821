import mongoose, { Schema, Document } from "mongoose";

export enum FAQStatus {
  Active = "active",
  Draft = "draft",
  Deleted = "deleted",
}

export interface IFAQ extends Document {
  question: string;
  answer: string;
  order: number;
  status: FAQStatus;
  createdAt: Date;
  updatedAt: Date;
}

const faqSchema = new Schema(
  {
    question: {
      type: String,
      required: true,
      trim: true,
    },
    answer: {
      type: String,
      required: true,
      trim: true,
    },
    order: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      enum: Object.values(FAQStatus),
      default: FAQStatus.Draft,
    },
  },
  {
    timestamps: true,
  },
);

export default mongoose.model<IFAQ>("FAQs", faqSchema);
