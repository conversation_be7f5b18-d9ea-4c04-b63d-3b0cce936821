import Stripe from "stripe";
import logger from "../../../shared/services/logger.service";

class StripeService {
  private stripe: Stripe;

  constructor() {
    // Initialize Stripe with secret key
    this.stripe = new Stripe(global.config.STRIPE_SECRET_KEY || "sk_test_...", {
      apiVersion: "2023-10-16",
    });
  }

  /**
   * Create a Stripe customer
   */
  async createCustomer(customerData: {
    email: string;
    name?: string;
    metadata?: Record<string, string>;
  }): Promise<{
    success: boolean;
    customer?: Stripe.Customer;
    error?: string;
  }> {
    try {
      const customer = await this.stripe.customers.create({
        email: customerData.email,
        name: customerData.name,
        metadata: customerData.metadata,
      });

      return {
        success: true,
        customer,
      };
    } catch (error: any) {
      logger.error("Stripe customer creation failed", {
        service: "card-ms",
        email: customerData.email,
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Customer creation failed",
      };
    }
  }

  /**
   * Create a source using user's Stripe merchant ID and card token
   */
  async createSourceFromToken(
    cardToken: string,
    customerId: string
  ): Promise<{
    isValid: boolean;
    sourceId?: string;
    lastFourDigits?: string;
    cardType?: string;
    error?: string;
  }> {
    try {
      // Create a source from the card token and attach to customer
      const source = await this.stripe.sources.create({
        type: "card",
        token: cardToken,
        customer: customerId,
      });

      return {
        isValid: true,
        sourceId: source.id,
        lastFourDigits: source.card?.last4 || undefined,
        cardType: source.card?.brand || undefined,
      };
    } catch (error: any) {
      logger.error("Stripe source creation failed", {
        service: "card-ms",
        cardToken,
        customerId,
        error: error.message,
      });

      return {
        isValid: false,
        error: error.message || "Source creation failed",
      };
    }
  }

  /**
   * Validate a card using card token from client (fallback method)
   */
  async validateCardWithToken(cardToken: string): Promise<{
    isValid: boolean;
    paymentMethodId?: string;
    lastFourDigits?: string;
    cardType?: string;
    error?: string;
  }> {
    try {
      // Create a payment method from the card token
      const paymentMethod = await this.stripe.paymentMethods.create({
        type: "card",
        card: {
          token: cardToken,
        },
      });

      return {
        isValid: true,
        paymentMethodId: paymentMethod.id,
        lastFourDigits: paymentMethod.card?.last4,
        cardType: paymentMethod.card?.brand,
      };
    } catch (error: any) {
      logger.error("Stripe card token validation failed", {
        service: "card-ms",
        cardToken,
        error: error.message,
      });

      return {
        isValid: false,
        error: error.message || "Card token validation failed",
      };
    }
  }

  /**
   * Validate a card by creating a payment method directly from card details
   * (Alternative method for direct card processing)
   */
  async validateCard(cardData: {
    number: string;
    expiry: string;
    cvv: string;
    name: string;
    email: string;
  }): Promise<{
    isValid: boolean;
    paymentMethodId?: string;
    lastFourDigits?: string;
    cardType?: string;
    error?: string;
  }> {
    try {
      const [expiryMonth, expiryYear] = cardData.expiry.split("/");

      // Create a payment method with the card details
      const paymentMethod = await this.stripe.paymentMethods.create({
        type: "card",
        card: {
          number: cardData.number,
          exp_month: parseInt(expiryMonth),
          exp_year: parseInt(`20${expiryYear}`),
          cvc: cardData.cvv,
        },
        billing_details: {
          name: cardData.name,
          email: cardData.email,
        },
      });

      return {
        isValid: true,
        paymentMethodId: paymentMethod.id,
        lastFourDigits: paymentMethod.card?.last4,
        cardType: paymentMethod.card?.brand,
      };
    } catch (error: any) {
      logger.error("Stripe card validation failed", {
        service: "card-ms",
        error: error.message,
      });

      return {
        isValid: false,
        error: error.message || "Card validation failed",
      };
    }
  }



  /**
   * Attach a payment method to a customer
   */
  async attachPaymentMethodToCustomer(
    paymentMethodId: string,
    customerId: string,
  ): Promise<{
    success: boolean;
    paymentMethod?: Stripe.PaymentMethod;
    error?: string;
  }> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      return {
        success: true,
        paymentMethod,
      };
    } catch (error: any) {
      logger.error("Failed to attach payment method to customer", {
        service: "card-ms",
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to attach payment method",
      };
    }
  }

  /**
   * Detach a payment method from a customer
   */
  async detachPaymentMethod(paymentMethodId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      await this.stripe.paymentMethods.detach(paymentMethodId);

      return {
        success: true,
      };
    } catch (error: any) {
      logger.error("Failed to detach payment method", {
        service: "card-ms",
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to detach payment method",
      };
    }
  }

  /**
   * Get payment methods for a customer
   */
  async getCustomerPaymentMethods(customerId: string): Promise<{
    success: boolean;
    paymentMethods?: Stripe.PaymentMethod[];
    error?: string;
  }> {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: "card",
      });

      return {
        success: true,
        paymentMethods: paymentMethods.data,
      };
    } catch (error: any) {
      logger.error("Failed to get customer payment methods", {
        service: "card-ms",
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to get payment methods",
      };
    }
  }

  /**
   * Get a specific payment method
   */
  async getPaymentMethod(paymentMethodId: string): Promise<{
    success: boolean;
    paymentMethod?: Stripe.PaymentMethod;
    error?: string;
  }> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.retrieve(paymentMethodId);

      return {
        success: true,
        paymentMethod,
      };
    } catch (error: any) {
      logger.error("Failed to get payment method", {
        service: "card-ms",
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to get payment method",
      };
    }
  }

  /**
   * Delete a source from customer
   */
  async deleteSource(customerId: string, sourceId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Delete the source from the customer
      await this.stripe.customers.deleteSource(customerId, sourceId);

      return {
        success: true,
      };
    } catch (error: any) {
      logger.error("Failed to delete source", {
        service: "card-ms",
        customerId,
        sourceId,
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to delete source",
      };
    }
  }

  /**
   * Delete a payment method (kept for backward compatibility)
   */
  async deletePaymentMethod(paymentMethodId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // First detach the payment method from any customer
      await this.stripe.paymentMethods.detach(paymentMethodId);

      return {
        success: true,
      };
    } catch (error: any) {
      logger.error("Failed to delete payment method", {
        service: "card-ms",
        paymentMethodId,
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to delete payment method",
      };
    }
  }
}

export default new StripeService();
