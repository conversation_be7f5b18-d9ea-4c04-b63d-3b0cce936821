{"dev": {"db": "mongodb://sea_escape:sea_escape!321@**************:27017/sea-escape-prod?authSource=admin&authMechanism=DEFAULT", "PORT": 3000, "FILE_BASE_URL": "http://localhost:3000/uploads/", "JWT_SECRET": "myjwtsecret", "JWT_EXIPIRATION_TIME": "1d", "JWT_REFRESH_SECRET": "myjwtrefreshtoken", "JWT_REFRESH_EXIPIRATION_TIME": "7d", "IS_EMAIL_USE_SMTP": "on", "OTP_EXPIRE_TIME": 5, "services": {"auth-ms": {"PORT": 3100}, "user-ms": {"PORT": 3101}, "boat-ms": {"PORT": 3102}, "wishlist-ms": {"PORT": 3103}, "affiliate-ms": {"PORT": 3104}, "booking-ms": {"PORT": 3105}, "notification-ms": {"PORT": 3106}, "newsletter-ms": {"PORT": 3107}, "faq-ms": {"PORT": 3108}, "card-ms": {"PORT": 3109}, "contact-us-ms": {"PORT": 3110}, "privacy-policy-ms": {"PORT": 3111}, "terms-condition-ms": {"PORT": 3112}, "about-us-ms": {"PORT": 3113}, "social-media-ms": {"PORT": 3114}, "payment-ms": {"PORT": 3115}, "wallet-ms": {"PORT": 3116}, "reviews-ms": {"PORT": 3117}, "changelogs-ms": {"PORT": 3118}, "mail-ms": {"PORT": 3119}, "chat-ms": {"PORT": 3120}}, "SMTP_EMAIL": "<EMAIL>", "SMTP_USER": "apikey", "SMTP_PASSWORD": "*********************************************************************", "SMTP_HOST": "smtp.sendgrid.net", "SMTP_PORT": 587, "AWS_ACCESS_KEY_ID": "", "AWS_SECRET_ACCESS_KEY": "", "AWS_REGION": "", "AWS_S3_BUCKET": "", "ONESIGNAL_APP_ID": "", "ONESIGNAL_API_KEY": "", "FIREBASE_PROJECT_ID": "", "FIREBASE_PRIVATE_KEY": "", "FIREBASE_CLIENT_EMAIL": "", "STRIPE_PUBLISHABLE_KEY": "pk_test_...", "STRIPE_SECRET_KEY": "sk_test_..."}, "test": {"db": "mongodb://localhost:27017/sea-escape-test", "PORT": 3001, "FILE_BASE_URL": "http://localhost:3001/uploads/", "JWT_SECRET": "myjwtsecret", "JWT_EXIPIRATION_TIME": "1d", "JWT_REFRESH_SECRET": "myjwtrefreshtoken", "JWT_REFRESH_EXIPIRATION_TIME": "7d", "IS_EMAIL_USE_SMTP": "on", "OTP_EXPIRE_TIME": 5, "services": {"auth-ms": {"PORT": 3010}, "user-ms": {"PORT": 3011}, "boat-ms": {"PORT": 3012}, "wishlist-ms": {"PORT": 3013}, "affiliate-ms": {"PORT": 3014}, "booking-ms": {"PORT": 3015}, "notification-ms": {"PORT": 3016}, "newsletter-ms": {"PORT": 3017}, "faq-ms": {"PORT": 3018}, "card-ms": {"PORT": 3019}, "contact-us-ms": {"PORT": 3020}, "privacy-policy-ms": {"PORT": 3021}, "terms-condition-ms": {"PORT": 3022}, "about-us-ms": {"PORT": 3023}, "social-media-ms": {"PORT": 3024}, "payment-ms": {"PORT": 3025}, "wallet-ms": {"PORT": 3026}, "reviews-ms": {"PORT": 3027}, "changelogs-ms": {"PORT": 3028}, "mail-ms": {"PORT": 3029}, "chat-ms": {"PORT": 3030}}, "SMTP_EMAIL": "<EMAIL>", "SMTP_USER": "apikey", "SMTP_PASSWORD": "*********************************************************************", "SMTP_HOST": "smtp.sendgrid.net", "SMTP_PORT": 587, "AWS_ACCESS_KEY_ID": "", "AWS_SECRET_ACCESS_KEY": "", "AWS_REGION": "", "AWS_S3_BUCKET": "", "ONESIGNAL_APP_ID": "", "ONESIGNAL_API_KEY": "", "FIREBASE_PROJECT_ID": "", "FIREBASE_PRIVATE_KEY": "", "FIREBASE_CLIENT_EMAIL": "", "STRIPE_PUBLISHABLE_KEY": "pk_test_...", "STRIPE_SECRET_KEY": "sk_test_..."}, "production": {"db": "mongodb://sea_escape:sea_escape!321@127.0.0.1:27017/sea-escape-prod?authSource=admin&authMechanism=DEFAULT", "PORT": 3002, "FILE_BASE_URL": "http://api.seaxplore.co/uploads/", "JWT_SECRET": "myjwtsecret", "JWT_EXIPIRATION_TIME": "1d", "JWT_REFRESH_SECRET": "myjwtsecret", "JWT_REFRESH_EXIPIRATION_TIME": "7d", "IS_EMAIL_USE_SMTP": "on", "OTP_EXPIRE_TIME": 5, "services": {"auth-ms": {"PORT": 3201}, "user-ms": {"PORT": 3202}, "boat-ms": {"PORT": 3203}, "wishlist-ms": {"PORT": 3204}, "affiliate-ms": {"PORT": 3205}, "booking-ms": {"PORT": 3206}, "notification-ms": {"PORT": 3207}, "newsletter-ms": {"PORT": 3208}, "faq-ms": {"PORT": 3209}, "card-ms": {"PORT": 3210}, "contact-us-ms": {"PORT": 3211}, "privacy-policy-ms": {"PORT": 3212}, "terms-condition-ms": {"PORT": 3213}, "about-us-ms": {"PORT": 3214}, "social-media-ms": {"PORT": 3215}, "payment-ms": {"PORT": 3216}, "wallet-ms": {"PORT": 3217}, "reviews-ms": {"PORT": 3218}, "changelogs-ms": {"PORT": 3219}, "mail-ms": {"PORT": 3220}, "chat-ms": {"PORT": 3221}}, "SMTP_EMAIL": "<EMAIL>", "SMTP_USER": "apikey", "SMTP_PASSWORD": "*********************************************************************", "SMTP_HOST": "smtp.sendgrid.net", "SMTP_PORT": 587, "AWS_ACCESS_KEY_ID": "", "AWS_SECRET_ACCESS_KEY": "", "AWS_REGION": "", "AWS_S3_BUCKET": "", "ONESIGNAL_APP_ID": "", "ONESIGNAL_API_KEY": "", "FIREBASE_PROJECT_ID": "", "FIREBASE_PRIVATE_KEY": "", "FIREBASE_CLIENT_EMAIL": "", "STRIPE_PUBLISHABLE_KEY": "pk_live_51RVx46Ha5XgBaA5wb6yL7el7WwOfJqnecXDF9Th3e7p7eIUFqGiPKIQAXC7WVJwmgKZDP9g3HYMdwIUfNS1brZgF00kaJ5CFYE", "STRIPE_SECRET_KEY": "sk_test_51RVx4FH1zEPRCrbdERIFFMM4DBLB62enfIzPG6a3qeLukBpT0oZ2WaPtvkFwAlCfgQORQf2OydzvaxLPym3MSwLZ00Jqu2WZ1N"}}