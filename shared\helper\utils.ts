import passwordHash from "pbkdf2-password-hash";
import moment from "moment";

const generateOtp = async (n: number) => {
  const val =
    Math.floor(Math.random() * (9 * Math.pow(10, n - 1))) + Math.pow(10, n - 1);
  return val;
  // return 1234
};

const otpExpire = () => {
  const time = moment(new Date())
    .add(global.config.OTP_EXPIRE_TIME, "minutes")
    .format("YYYY-MM-DD HH:mm:ss");
  return time;
};
const encrypt = async (password: string) => {
  return await passwordHash.hash(password, {
    iterations: 100,
    digest: "sha1",
    keylen: 16,
    saltlen: 16,
  });
};

const otpExpTime = async () => {
  const time = moment(new Date())
    .add(global.config.OTP_EXPIRE_TIME, "minutes")
    .format("YYYY-MM-DD HH:mm:ss");
  return time;
};

const comparePassword = async (
  plainPassword: string,
  hashedPassword: string,
) => {
  return await passwordHash.compare(plainPassword, hashedPassword);
};

const getPagination = (page: number, size: number) => {
  const limit = size;
  const Page = page || 1;
  const offset = (Page - 1) * limit;
  return { limit, offset };
};

const parseJSON = (data: any, fallback: any) => {
  try {
    return data ? JSON.parse(data) : fallback;
  } catch (error) {
    console.error("JSON Parsing Error:", error);
    return fallback;
  }
};

const arrayParse = (data: any, fallback: any) => {
  try {
    // If data is already an array, return it as is
    if (Array.isArray(data)) {
      return data;
    }

    // If data is null, undefined, or empty string, return fallback
    if (data === null || data === undefined || data === '') {
      return fallback;
    }

    // If data is a string, try to parse it as JSON
    if (typeof data === 'string') {
      const parsed = JSON.parse(data);
      // Ensure the parsed result is an array
      if (Array.isArray(parsed)) {
        return parsed;
      }
      // If parsed result is not an array, return fallback
      return fallback;
    }

    // For any other data type, return fallback
    return fallback;
  } catch (error) {
    // If JSON parsing fails or any other error occurs, return fallback
    return fallback;
  }
}

export {
  generateOtp,
  encrypt,
  otpExpTime,
  comparePassword,
  getPagination,
  otpExpire,
  parseJSON,
  arrayParse
};
