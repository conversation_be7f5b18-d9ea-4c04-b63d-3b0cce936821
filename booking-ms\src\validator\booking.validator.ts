import { Joi, Segments, celebrate } from "celebrate";
import { BookingStatus, PatronType } from "../models/Booking";

const bookingSchema = {
  boatId: Joi.string().required(),
  startDate: Joi.date().iso().required(),
  endDate: Joi.date().iso().min(Joi.ref("startDate")).required(),
  location: Joi.string().required(),
  patronType: Joi.string()
    .valid(...Object.values(PatronType))
    .optional(), // Made optional for activities
  extraFacilities: Joi.array().items(
    Joi.object({
      facilityId: Joi.string().required(),
      quantity: Joi.number().integer().min(1).default(1),
    }),
  ).optional(),
  referralName: Joi.string().allow("", null),
};

export const createBookingValidator = celebrate({
  [Segments.BODY]: Joi.object(bookingSchema),
});

export const updateBookingValidator = celebrate({
  [Segments.PARAMS]: {
    id: Joi.string().required(),
  },
  [Segments.BODY]: Joi.object({
    startDate: Joi.date().iso(),
    endDate: Joi.date().iso().min(Joi.ref("startDate")),
    location: Joi.string(),
    patronType: Joi.string().valid(...Object.values(PatronType)),
    extraFacilities: Joi.array().items(
      Joi.object({
        facilityId: Joi.string().required(),
        quantity: Joi.number().integer().min(1),
      }),
    ),
  }).min(1),
});

export const getBookingValidator = celebrate({
  [Segments.PARAMS]: {
    id: Joi.string().required(),
  },
});

export const rejectBookingValidator = celebrate({
  [Segments.PARAMS]: {
    id: Joi.string().required(),
  },
  [Segments.BODY]: Joi.object({
    reason: Joi.string().required(),
  }),
});

export const updateStatusValidator = celebrate({
  [Segments.PARAMS]: {
    id: Joi.string().required(),
  },
  [Segments.BODY]: Joi.object({
    status: Joi.string()
      .valid(...Object.values(BookingStatus))
      .required()
      .messages({
        "string.base": "Status must be a string",
        "any.required": "Status is required",
        "any.only": `Status must be one of: ${Object.values(BookingStatus).join(
          ", ",
        )}`,
      }),
    paymentId: Joi.string().when("status", {
      is: BookingStatus.Accepted,
      then: Joi.required().messages({
        "string.base": "Payment ID must be a string",
        "any.required": "Payment ID is required when status is Accepted",
      }),
      otherwise: Joi.optional(),
    }),
  }),
});
