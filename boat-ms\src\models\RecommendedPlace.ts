import mongoose, { Document, Schema } from "mongoose";

export enum PlaceStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export interface IRecommendedPlace extends Document {
  ownerId: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  images?: mongoose.Types.ObjectId[]; // Array of File IDs
  status: PlaceStatus;
  createdAt: Date;
  updatedAt: Date;
}

const RecommendedPlaceSchema = new Schema<IRecommendedPlace>(
  {
    ownerId: { type: Schema.Types.ObjectId, ref: "Users", required: true },
    name: { type: String, required: true },
    description: { type: String },
    images: [{ type: Schema.Types.ObjectId, ref: "File" }],
    status: {
      type: String,
      enum: Object.values(PlaceStatus),
      default: PlaceStatus.ACTIVE,
    },
  },
  { timestamps: true },
);

const RecommendedPlace = mongoose.model<IRecommendedPlace>(
  "RecommendedPlace",
  RecommendedPlaceSchema,
);

export default RecommendedPlace;
