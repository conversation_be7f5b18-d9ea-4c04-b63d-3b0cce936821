import mongoose, { Schema, Document } from "mongoose";

export enum CommissionRateStatus {
  ACTIVE = "active",
  DELETED = "deleted",
}

export interface ICommissionRate extends Document {
  adminFee: number;
  affiliateRate: number;
  createdBy: mongoose.Types.ObjectId;
  updatedBy: mongoose.Types.ObjectId;
  status: CommissionRateStatus;
  createdAt: Date;
  updatedAt: Date;
}

const CommissionRateSchema: Schema = new Schema(
  {
    adminFee: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
      default: 10, // 10% default admin fee
    },
    affiliateRate: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
      default: 5, // 5% default affiliate rate
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    status: {
      type: String,
      enum: Object.values(CommissionRateStatus),
      default: CommissionRateStatus.ACTIVE,
    },
  },
  {
    timestamps: true,
  },
);

const CommissionRate = mongoose.model<ICommissionRate>(
  "CommissionRate",
  CommissionRateSchema,
);

export default CommissionRate;
