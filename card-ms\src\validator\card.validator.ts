import { Joi, Segments } from "celebrate";
import { CardStatus } from "../models/Card";

export const createCardSchema = {
  [Segments.BODY]: Joi.object().keys({
    // Method 1: Card token from client (preferred)
    cardToken: Joi.string()
      .optional()
      .min(10)
      .messages({
        "string.base": "Card token must be a string",
        "string.min": "Card token must be at least 10 characters"
      }),

    // Method 2: Direct card details (fallback)
    cardNumber: Joi.string()
      .optional()
      .pattern(/^\d{16}$/)
      .messages({
        "string.base": "Card number must be a string",
        "string.pattern.base": "Card number must be 16 digits"
      }),
    expiryMonth: Joi.number()
      .optional()
      .min(1)
      .max(12)
      .messages({
        "number.base": "Expiry month must be a number",
        "number.min": "Expiry month must be between 1 and 12",
        "number.max": "Expiry month must be between 1 and 12"
      }),
    expiryYear: Joi.number()
      .optional()
      .min(new Date().getFullYear())
      .messages({
        "number.base": "Expiry year must be a number",
        "number.min": "Expiry year must be current or future year"
      }),
    cvv: Joi.string()
      .optional()
      .pattern(/^\d{3,4}$/)
      .messages({
        "string.base": "CVV must be a string",
        "string.pattern.base": "CVV must be 3 or 4 digits"
      }),

    // Common fields
    cardholderName: Joi.string()
      .optional()
      .min(2)
      .max(100)
      .messages({
        "string.base": "Cardholder name must be a string",
        "string.min": "Cardholder name must be between 2 and 100 characters",
        "string.max": "Cardholder name must be between 2 and 100 characters"
      }),
    isDefault: Joi.boolean().default(false),
  })
  .or('cardToken', 'cardNumber') // At least one method must be provided
  .with('cardNumber', ['expiryMonth', 'expiryYear', 'cvv', 'cardholderName']) // If cardNumber, require other fields
  .messages({
    "object.missing": "Either cardToken or complete card details (cardNumber, expiryMonth, expiryYear, cvv, cardholderName) must be provided"
  }),
};

export const getCardsSchema = {
  [Segments.QUERY]: Joi.object().keys({
    status: Joi.string().valid(...Object.values(CardStatus)),
  }),
};

export const getCardByIdSchema = {
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string()
      .required()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .messages({
        "string.base": "Card ID must be a string",
        "string.pattern.base": "Invalid card ID format",
        "any.required": "Card ID is required"
      }),
  }),
};

export const updateCardSchema = {
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string()
      .required()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .messages({
        "string.base": "Card ID must be a string",
        "string.pattern.base": "Invalid card ID format",
        "any.required": "Card ID is required"
      }),
  }),
  [Segments.BODY]: Joi.object()
    .keys({
      cardholderName: Joi.string()
        .min(2)
        .max(100)
        .messages({
          "string.base": "Cardholder name must be a string",
          "string.min": "Cardholder name must be between 2 and 100 characters",
          "string.max": "Cardholder name must be between 2 and 100 characters"
        })
        .allow(null),
      isDefault: Joi.boolean().allow(null),
      status: Joi.string()
        .valid(CardStatus.ACTIVE, CardStatus.INACTIVE)
        .allow(null),
    })
    .min(1),
};

export const deleteCardSchema = {
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string()
      .required()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .messages({
        "string.base": "Card ID must be a string",
        "string.pattern.base": "Invalid card ID format",
        "any.required": "Card ID is required"
      }),
  }),
};
