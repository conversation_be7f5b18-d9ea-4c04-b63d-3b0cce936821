import { Router } from "express";
import {
  addToWishlist,
  removeFromWishlist,
  getWishlist,
  checkWishlistStatus,
} from "../controller/wishlist.controller";
import {
  idParamValidator,
  addToWishlistValidator,
} from "../validator/wishlist.validator";

const router = Router();

// Wishlist routes
router.post("/", addToWishlistValidator, addToWishlist);
router.get("/", getWishlist);
router.delete("/:boatId", idParamValidator, removeFromWishlist);
router.get("/status/:boatId", idParamValidator, checkWishlistStatus);

export default router;
