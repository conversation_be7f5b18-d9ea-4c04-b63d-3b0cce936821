import { Router } from "express";
import { addReview, getReviews, deleteReview } from "../controller";
import {
  createReviewValidator,
  getReviewsValidator,
  deleteReviewValidator,
} from "../validator/review.validator";

const routes = Router();

// Add a new review
routes.post("/", createReviewValidator, addReview);

// Get reviews with optional filtering
routes.get("/", getReviewsValidator, getReviews);

// Delete a review
routes.delete("/:id", deleteReviewValidator, deleteReview);

export default routes;
