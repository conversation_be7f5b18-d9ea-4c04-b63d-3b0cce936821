import mongoose from "mongoose";
import Wallet, { WalletStatus } from "../models/Wallet";
import Transaction, {
  TransactionSource,
  TransactionStatus,
  TransactionType,
} from "../models/Transaction";
import logger from "../../../shared/services/logger.service";

/**
 * Credit wallet with specified amount
 */
export const creditWallet = async (
  userId: mongoose.Types.ObjectId,
  amount: number,
  source: TransactionSource,
  sourceId?: mongoose.Types.ObjectId,
  bookingId?: mongoose.Types.ObjectId,
  description?: string,
  paymentMethod?: string,
  reference?: string,
) => {
  try {
    // Get or create wallet
    let wallet = await Wallet.findOne({
      userId,
      status: WalletStatus.ACTIVE,
    });

    if (!wallet) {
      wallet = new Wallet({
        userId,
        balance: 0,
        status: WalletStatus.ACTIVE,
      });
    }

    // Create transaction
    const transaction = new Transaction({
      userId,
      amount,
      type: TransactionType.CREDIT,
      status: TransactionStatus.COMPLETED,
      source,
      sourceId,
      bookingId,
      description: description || `Credit ${amount} to wallet`,
      paymentMethod,
      reference,
    });

    await transaction.save();

    // Update wallet balance
    wallet.balance += amount;
    await wallet.save();

    return {
      success: true,
      wallet,
      transaction,
    };
  } catch (error) {
    logger.error("Error in creditWallet", {
      service: "wallet-ms",
      userId,
      amount,
      source,
      error,
    });

    return {
      success: false,
      error,
    };
  }
};

/**
 * Debit wallet with specified amount
 */
export const debitWallet = async (
  userId: mongoose.Types.ObjectId,
  amount: number,
  source: TransactionSource,
  sourceId?: mongoose.Types.ObjectId,
  bookingId?: mongoose.Types.ObjectId,
  description?: string,
  paymentMethod?: string,
  reference?: string,
) => {
  try {
    // Get wallet
    const wallet = await Wallet.findOne({
      userId,
      status: WalletStatus.ACTIVE,
    });

    if (!wallet) {
      return {
        success: false,
        error: "Wallet not found",
      };
    }

    // Check if wallet has sufficient balance
    if (wallet.balance < amount) {
      return {
        success: false,
        error: "Insufficient wallet balance",
      };
    }

    // Create transaction
    const transaction = new Transaction({
      userId,
      amount,
      type: TransactionType.DEBIT,
      status: TransactionStatus.COMPLETED,
      source,
      sourceId,
      bookingId,
      description: description || `Debit ${amount} from wallet`,
      paymentMethod,
      reference,
    });

    await transaction.save();

    // Update wallet balance
    wallet.balance -= amount;
    await wallet.save();

    return {
      success: true,
      wallet,
      transaction,
    };
  } catch (error) {
    logger.error("Error in debitWallet", {
      service: "wallet-ms",
      userId,
      amount,
      source,
      error,
    });

    return {
      success: false,
      error,
    };
  }
};
