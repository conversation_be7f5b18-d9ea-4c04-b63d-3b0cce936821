# Sea Escape Backend

This is the backend service for the Sea Escape boat rental platform. The project is structured as a microservices architecture with the following services:

- **Authentication Service** (auth-ms): Handles user registration, login, and authentication
- **User Service** (user-ms): Manages user profiles, preferences, and settings
- **Boat Service** (boat-ms): Handles boat listing, details, and availability management
- **Booking Service** (booking-ms): Manages boat reservations, booking status, and scheduling
- **Wishlist Service** (wishlist-ms): Handles users' saved favorite boats
- **Affiliate Service** (affiliate-ms): Manages affiliate program for referrals and commissions
- **Notification Service** (notification-ms): Handles in-app notifications and alerts
- **Newsletter Service** (newsletter-ms): Manages newsletter subscriptions and campaigns
- **FAQ Service** (faq-ms): Handles frequently asked questions content
- **Card Service** (card-ms): Manages payment card information securely
- **Contact Us Service** (contact-us-ms): Handles user inquiries and support requests
- **Privacy Policy Service** (privacy-policy-ms): Manages privacy policy content and versioning
- **Terms & Conditions Service** (terms-condition-ms): Manages terms of service content and versioning
- **About Us Service** (about-us-ms): Handles about us page content
- **Social Media Service** (social-media-ms): Manages social media links and integrations
- **Payment Service** (payment-ms): Handles payment processing and transactions
- **Wallet Service** (wallet-ms): Manages user wallets and balance for boat owners and affiliates
- **Reviews Service** (reviews-ms): Handles boat reviews and ratings

## Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- MongoDB (v4.4 or higher)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd sea-escape-backend
```

2. Install dependencies:
```bash
npm install
```

## Running the Application

### Development Mode

To start all microservices in development mode:

```bash
npm run dev
```

To start individual microservices:

```bash
# Authentication service
npm run start:auth

# User service
npm run start:user

# Boat service
npm run start:boat

# Booking service
npm run start:booking

# Wishlist service
npm run start:wishlist

# Affiliate service
npm run start:affiliate

# Notification service
npm run start:notification

# Newsletter service
npm run start:newsletter

# FAQ service
npm run start:faq

# Card service
npm run start:card

# Contact Us service
npm run start:contact-us

# Privacy Policy service
npm run start:privacy-policy

# Terms & Conditions service
npm run start:terms-condition

# About Us service
npm run start:about-us

# Social Media service
npm run start:social-media

# Payment service
npm run start:payment

# Wallet service
npm run start:wallet

# Reviews service
npm run start:reviews
```

### Production Mode

To build and start all microservices for production:

```bash
# Build the project
npm run build

# Start in production mode
npm start
```

## Project Structure

The project follows a microservices architecture with each service having its own responsibility:

```
sea-escape-backend/
├── shared/              # Shared resources across microservices
│   ├── config.json      # Common configuration
│   ├── db.ts            # Database connection
│   ├── middleware/      # Shared middleware
│   ├── models/          # Shared models
│   └── services/        # Shared services
│
├── api/                 # API gateway and documentation
├── auth-ms/             # Authentication microservice
├── user-ms/             # User microservice
├── boat-ms/             # Boat microservice
├── booking-ms/          # Booking microservice
├── wishlist-ms/         # Wishlist microservice
├── affiliate-ms/        # Affiliate microservice
├── notification-ms/     # Notification microservice
├── newsletter-ms/       # Newsletter microservice
├── faq-ms/              # FAQ microservice
├── card-ms/             # Card microservice
├── contact-us-ms/       # Contact Us microservice
├── privacy-policy-ms/   # Privacy Policy microservice
├── terms-condition-ms/  # Terms & Conditions microservice
├── about-us-ms/         # About Us microservice
├── social-media-ms/     # Social Media microservice
├── payment-ms/          # Payment microservice
├── wallet-ms/           # Wallet microservice
└── reviews-ms/          # Reviews microservice
```

Each microservice follows a similar structure:

```
microservice/
├── index.ts             # Entry point
├── src/
│   ├── controller/      # Business logic
│   ├── models/          # Data models
│   ├── routes/          # API routes
│   ├── services/        # Services
│   ├── validator/       # Request validators
│   └── locales/         # Internationalization
```

## API Documentation

The API documentation is available through Swagger UI. Once the services are running, you can access the documentation at:

```
http://localhost:3000/api-docs
```

Each microservice has its own set of endpoints documented in the Swagger UI.

## Configuration

This project uses a centralized configuration approach with `shared/config.json` instead of environment variables. The configuration is controlled by the `NODE_ENV` environment variable, which can be set to:

- `dev` (default) - Development environment
- `test` - Testing environment
- `production` - Production environment

The `config.json` file contains environment-specific settings for:

- Database connection
- API ports
- JWT secrets
- SMTP settings
- Payment gateway credentials
- Microservice port configurations
- File storage settings

### Running with Specific Environment

To run the application with a specific environment:

```bash
# For development
NODE_ENV=dev npm run dev

# For testing
NODE_ENV=test npm run dev

# For production
NODE_ENV=production npm start
```

### Customizing Configuration

To modify the configuration, edit the `shared/config.json` file and update the values for the appropriate environment.

```json
{
  "dev": {
    "db": "mongodb://localhost:27017/sea-escape",
    "PORT": 3000,
    "JWT_SECRET": "your_jwt_secret",
    "services": {
      "boat-ms": {
        "PORT": 3102
      },
      // Other service configurations
    }
  },
  // Test and production configurations
}
```

## Features

- **Authentication**: JWT-based authentication and authorization
- **User Management**: User profiles, settings, and document uploads
- **Boat Management**: Create, update, delete and search boats
- **Booking System**: Book boats, manage bookings, handle payments
- **Wishlist**: Save favorite boats
- **Affiliates**: Referral program for generating income
- **Notifications**: Email and in-app notifications
- **Newsletter**: Newsletter subscription and management
- **FAQ Management**: Dynamic FAQ system
- **Payment Processing**: Secure payment processing with PayPal integration
- **Wallet System**: User wallet for boat owners and affiliates
- **Reviews & Ratings**: Boat review and rating system
- **Content Management**: About Us, Privacy Policy, Terms & Conditions pages
- **Social Media Integration**: Connect and share on social platforms
- **Contact Management**: Handle user inquiries and support requests
- **Internationalization**: Multi-language support (English, Arabic, German, etc.)