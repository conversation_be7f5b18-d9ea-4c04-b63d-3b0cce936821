import mongoose, { Document, Schema } from "mongoose";

export enum BoatType {
  YACHT = "Yacht",
  SAILBOAT = "Sailboat",
  CATAMARAN = "Catamaran",
  SPEEDBOAT = "Speedboat",
  FISHING_BOAT = "Fishing Boat",
  HOUSEBOAT = "Houseboat",
  
}

export enum BoatStatus {
  DRAFT = "draft",
  PUBLISHED = "published",
  DELETED = "deleted",
}

export enum DayDuration {
  FULL_DAY = "Full Day",
  HALF_DAY = "Half Day",
}

export enum RecordType {
  BOAT = "boat",
  ACTIVITY = "activity",
}

export interface IScheduleTime {
  _id?: mongoose.Types.ObjectId;
  day: string;
  duration: DayDuration;
  startTime: string; // Format: "HH:MM AM/PM"
  endTime: string; // Format: "HH:MM AM/PM"
}

export interface IBoat extends Document {
  ownerId: mongoose.Types.ObjectId;
  name: string;
  type: string;
  guestsCapacity: number;
  cabins: number;
  baths: number;
  pricePerDay: number;
  visualizationUrl?: string;
  description?: string;
  fullDayWithPatron: number;
  fullDayWithoutPatron: number;
  halfDayWithPatron: number;
  halfDayWithoutPatron: number;
  affiliateCode?: string;
  language: string[];
  fuel: boolean;
  patron: boolean;
  license: boolean;
  schedule: IScheduleTime[];
  checkInNotes?: string;
  checkOutNotes?: string;
  location: string;
  lat: number;
  lng: number;
  recommendedPlaces: mongoose.Types.ObjectId[];
  availability: {
    start: Date;
    end: Date;
  };
  attachments: {
    images: mongoose.Types.ObjectId[]; // Array of File IDs
    videos: mongoose.Types.ObjectId[]; // Array of File IDs
    documents: mongoose.Types.ObjectId[]; // Array of File IDs
  };
  facilities: mongoose.Types.ObjectId[];
  status: BoatStatus;
  recordType: RecordType;
  duration?: number; // in hours
  price?: number; // Price per activity (different from pricePerDay)
  safetyInstructions?: string;
  createdAt: Date;
  updatedAt: Date;
}

const BoatSchema = new Schema<IBoat>(
  {
    ownerId: { type: Schema.Types.ObjectId, ref: "Users", required: true },
    name: { type: String, required: true },
    type: {
      type: String,
      enum: Object.values(BoatType),
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    guestsCapacity: {
      type: Number,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    cabins: {
      type: Number,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    baths: {
      type: Number,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    pricePerDay: {
      type: Number,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    visualizationUrl: { type: String },
    description: { type: String },
    fullDayWithPatron: {
      type: Number,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    fullDayWithoutPatron: {
      type: Number,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    halfDayWithPatron: {
      type: Number,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    halfDayWithoutPatron: {
      type: Number,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    affiliateCode: { type: String },
    language: [
      {
        type: String,
        required: function () {
          return this.recordType === RecordType.BOAT;
        },
      },
    ],
    fuel: {
      type: Boolean,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    patron: {
      type: Boolean,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    license: {
      type: Boolean,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    schedule: [
      {
        day: {
          type: String,
          required: function () {
            return this.recordType === RecordType.BOAT;
          },
        },
        duration: {
          type: String,
          enum: Object.values(DayDuration),
          required: function () {
            return this.recordType === RecordType.BOAT;
          },
        },
        startTime: {
          type: String,
          required: function () {
            return this.recordType === RecordType.BOAT;
          },
        },
        endTime: {
          type: String,
          required: function () {
            return this.recordType === RecordType.BOAT;
          },
        },
      },
    ],
    checkInNotes: { type: String },
    checkOutNotes: { type: String },
    location: {
      type: String,
      required: function () {
        return this.recordType === RecordType.BOAT;
      },
    },
    lat: {
      type: Number
    },
    lng: {
      type: Number
    },
    recommendedPlaces: [
      { type: Schema.Types.ObjectId, ref: "RecommendedPlace" },
    ],
    availability: {
      start: { type: Date, required: true },
      end: { type: Date, required: true },
    },
    attachments: {
      images: [{ type: Schema.Types.ObjectId, ref: "File", required: true }], // Images required for all record types
      videos: [{ type: Schema.Types.ObjectId, ref: "File" }],
      documents: [{ type: Schema.Types.ObjectId, ref: "File" }],
    },
    facilities: [{ type: Schema.Types.ObjectId, ref: "Facility" }],
    status: {
      type: String,
      enum: Object.values(BoatStatus),
      default: BoatStatus.DRAFT,
    },
    recordType: {
      type: String,
      enum: Object.values(RecordType),
      default: RecordType.BOAT,
    },
    duration: { type: Number },
    price: { type: Number },
    safetyInstructions: { type: String },
  },
  { timestamps: true },
);

const Boat = mongoose.model<IBoat>("Boat", BoatSchema);

export default Boat;
