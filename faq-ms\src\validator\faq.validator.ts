import { celebrate, Joi, Segments } from "celebrate";

export const createFAQValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    question: Joi.string().required().trim(),
    answer: Joi.string().required().trim(),
    order: Joi.number().optional(),
  }),
});

export const updateFAQValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    question: Joi.string().optional().trim(),
    answer: Joi.string().optional().trim(),
    order: Joi.number().optional(),
  }),
});

export const getFAQValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string()
      .required()
      .regex(/^[0-9a-fA-F]{24}$/),
  }),
});

export const deleteFAQValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string()
      .required()
      .regex(/^[0-9a-fA-F]{24}$/),
  }),
});
