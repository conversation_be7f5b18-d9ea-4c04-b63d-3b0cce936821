{"BOAT_NOT_FOUND": "Boot nicht gefunden", "BOAT_CREATED_SUCCESS": "<PERSON>ot erfolg<PERSON>ich erstellt", "BOAT_UPDATED_SUCCESS": "Boot erfolgreich aktualisiert", "BOAT_DELETED_SUCCESS": "<PERSON>ot er<PERSON><PERSON><PERSON><PERSON><PERSON>", "BOAT_ATTACHMENT_ADDED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hinz<PERSON>", "BOAT_ATTACHMENT_REMOVED": "<PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON> entfernt", "BOAT_AVAILABILITY_SET": "Verfügbarkeit des boots erfolgreich festgelegt", "BOAT_NOT_OWNER": "Sie sind nicht der besitzer dieses boots", "BOAT_PLACE_ADDED": "Empfohlener ort erfolgreich hinzugefügt", "BOAT_PLACE_REMOVED": "Empfohlener ort erfolgreich entfernt", "BOAT_PLACE_NOT_FOUND": "Empfohlener ort nicht gefunden", "BOAT_CHECKIN_ADDED": "Check-in zeitplan erfolgreich hinzugefügt", "BOAT_CHECKOUT_ADDED": "Check-out zeitplan erfolgreich hinzugefügt", "BOAT_CHECKIN_REMOVED": "Check-in zeitplan erfolgreich entfernt", "BOAT_CHECKOUT_REMOVED": "Check-out zeitplan erfolgreich entfernt", "BOAT_SCHEDULE_ADDED": "Zeitplan erfolgreich hinzugefügt", "BOAT_SCHEDULE_REMOVED": "Zeitplan erfolgreich entfernt", "BOAT_SCHEDULE_NOT_FOUND": "Zeitplan nicht gefunden", "BOAT_INVALID_SCHEDULE_TYPE": "Ungültiger zeitplantyp. muss entweder 'check-in' oder 'check-out' sein", "BOAT_INVALID_DOCUMENT_IDS": "Eine oder mehrere dokument-ids sind ungültig oder gehören ihnen nicht", "PLACE_NOT_FOUND": "Empfohlener ort nicht gefunden", "PLACE_CREATED_SUCCESS": "Empfohlener ort erfolgreich erstellt", "PLACE_UPDATED_SUCCESS": "Empfohlener ort erfolgreich aktualisiert", "PLACE_DELETED_SUCCESS": "Empfohlener ort erfolgreich gelöscht", "FACILITY_NOT_FOUND": "Einrichtung nicht gefunden", "FACILITY_CREATED_SUCCESS": "Einrichtung erfolgreich erstellt", "FACILITY_UPDATED_SUCCESS": "Einrichtung erfolgreich aktualisiert", "FACILITY_DELETED_SUCCESS": "Einrichtung erfolgreich gelöscht", "FACILITY_IMAGE_ADDED": "Einrichtungsbild erfolgreich hinzugefügt", "DOCUMENT_USE_USER_API": "Für dokumentuploads verwenden sie bitte die dokumentupload-api im benutzerdienst", "DOCUMENT_INVALID_IDS": "Eine oder mehrere dokument-ids sind ungültig oder gehören ihnen nicht", "DOCUMENT_UPLOAD_SUCCESS": "Dokument erfolgreich hochgeladen", "ACTIVITY_NOT_FOUND": "Aktivität nicht gefunden", "ACTIVITY_CREATED_SUCCESS": "Aktivität erfolgreich erstellt", "ACTIVITY_UPDATED_SUCCESS": "Aktivität erfolgreich aktualisiert", "ACTIVITY_DELETED_SUCCESS": "Aktivität erfolgreich gelöscht", "ACTIVITY_NOT_OWNER": "Sie sind nicht der eigentümer dieser aktivität", "SOMETHING_WENT_WRONG": "Etwas ist schief gelaufen, bitte versuchen sie es erneut", "UNAUTHORIZED": "Nicht autorisierter zugriff", "TOKEN_REQUIRED": "Autorisierungstoken ist erforderlich", "FAIL_TOKEN_EXPIRED": "Sitzung ist abgelaufen. bitte melden sie sich erneut an.", "USER_NOT_FOUND": "Benutzer nicht gefunden.", "ACCOUNT_INACTIVE": "Konto ist inaktiv oder gelöscht. bitte wenden sie sich an den support.", "BOAT_TYPES_FETCHED": "Boottypen erfolgreich abgerufen", "BOAT_LOCATIONS_FETCHED": "Bootorte erfolgreich abgerufen", "POPULAR_DESTINATIONS_FETCHED": "Beliebte reiseziele erfolgreich abgerufen", "NEARBY_BOATS_FETCHED": "Nahegelegene boote und aktivitäten erfolgreich abgerufen", "LAT_LNG_REQUIRED": "Breiten- und längengrad sind für standortbasierte suche erforderlich", "NO_FILES_UPLOADED": "<PERSON><PERSON> ho<PERSON>", "BOAT_IMAGES_ADDED": "Bilder er<PERSON><PERSON><PERSON><PERSON><PERSON> zum boot hinzugefügt", "BOAT_IMAGES_REMOVED": "Bilder erfolgre<PERSON> vom boot entfernt", "IMAGE_IDS_REQUIRED": "Bild-ids sind für die entfernung erforderlich", "OWNER_ACTIVITIES_FETCHED": "Eigentümeraktivitäten erfolgreich abgerufen", "ACCESS_DENIED": "Zugriff verweigert.", "FORBIDDEN": "<PERSON>e sind nicht berechtigt, auf diese ressource zuzugreifen.", "INTERNAL_SERVER_ERROR": "Ein interner serverfehler ist aufgetreten.", "INVALID_ID": "Ungültige id angegeben.", "INVALID_PAGINATION_PARAMS": "Ungültige paginierungsparameter.", "INVALID_FILTER_PARAMS": "Ungültige filterparameter.", "ACTIVITY_INVALID_ID": "Ungültige aktivitäts-id", "AFFILIATE_CODE_NOT_FOUND": "Affiliate-code existiert nicht. bitte überprüfen sie den code und versuchen sie es erneut.", "AFFILIATE_CODE_NOT_ACTIVE": "Affiliate-code ist nicht aktiv. nur genehmigte affiliate-codes können verwendet werden.", "AFFILIATE_CODE_EXPIRED": "Affiliate-code ist abgelaufen und kann nicht mehr verwendet werden.", "AFFILIATE_CODE_VALID": "Affiliate-code ist gültig und aktiv.", "AFFILIATE_CODE_VALIDATION_ERROR": "Fehler beim validieren des affiliate-codes. bitte versuchen sie es erneut.", "AFFILIATE_CODE_INVALID_FORMAT": "Affiliate-code-format ist ungültig. code muss 6-20 alphanumerische zeichen haben.", "REFERRAL_NAME_NONE": "<PERSON>in empfehlungsname angegeben.", "REFERRAL_NAME_NOT_FOUND": "Empfehlungsname nicht gefunden oder nicht aktiv. bitte überprüfen sie den namen und versuchen sie es erneut.", "REFERRAL_NAME_VALID": "Empfehlungsname ist gültig und aktiv.", "REFERRAL_NAME_VALIDATION_ERROR": "Fehler beim validieren des empfehlungsnamens. bitte versuchen sie es erneut.", "INVALID_BOAT_ID": "Ungültige boot-id angegeben.", "BOAT_NOT_FOUND_OR_NOT_AVAILABLE": "Boot nicht gefunden oder nicht verfügbar.", "BOAT_OWNER_NOT_FOUND": "Bootbesitzer nicht gefunden.", "CANNOT_BOOK_OWN_BOAT": "<PERSON>e können ihr eigenes boot nicht buchen.", "BOAT_FETCH_SUCCESS": "Boot erfolgreich abgerufen.", "INVALID_IMAGE_IDS": "Eine oder mehrere bild-ids sind ungültig.", "VIDEO_IDS_REQUIRED": "Video-ids sind für die entfernung erforderlich.", "INVALID_VIDEO_IDS": "Eine oder mehrere video-ids sind ungültig.", "BOAT_VIDEOS_REMOVED": "Videos erfolgreich vom boot entfernt."}