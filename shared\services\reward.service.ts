import RewardModel, { RewardStatus, RewardType } from "../models/Reward";
import { emailSender } from "./sendMail.service";
import cron from "node-cron";
import logger from "./logger.service";
import { EMAIL_CONSTANT } from "../constant/emailContant";
import { CurrencyCode } from "../../user-ms/src/models/User";
import mongoose from "mongoose";
/**
 * Add points to user's reward account
 */
export const addPoints = async (
  userId: mongoose.Schema.Types.ObjectId,
  points: number,
  type: string,
  description: string,
  bookingId?: any,
  reviewId?: any
) => {
  try {
    const currentYear = new Date().getFullYear();
    const expiryDate = new Date(currentYear, 11, 31, 23, 59, 59);

    // Find or create reward record
    let reward = await RewardModel.findOne({ userId });
    if (!reward) {
      reward = await RewardModel.create({
        userId,
        points: 0,
        pointsExpiryDate: expiryDate,
        welcomeDiscounts: [], // Two welcome discounts
        createdBy: userId,
        updatedBy: userId
      });
    }

    if(reward.status === RewardStatus.DELETED) {
      reward.status = RewardStatus.ACTIVE;
      await reward.save();
    }
    
    // Add points and history
    reward.points += points;
    reward.pointsHistory.push({
      points,
      type,
      bookingId: bookingId,
      reviewId: reviewId,
      description,
      createdAt: new Date()
    });
    reward.updatedBy = userId;

    await reward.save();
    return reward;
  } catch (error: any) {
    logger.error(`Error adding points: ${error.message}`, {
      service: "reward-service",
      error
    });
    throw error;
  }
};

/**
 * Redeem points for a booking
 */
export const redeemPoints = async (
  userId: mongoose.Schema.Types.ObjectId,
  points: number,
  bookingId: any,
  description: string
) => {
  try {
    const reward = await RewardModel.findOne({ userId });
    if (!reward) {
      throw new Error("No reward account found");
    }

    if (reward.points < points) {
      throw new Error("Insufficient points");
    }

    // Deduct points and add to history
    reward.points -= points;
    reward.pointsHistory.push({
      points: -points,
      type: RewardType.REDEEMED,
      bookingId: bookingId,
      description,
      createdAt: new Date()
    });
    reward.updatedBy = userId;

    await reward.save();
    return reward;
  } catch (error: any) {
    logger.error(`Error redeeming points: ${error.message}`, {
      service: "reward-service",
      error
    });
    throw error;
  }
};

/**
 * Check and use welcome discount
 */
export const useWelcomeDiscount = async (
  userId: mongoose.Schema.Types.ObjectId,
  bookingId: any
) => {
  try {
    const reward = await RewardModel.findOne({ userId });
    if (!reward) {
      throw new Error("No reward account found");
    }

    // Find unused welcome discount
    const unusedDiscount = reward.welcomeDiscounts.find(d => !d.used);
    if (!unusedDiscount) {
      throw new Error("No welcome discounts available");
    }

    // Use the discount
    unusedDiscount.used = true;
    unusedDiscount.bookingId = bookingId
    unusedDiscount.usedAt = new Date();
    reward.updatedBy = userId;

    await reward.save();
    return reward;
  } catch (error: any) {
    logger.error(`Error using welcome discount: ${error.message}`, {
      service: "reward-service",
      error
    });
    throw error;
  }
};

/**
 * Check if user has welcome discounts available
 */
export const checkWelcomeDiscounts = async (userId: mongoose.Schema.Types.ObjectId) => {
  try {
    const reward = await RewardModel.findOne({ userId });
    if (!reward) {
      return { available: 2 }; // New user gets 2 discounts
    }

    const unusedCount = reward.welcomeDiscounts.filter(d => !d.used).length;
    return { available: unusedCount };
  } catch (error: any) {
    logger.error(`Error checking welcome discounts: ${error.message}`, {
      service: "reward-service",
      error
    });
    throw error;
  }
};

/**
 * Get user's reward details
 */
export const getRewardDetails = async (userId: mongoose.Schema.Types.ObjectId) => {
  try {
    const reward = await RewardModel.findOne({ userId });
    if (!reward) {
      return null;
    }
    return reward;
  } catch (error: any) {
    logger.error(`Error getting reward details: ${error.message}`, {
      service: "reward-service",
      error
    });
    throw error;
  }
};

// Schedule cron job to send expiry reminders
cron.schedule("0 0 1 12 *", async () => { // Run on December 1st
  try {
    const rewards: any = await RewardModel.find({
      points: { $gt: 0 }
    }).populate("userId", "email username currency");

    for (const reward of rewards) {
      // Send reminder email
      await emailSender(
        reward?.userId?.email,
        EMAIL_CONSTANT.REWARD_EXPIRE_REMINDER.SUBJECT,
        {
          username: reward?.userId?.username,
          points: reward.points,
          expiryDate: reward.pointsExpiryDate,
          bookingCurrency: CurrencyCode[reward?.userId?.currency as keyof typeof CurrencyCode]
        },
        EMAIL_CONSTANT.REWARD_EXPIRE_REMINDER.TEMPLATE
      );
    }
  } catch (error: any) {
    logger.error(`Error sending expiry reminders: ${error.message}`, {
      service: "reward-service",
      error
    });
  }
});

// Schedule cron job to expire points
cron.schedule("0 0 1 1 *", async () => { // Run on January 1st
  try {
    const lastYear = new Date().getFullYear() - 1;
    const rewards = await RewardModel.find({
      points: { $gt: 0 },
      pointsExpiryDate: {
        $lte: new Date(lastYear, 11, 31, 23, 59, 59)
      }
    });

    for (const reward of rewards) {
      // Add expiry record to history
      reward.pointsHistory.push({
        points: -reward.points,
        type: RewardType.EXPIRED,
        description: `Points expired on ${reward.pointsExpiryDate.toDateString()}`,
        createdAt: new Date()
      });

      // Reset points and update expiry date
      reward.points = 0;
      const currentYear = new Date().getFullYear();
      reward.pointsExpiryDate = new Date(currentYear, 11, 31, 23, 59, 59);

      await reward.save();
    }
  } catch (error: any) {
    logger.error(`Error expiring points: ${error.message}`, {
      service: "reward-service",
      error
    });
  }
}); 