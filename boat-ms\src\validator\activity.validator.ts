import { celebrate, Joi, Segments } from "celebrate";
import { BoatStatus, RecordType } from "../models/Boat";

export const createActivityValidatorBody = Joi.object().keys({
  name: Joi.string().required(),
  description: Joi.string().optional().allow(null, ""),
  duration: Joi.number().required().min(0.5), // At least 30 minutes
  location: Joi.string().required(),
  lat: Joi.number().required(),
  lng: Joi.number().required(),
  price: Joi.number().required().min(0),
  safetyInstructions: Joi.string().optional().allow(null, ""),
  status: Joi.string()
    .valid(...Object.values(BoatStatus))
    .optional(),
  availability: Joi.object()
    .keys({
      start: Joi.date().iso().required(),
      end: Joi.date().iso().min(Joi.ref("start")).required(),
    })
    .required(),
  recordType: Joi.string()
    .valid(RecordType.ACTIVITY)
    .default(RecordType.ACTIVITY),
  type: Joi.string().optional().allow(null, ""),
  affiliateCode: Joi.string().optional().allow(null, ""),
});

export const updateActivityValidatorBody = Joi.object().keys({
  name: Joi.string().optional(),
  description: Joi.string().optional().allow(null, ""),
  duration: Joi.number().optional().min(0.5),
  location: Joi.string().optional(),
  lat: Joi.number().optional(),
  lng: Joi.number().optional(),
  price: Joi.number().optional().min(0),
  safetyInstructions: Joi.string().optional().allow(null, ""),
  status: Joi.string()
    .valid(...Object.values(BoatStatus))
    .optional(),
  availability: Joi.object()
    .keys({
      start: Joi.date().iso().required(),
      end: Joi.date().iso().min(Joi.ref("start")).required(),
    })
    .optional(),
  type: Joi.string().optional().allow(null, ""),
  affiliateCode: Joi.string().optional().allow(null, ""),
  // Existing images/videos for selective updates
  existingImages: Joi.array().items(Joi.string()).optional().allow(null),
  existingVideos: Joi.array().items(Joi.string()).allow(null),
});

export const createActivityValidator = celebrate({
  [Segments.BODY]: createActivityValidatorBody,
});

export const updateActivityValidator = celebrate({
  [Segments.BODY]: updateActivityValidatorBody,
});

export const idParamValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string().required(),
  }),
});
