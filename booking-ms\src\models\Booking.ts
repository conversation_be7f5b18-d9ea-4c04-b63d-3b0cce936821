import mongoose, { Document, Schema } from "mongoose";
import { UserCurrency } from "../../../user-ms/src/models/User";

export enum BookingStatus {
  Pending = "Pending",
  ReadyForPayment = "ReadyForPayment",
  Accepted = "Accepted",
  Rejected = "Rejected",
  Cancelled = "Cancelled",
  Completed = "Completed",
}

export enum BookingDuration {
  FullDay = "FullDay",
  HalfDay = "HalfDay",
}

export enum PatronType {
  FullDayWithPatron = "fullDayWithPatron",
  FullDayWithoutPatron = "fullDayWithoutPatron",
  HalfDayWithPatron = "halfDayWithPatron",
  HalfDayWithoutPatron = "halfDayWithoutPatron",
}

export interface IExtraFacility {
  facilityId: mongoose.Types.ObjectId;
  name: string;
  price: number;
  quantity: number;
}

export interface IBooking extends Document {
  boatId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  startDate: Date;
  endDate: Date;
  location: string;
  duration: BookingDuration;
  patronType: PatronType;
  extraFacilities: IExtraFacility[];
  referralName?: string;
  totalAmount: number;
  adminFee: number;
  netAmount: number;
  status: BookingStatus;
  rejectionReason?: string;
  paymentId?: mongoose.Types.ObjectId;
  currency?: string; // The currency used for display to the renter
  ownerCurrency?: string; // The currency of the boat owner
  affiliateCode?: string; // The affiliate code used for this booking
  createdAt: Date;
  updatedAt: Date;
}

const BookingSchema = new Schema<IBooking>(
  {
    boatId: { type: Schema.Types.ObjectId, ref: "Boat", required: true },
    userId: { type: Schema.Types.ObjectId, ref: "Users", required: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    location: { type: String, required: true },
    duration: {
      type: String,
      enum: Object.values(BookingDuration),
      required: true,
    },
    patronType: {
      type: String,
      enum: Object.values(PatronType),
      required: false, // Not required for activities
      default: PatronType.FullDayWithoutPatron, // Default value for activities
    },
    extraFacilities: [
      {
        facilityId: {
          type: Schema.Types.ObjectId,
          ref: "Facility",
          required: true,
        },
        name: { type: String, required: true },
        price: { type: Number, required: true },
        quantity: { type: Number, required: true, min: 1, default: 1 },
      },
    ],
    referralName: { type: String },
    totalAmount: { type: Number, required: true },
    adminFee: { type: Number, required: true },
    netAmount: { type: Number, required: true },
    status: {
      type: String,
      enum: Object.values(BookingStatus),
      default: BookingStatus.Pending,
    },
    rejectionReason: { type: String },
    paymentId: { type: Schema.Types.ObjectId, ref: "Payment" },
    currency: { 
      type: String, 
      enum: Object.values(UserCurrency),
      default: UserCurrency.USD 
    },
    ownerCurrency: {
      type: String,
      enum: Object.values(UserCurrency),
      default: UserCurrency.USD
    },
    affiliateCode: { type: String },
  },
  { timestamps: true },
);

// Add indexes for common queries
BookingSchema.index({ boatId: 1, startDate: 1, endDate: 1 });
BookingSchema.index({ userId: 1, status: 1 });
BookingSchema.index({ status: 1 });

const Booking = mongoose.model<IBooking>("Booking", BookingSchema);
export default Booking;
