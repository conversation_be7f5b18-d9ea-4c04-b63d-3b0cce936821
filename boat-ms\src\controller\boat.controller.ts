import { StatusCodes } from "http-status-codes";
import logger from "../../../shared/services/logger.service";
import Boat, { BoatStatus, RecordType } from "../models/Boat";
import File, { FileType } from "../../../shared/models/Files";
import { getRelativePath } from "../../../shared/middleware/fileUpload.middleware";
import { Types } from "mongoose";
import {
  createBoatValidatorBody,
  updateBoatValidatorBody,
} from "../validator/boat.validator";
import { parseJSON } from "../../../shared/helper/utils";
import mongoose from "mongoose";
import {
  createActivityLog,
  validateAndGetChanges,
  IChange,
} from "../../../shared/models/ActivityLog";
import { ReviewStatus } from "../../../reviews-ms/src/models/Review";
import affiliateCodeService from "../../../shared/services/affiliateCode.service";
import RecommendedPlaceModel from "../models/RecommendedPlace";
import FacilityModel from "../models/Facility";
import Facility from "../models/Facility";
import RecommendedPlace from "../models/RecommendedPlace";

// Enhanced helper function to parse nested form data dynamically
const parseNestedFormData = (body: any, files: any) => {
  const facilities: any[] = [];
  const recommendedPlaces: any[] = [];

  console.log("Parsing nested form data...");
  console.log("Body keys:", Object.keys(body));
  console.log(
    "Files:",
    files
      ? Array.isArray(files)
        ? files.map((f) => f.fieldname)
        : Object.keys(files)
      : "No files"
  );

  // Parse facilities from form data - support multiple patterns
  Object.keys(body).forEach((key) => {
    // Support patterns: facilities[0].name, facilities.0.name, facilities[0][name]
    const facilityMatch =
      key.match(/^facilities\[(\d+)\]\.(.+)$/) ||
      key.match(/^facilities\[(\d+)\]\[(.+)\]$/);

    if (facilityMatch) {
      const index = parseInt(facilityMatch[1]);
      let field = facilityMatch[2];

      // Remove any leading dots from field name
      field = field.replace(/^\.+/, "");

      if (!facilities[index]) {
        facilities[index] = {};
      }

      if (field === "price") {
        facilities[index][field] = parseFloat(body[key]) || 0;
      } else {
        facilities[index][field] = body[key];
      }
      console.log(`Parsed facility[${index}].${field}:`, body[key]);
    }

    // Parse recommended places from form data - support multiple patterns
    const placeMatch =
      key.match(/^recommendedPlaces\[(\d+)\]\.(.+)$/) ||
      key.match(/^recommendedPlaces\[(\d+)\]\[(.+)\]$/);

    if (placeMatch) {
      const index = parseInt(placeMatch[1]);
      let field = placeMatch[2];

      // Remove any leading dots from field name
      field = field.replace(/^\.+/, "");

      if (!recommendedPlaces[index]) {
        recommendedPlaces[index] = {};
      }

      recommendedPlaces[index][field] = body[key];
      console.log(`Parsed place[${index}].${field}:`, body[key]);
    }
  });

  // Add images from files to facilities and places - handle dynamic file uploads
  if (files) {
    const fileArray = Array.isArray(files)
      ? files
      : Object.values(files).flat();

    fileArray.forEach((file: any) => {
      const fieldname = file.fieldname;

      // Match facility images with multiple patterns
      const facilityImageMatch =
        fieldname.match(/^facilities\[(\d+)\]\.images$/) ||
        fieldname.match(/^facilities\[(\d+)\]\[images\]$/);

      if (facilityImageMatch) {
        const index = parseInt(facilityImageMatch[1]);
        if (!facilities[index]) {
          facilities[index] = {};
        }
        if (!facilities[index].imageFiles) {
          facilities[index].imageFiles = [];
        }
        facilities[index].imageFiles.push(file);
        console.log(`Added image to facility[${index}]:`, file.filename);
      }

      // Match place images with multiple patterns
      const placeImageMatch =
        fieldname.match(/^recommendedPlaces\[(\d+)\]\.images$/) ||
        fieldname.match(/^recommendedPlaces\[(\d+)\]\[images\]$/);

      if (placeImageMatch) {
        const index = parseInt(placeImageMatch[1]);
        if (!recommendedPlaces[index]) {
          recommendedPlaces[index] = {};
        }
        if (!recommendedPlaces[index].imageFiles) {
          recommendedPlaces[index].imageFiles = [];
        }
        recommendedPlaces[index].imageFiles.push(file);
        console.log(`Added image to place[${index}]:`, file.filename);
      }
    });
  }

  const filteredFacilities = facilities.filter(
    (f) => f && Object.keys(f).length > 0
  );
  const filteredPlaces = recommendedPlaces.filter(
    (p) => p && Object.keys(p).length > 0
  );

  console.log(
    `Parsed ${filteredFacilities.length} facilities and ${filteredPlaces.length} places`
  );

  return {
    facilities: filteredFacilities,
    recommendedPlaces: filteredPlaces,
  };
};

export const createBoat = async (req: any, res: any): Promise<any> => {
  try {
    console.log("Starting boat creation for user:", req.user._id);
    console.log("Request body keys:", Object.keys(req.body));
    console.log(
      "Request files:",
      req.files ? Object.keys(req.files) : "No files"
    );

    const userId = req.user._id;

    // Parse nested form data first to extract facilities and places
    const { facilities, recommendedPlaces } = parseNestedFormData(
      req.body,
      req.files
    );

    // Create clean boat data without nested form fields
    const cleanBody = { ...req.body };

    // Remove nested form fields from body
    Object.keys(cleanBody).forEach((key) => {
      if (key.match(/^(facilities|recommendedPlaces)\[/)) {
        delete cleanBody[key];
      }
    });

    const boatData = {
      ...cleanBody,
      schedule: parseJSON(cleanBody.schedule, []),
      availability: parseJSON(cleanBody.availability, {}),
      language: Array.isArray(cleanBody.language)
        ? cleanBody.language
        : cleanBody.language
          ? [cleanBody.language]
          : [],
      documents: parseJSON(cleanBody.documents, []),
      status: cleanBody.status || "draft",
    };

    // Initialize empty arrays for recommended places and facilities
    boatData.recommendedPlaces = [];
    boatData.facilities = [];

    Object.keys(boatData).forEach((key) => {
      if (boatData[key]?.toString().trim() === "true") {
        boatData[key] = true;
      } else if (boatData[key]?.toString().trim() === "false") {
        boatData[key] = false;
      }
    });

    // Validate body
    console.log("Validating boat data...");
    const { error } = createBoatValidatorBody.validate(boatData);
    if (error) {
      console.log("Validation error:", error.message);
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: error.message,
      });
    }
    console.log("Validation passed");

    // Validate affiliate code if provided
    if (boatData.affiliateCode) {
      console.log("Validating affiliate code:", boatData.affiliateCode);
      try {
        const affiliateValidation =
          await affiliateCodeService.validateAffiliateCode(
            boatData.affiliateCode
          );
        if (!affiliateValidation.isValid) {
          console.log(
            "Affiliate code validation failed:",
            affiliateValidation.messageKey
          );
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__(affiliateValidation.messageKey),
          });
        }
        // Normalize the affiliate code
        boatData.affiliateCode = affiliateCodeService.normalizeAffiliateCode(
          boatData.affiliateCode
        );
        console.log("Affiliate code validated and normalized");
      } catch (affiliateError) {
        console.error("Error validating affiliate code:", affiliateError);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          status: false,
          message: "Error validating affiliate code",
        });
      }
    }

    boatData.ownerId = userId;

    // Handle file uploads
    console.log("Processing file uploads...");
    boatData.attachments = {
      images: [],
      videos: [],
      documents: [],
    };

    // Process images
    console.log("Processing boat images...");
    console.log("req.files structure:", req.files);

    // Handle both array structure (from .any()) and object structure (from .fields())
    let imageFiles = [];
    let videoFiles = [];

    if (req.files) {
      if (Array.isArray(req.files)) {
        // Handle .any() structure - filter by fieldname
        imageFiles = req.files.filter((file: any) => file.fieldname === 'images');
        videoFiles = req.files.filter((file: any) => file.fieldname === 'videos');
      } else {
        // Handle .fields() structure - direct access
        imageFiles = req.files.images || [];
        videoFiles = req.files.videos || [];
      }
    }

    console.log("Image files found:", imageFiles.length);
    console.log("Video files found:", videoFiles.length);

    if (imageFiles.length > 0) {
      const imageIds = await Promise.all(
        imageFiles.map(async (file: any) => {
          const relativePath = getRelativePath(file.path);
          console.log("Processing image file:", file.filename, "at path:", relativePath);

          const newFile = await File.create({
            name: file.filename,
            size: file.size,
            fileType: file.mimetype,
            ext: file.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.IMAGE,
            ownerId: userId,
          });
          console.log("Created image file record with ID:", newFile._id);
          return newFile._id;
        })
      );
      boatData.attachments.images = imageIds;
      console.log("Assigned image IDs to boat:", imageIds);
    }

    // Process videos
    if (videoFiles.length > 0) {
      const videoIds = await Promise.all(
        videoFiles.map(async (file: any) => {
          const relativePath = getRelativePath(file.path);
          console.log("Processing video file:", file.filename, "at path:", relativePath);

          const newFile = await File.create({
            name: file.filename,
            size: file.size,
            fileType: file.mimetype,
            ext: file.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.VIDEO,
            ownerId: userId,
          });
          console.log("Created video file record with ID:", newFile._id);
          return newFile._id;
        })
      );
      boatData.attachments.videos = videoIds;
      console.log("Assigned video IDs to boat:", videoIds);
    }

    // Process document IDs (not file uploads, just references to existing docs)
    if (
      boatData.documents &&
      Array.isArray(boatData.documents) &&
      boatData.documents.length > 0
    ) {
      // Validate that all document IDs exist and belong to this user
      const documentIds = boatData.documents
        .filter((id: string) => Types.ObjectId.isValid(id))
        .map((id: string) => new Types.ObjectId(id));

      if (documentIds.length === 0 && boatData.documents.length > 0) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("BOAT_INVALID_DOCUMENT_IDS"),
        });
      }

      // Check if documents exist and belong to user
      const existingDocs = await File.find({
        _id: { $in: documentIds },
        ownerId: userId,
        type: FileType.DOCUMENT,
      });

      if (existingDocs.length !== documentIds.length) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("BOAT_INVALID_DOCUMENT_IDS"),
        });
      }

      // Set document IDs in boat data
      boatData.attachments.documents = documentIds;
    }

    // Use already parsed nested form data
    console.log("Using parsed nested form data...");
    console.log("Parsed facilities:", facilities.length);
    console.log("Parsed recommended places:", recommendedPlaces.length);

    // Process recommended places (create new ones)
    console.log("Processing recommended places...");
    if (recommendedPlaces.length > 0) {
      for (const placeData of recommendedPlaces) {
        console.log(
          "Processing place data:",
          JSON.stringify(placeData, null, 2)
        );

        // Validate required fields
        if (!placeData.name || !placeData.description) {
          console.error("Missing required place data:", {
            name: placeData.name,
            description: placeData.description,
          });
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message:
              "Missing required fields for recommended place: name and description are required",
          });
        }

        console.log("Creating recommended place:", placeData.name);
        const newPlace = {
          name: placeData.name,
          description: placeData.description,
          status: "active",
          ownerId: userId,
          images: [] as mongoose.Types.ObjectId[],
        };

        // If place has image files, process them
        if (placeData.imageFiles && Array.isArray(placeData.imageFiles)) {
          for (const imageFile of placeData.imageFiles) {
            const relativePath = getRelativePath(imageFile.path);
            const newFile = await File.create({
              name: imageFile.filename,
              size: imageFile.size,
              fileType: imageFile.mimetype,
              ext: imageFile.originalname.split(".").pop(),
              location: relativePath,
              type: FileType.IMAGE,
              ownerId: userId,
            });
            newPlace.images.push(newFile._id as mongoose.Types.ObjectId);
          }
        }

        const createdPlace = await mongoose
          .model("RecommendedPlace")
          .create(newPlace);
        boatData.recommendedPlaces.push(createdPlace._id);
      }
    }

    // Process facilities (create new ones)
    if (facilities.length > 0) {
      for (const facilityData of facilities) {
        console.log(
          "Processing facility data:",
          JSON.stringify(facilityData, null, 2)
        );

        // Validate required fields
        if (
          !facilityData.name ||
          facilityData.price === undefined ||
          facilityData.price === null
        ) {
          console.error("Missing required facility data:", {
            name: facilityData.name,
            price: facilityData.price,
          });
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message:
              "Missing required fields for facility: name and price are required",
          });
        }

        const newFacility = {
          name: facilityData.name,
          price: facilityData.price,
          status: "active",
          ownerId: userId,
          images: [] as mongoose.Types.ObjectId[],
        };

        // If facility has image files, process them
        if (facilityData.imageFiles && Array.isArray(facilityData.imageFiles)) {
          for (const imageFile of facilityData.imageFiles) {
            const relativePath = getRelativePath(imageFile.path);
            const newFile = await File.create({
              name: imageFile.filename,
              size: imageFile.size,
              fileType: imageFile.mimetype,
              ext: imageFile.originalname.split(".").pop(),
              location: relativePath,
              type: FileType.IMAGE,
              ownerId: userId,
            });
            newFacility.images.push(newFile._id as mongoose.Types.ObjectId);
          }
        }

        const createdFacility = await mongoose
          .model("Facility")
          .create(newFacility);
        boatData.facilities.push(createdFacility._id);
      }
    }

    // Create the boat
    console.log("Creating boat in database...");
    console.log("Final boat data before save:", JSON.stringify({
      ...boatData,
      attachments: boatData.attachments
    }, null, 2));

    const boat = new Boat(boatData);
    await boat.save();
    console.log("Boat created with ID:", boat._id);
    console.log("Boat attachments after save:", JSON.stringify(boat.attachments, null, 2));

    // Log the creation
    console.log("Creating activity log...");
    await createActivityLog("boats", boat._id, "CREATE", [], userId);
    console.log("Activity log created");

    // Populate file references
    await boat.populate([
      {
        path: "attachments.images",
        select: "location",
      },
      {
        path: "attachments.videos",
        select: "location",
      },
      {
        path: "attachments.documents",
        select: "location",
      },
      {
        path: "recommendedPlaces",
      },
      {
        path: "facilities",
      },
    ]);

    // Process for response
    const processedBoat: any = boat.toObject();
    if (processedBoat.attachments) {
      if (processedBoat.attachments.images?.length > 0) {
        processedBoat.attachments.images = processedBoat.attachments.images.map(
          (img: any) => ({
            id: img._id,
            link: `${global.config.FILE_BASE_URL}${img.location}`
          })
        );
      }
      if (processedBoat.attachments.videos?.length > 0) {
        processedBoat.attachments.videos = processedBoat.attachments.videos.map(
          (video: any) => ({
            id: video._id,
            link: `${global.config.FILE_BASE_URL}${video.location}`
          })
        );
      }
      if (processedBoat.attachments.documents?.length > 0) {
        processedBoat.attachments.documents =
          processedBoat.attachments.documents.map(
            (doc: any) => ({
              id: doc._id,
              link: `${global.config.FILE_BASE_URL}${doc.location}`
            })
          );
      }
    }

    console.log("Sending response...");
    res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("BOAT_CREATED_SUCCESS"),
      data: processedBoat,
    });
    console.log("Response sent successfully");
  } catch (error) {
    console.error("Error in createBoat:", error);
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

export const getBoat = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_BOAT_ID"),
      });
    }

    // Use aggregation pipeline instead of findById with multiple populate calls
    const pipeline = [
      // Match stage - find boat by ID
      { $match: { _id: mongoose.Types.ObjectId.createFromHexString(id) } },

      // Add default attachments structure if missing
      {
        $addFields: {
          "attachments.images": { $ifNull: ["$attachments.images", []] },
          "attachments.videos": { $ifNull: ["$attachments.videos", []] },
          "attachments.documents": { $ifNull: ["$attachments.documents", []] },
        },
      },

      // Lookup facilities
      {
        $lookup: {
          from: "facilities",
          localField: "facilities",
          foreignField: "_id",
          as: "facilities",
        },
      },

      // Lookup recommended places
      {
        $lookup: {
          from: "recommendedplaces",
          localField: "recommendedPlaces",
          foreignField: "_id",
          as: "recommendedPlaces",
        },
      },

      // Lookup owner
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "ownerInfo",
        },
      },

      // Lookup wishlist status
      {
        $lookup: {
          from: "wishlists",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$userId", mongoose.Types.ObjectId.createFromHexString(userId)] },
                  ],
                },
              },
            },
          ],
          as: "wishlist",
        },
      },

      // Lookup booking status to check if boat is currently booked
      {
        $lookup: {
          from: "bookings",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $in: ["$status", ["Pending", "Confirmed", "Accepted"]] },
                    { $gte: ["$endDate", new Date()] }, // Only active bookings
                  ],
                },
              },
            },
          ],
          as: "activeBookings",
        },
      },

      // Lookup reviews to calculate average rating
      {
        $lookup: {
          from: "reviews",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$status", ReviewStatus.ACTIVE] },
                  ],
                },
              },
            },
          ],
          as: "reviews",
        },
      },

      // Calculate average rating
      {
        $addFields: {
          averageRating: {
            $cond: [
              { $gt: [{ $size: "$reviews" }, 0] },
              { $avg: "$reviews.rating" },
              0,
            ],
          },
          reviewCount: { $size: "$reviews" },
        },
      },

      // Unwind and restructure owner info
      {
        $addFields: {
          ownerId: { $arrayElemAt: ["$ownerInfo", 0] },
          isWishlisted: { $gt: [{ $size: "$wishlist" }, 0] },
          isBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
        },
      },

      // Lookup attachments - images
      {
        $lookup: {
          from: "files",
          let: { imageIds: { $ifNull: ["$attachments.images", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$imageIds" }, 0] },
                    { $in: ["$_id", "$$imageIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "imageFiles",
        },
      },

      // Lookup attachments - videos
      {
        $lookup: {
          from: "files",
          let: { videoIds: { $ifNull: ["$attachments.videos", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$videoIds" }, 0] },
                    { $in: ["$_id", "$$videoIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "videoFiles",
        },
      },

      // Lookup attachments - documents
      {
        $lookup: {
          from: "files",
          let: { docIds: { $ifNull: ["$attachments.documents", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$docIds" }, 0] },
                    { $in: ["$_id", "$$docIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "documentFiles",
        },
      },

      // Lookup facility images
      {
        $lookup: {
          from: "files",
          localField: "facilities.images",
          foreignField: "_id",
          as: "facilityImageFiles",
        },
      },

      // Lookup place images
      {
        $lookup: {
          from: "files",
          localField: "recommendedPlaces.images",
          foreignField: "_id",
          as: "placeImageFiles",
        },
      },

      // Lookup owner avatar
      {
        $lookup: {
          from: "files",
          localField: "ownerId.avatar",
          foreignField: "_id",
          as: "ownerAvatarFile",
        },
      },

      // Final project stage - transform data
      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          guestsCapacity: 1,
          cabins: 1,
          baths: 1,
          pricePerDay: 1,
          visualizationUrl: 1,
          description: 1,
          fullDayWithPatron: 1,
          fullDayWithoutPatron: 1,
          halfDayWithPatron: 1,
          halfDayWithoutPatron: 1,
          affiliateCode: 1,
          availability: 1,
          language: 1,
          fuel: 1,
          patron: 1,
          license: 1,
          schedule: 1,
          checkInNotes: 1,
          checkOutNotes: 1,
          location: 1,
          lat: 1,
          lng: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          isWishlisted: 1,
          isBooked: 1,
          averageRating: 1,
          reviewCount: 1,
          ownerInfo: {
            $let: {
              vars: {
                owner: { $arrayElemAt: ["$ownerInfo", 0] },
                avatar: { $arrayElemAt: ["$ownerAvatarFile", 0] },
              },
              in: {
                _id: "$$owner._id",
                name: "$$owner.name",
                email: "$$owner.email",
                currency: "$$owner.currency",
                avatarUrl: {
                  $cond: [
                    { $gt: [{ $size: "$ownerAvatarFile" }, 0] },
                    {
                      $concat: [
                        global.config.FILE_BASE_URL,
                        "$$avatar.location",
                      ],
                    },
                    null,
                  ],
                },
              },
            },
          },
          facilities: {
            $map: {
              input: "$facilities",
              as: "facility",
              in: {
                _id: "$$facility._id",
                name: "$$facility.name",
                price: "$$facility.price",
                location: "$$facility.location",
                status: "$$facility.status",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$facilityImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$facility.images"] },
                      },
                    },
                    as: "image",
                    in: {
                      id: "$$image._id",
                      link: {
                        $concat: [
                          global.config.FILE_BASE_URL,
                          "$$image.location",
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
          recommendedPlaces: {
            $map: {
              input: "$recommendedPlaces",
              as: "place",
              in: {
                _id: "$$place._id",
                name: "$$place.name",
                description: "$$place.description",
                status: "$$place.status",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$placeImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$place.images"] },
                      },
                    },
                    as: "image",
                    in: {
                      id: "$$image._id",
                      link: {
                        $concat: [
                          global.config.FILE_BASE_URL,
                          "$$image.location",
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
          attachments: {
            images: {
              $map: {
                input: "$imageFiles",
                as: "img",
                in: {
                  id: "$$img._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$img.location"],
                  },
                },
              },
            },
            videos: {
              $map: {
                input: "$videoFiles",
                as: "video",
                in: {
                  id: "$$video._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$video.location"],
                  },
                },
              },
            },
            documents: {
              $map: {
                input: "$documentFiles",
                as: "doc",
                in: {
                  id: "$$doc._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$doc.location"],
                  },
                },
              },
            },
          },
        },
      },
    ] as any[]; // Cast to any[] to avoid TypeScript pipeline errors

    const results = await Boat.aggregate(pipeline);

    // Check if boat exists
    if (!results.length) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("BOAT_NOT_FOUND"),
      });
    }

    const processedBoat = results[0];

    return res.status(200).json({
      status: "success",
      data: processedBoat,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getAllBoats = async (req: any, res: any): Promise<any> => {
  try {
    const { page = 1, limit = 10, search, fromDate, toDate, lat, lng, radius = 50, ...filters } = req.query;
    const userId = req.user._id;
    const skip = (page - 1) * limit;

    // Parse location parameters only if they exist
    const hasLocationFilter = lat && lng && !isNaN(parseFloat(lat)) && !isNaN(parseFloat(lng));
    const latitude = hasLocationFilter ? parseFloat(lat) : null;
    const longitude = hasLocationFilter ? parseFloat(lng) : null;
    const radiusInKm = hasLocationFilter ? parseFloat(radius) : null;

    const query: any = {
      status: { $eq: BoatStatus.PUBLISHED },
      ownerId: { $ne: mongoose.Types.ObjectId.createFromHexString(userId) }, // Exclude owner's own boats
    };

    // Add location constraints only if location filter is provided
    if (hasLocationFilter) {
      query.lat = { $exists: true, $ne: null, $type: "number" };
      query.lng = { $exists: true, $ne: null, $type: "number" };
    }

    // Add search filter for boat name
    if (search) {
      query.name = { $regex: search, $options: "i" };
    }

    // Add date filters for availability
    if (fromDate || toDate) {
      query["availability.start"] = {};
      query["availability.end"] = {};

      if (fromDate) {
        query["availability.start"].$lte = new Date(fromDate);
      }
      if (toDate) {
        query["availability.end"].$gte = new Date(toDate);
      }
    }

    // Add filters if provided
    if (filters.type) query.type = filters.type;
    if (filters.minPrice)
      query.pricePerDay = { $gte: Number(filters.minPrice) };
    if (filters.maxPrice) {
      query.pricePerDay = {
        ...query.pricePerDay,
        $lte: Number(filters.maxPrice),
      };
    }
    if (filters.minCapacity)
      query.guestsCapacity = { $gte: Number(filters.minCapacity) };
    if (filters.location)
      query.location = { $regex: filters.location, $options: "i" };
    if (filters.fuel !== undefined) query.fuel = filters.fuel === "true";
    if (filters.patron !== undefined) query.patron = filters.patron === "true";
    if (filters.license !== undefined)
      query.license = filters.license === "true";
    if (filters.status) query.status = filters.status;

    // Build aggregation pipeline
    const pipeline: any[] = [
      // Match stage - filter boats
      { $match: query },

      // Add default attachments structure if missing
      {
        $addFields: {
          "attachments.images": { $ifNull: ["$attachments.images", []] },
          "attachments.videos": { $ifNull: ["$attachments.videos", []] },
          "attachments.documents": { $ifNull: ["$attachments.documents", []] },
        },
      },
    ];

    // Add distance calculation if location filter is provided
    if (hasLocationFilter) {
      pipeline.push({
        $addFields: {
          distance: {
            $multiply: [
              6371, // Earth's radius in kilometers
              {
                $acos: {
                  $max: [
                    -1,
                    {
                      $min: [
                        1,
                        {
                          $add: [
                            {
                              $multiply: [
                                { $sin: { $degreesToRadians: latitude } },
                                { $sin: { $degreesToRadians: "$lat" } }
                              ]
                            },
                            {
                              $multiply: [
                                { $cos: { $degreesToRadians: latitude } },
                                { $cos: { $degreesToRadians: "$lat" } },
                                { $cos: { $degreesToRadians: { $subtract: [longitude, "$lng"] } } }
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              }
            ]
          }
        }
      });

      // Filter by distance
      pipeline.push({
        $match: { distance: { $lte: radiusInKm } }
      });
    }

    // Add sorting - distance first if location filter, then by creation date
    if (hasLocationFilter) {
      pipeline.push({ $sort: { distance: 1, createdAt: -1 } });
    } else {
      pipeline.push({ $sort: { createdAt: -1 } });
    }

    // Add pagination
    pipeline.push(
      { $skip: Number(skip) },
      { $limit: Number(limit) }
    );

    // Add remaining lookup stages
    pipeline.push(
      // Lookup facilities
      {
        $lookup: {
          from: "facilities",
          localField: "facilities",
          foreignField: "_id",
          as: "facilities",
        },
      },

      // Lookup recommended places
      {
        $lookup: {
          from: "recommendedplaces",
          localField: "recommendedPlaces",
          foreignField: "_id",
          as: "recommendedPlaces",
        },
      },

      // Lookup owner
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "ownerInfo",
        },
      },

      // Lookup wishlist status
      {
        $lookup: {
          from: "wishlists",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$userId", mongoose.Types.ObjectId.createFromHexString(userId)] },
                  ],
                },
              },
            },
          ],
          as: "wishlist",
        },
      },

      // Lookup booking status to check if boat is currently booked
      {
        $lookup: {
          from: "bookings",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $in: ["$status", ["Pending", "Confirmed", "Accepted"]] },
                    { $gte: ["$endDate", new Date()] }, // Only active bookings
                  ],
                },
              },
            },
          ],
          as: "activeBookings",
        },
      },

      // Lookup reviews to calculate average rating
      {
        $lookup: {
          from: "reviews",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$status", ReviewStatus.ACTIVE] },
                  ],
                },
              },
            },
          ],
          as: "reviews",
        },
      },

      // Calculate average rating
      {
        $addFields: {
          averageRating: {
            $cond: [
              { $gt: [{ $size: "$reviews" }, 0] },
              { $avg: "$reviews.rating" },
              0,
            ],
          },
          reviewCount: { $size: "$reviews" },
        },
      },

      // Unwind and restructure owner info
      {
        $addFields: {
          ownerId: { $arrayElemAt: ["$ownerInfo", 0] },
          isWishlisted: { $gt: [{ $size: "$wishlist" }, 0] },
          isBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
        },
      },

      // Lookup attachments - images
      {
        $lookup: {
          from: "files",
          let: { imageIds: { $ifNull: ["$attachments.images", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$imageIds" }, 0] },
                    { $in: ["$_id", "$$imageIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "imageFiles",
        },
      },

      // Lookup attachments - videos
      {
        $lookup: {
          from: "files",
          let: { videoIds: { $ifNull: ["$attachments.videos", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$videoIds" }, 0] },
                    { $in: ["$_id", "$$videoIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "videoFiles",
        },
      },

      // Lookup attachments - documents
      {
        $lookup: {
          from: "files",
          let: { docIds: { $ifNull: ["$attachments.documents", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$docIds" }, 0] },
                    { $in: ["$_id", "$$docIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "documentFiles",
        },
      },

      // Lookup facility images
      {
        $lookup: {
          from: "files",
          localField: "facilities.images",
          foreignField: "_id",
          as: "facilityImageFiles",
        },
      },

      // Lookup place images
      {
        $lookup: {
          from: "files",
          localField: "recommendedPlaces.images",
          foreignField: "_id",
          as: "placeImageFiles",
        },
      },

      // Lookup owner avatar
      {
        $lookup: {
          from: "files",
          localField: "ownerId.avatar",
          foreignField: "_id",
          as: "ownerAvatarFile",
        },
      },

      // Final project stage - transform data
      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          guestsCapacity: 1,
          cabins: 1,
          baths: 1,
          pricePerDay: 1,
          visualizationUrl: 1,
          description: 1,
          fullDayWithPatron: 1,
          fullDayWithoutPatron: 1,
          halfDayWithPatron: 1,
          halfDayWithoutPatron: 1,
          affiliateCode: 1,
          availability: 1,
          language: 1,
          fuel: 1,
          patron: 1,
          license: 1,
          schedule: 1,
          checkInNotes: 1,
          checkOutNotes: 1,
          location: 1,
          lat: 1,
          lng: 1,
          distance: hasLocationFilter ? { $round: ["$distance", 2] } : "$$REMOVE",
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          isWishlisted: 1,
          isBooked: 1,
          averageRating: 1,
          reviewCount: 1,
          ownerInfo: {
            $let: {
              vars: {
                owner: { $arrayElemAt: ["$ownerInfo", 0] },
                avatar: { $arrayElemAt: ["$ownerAvatarFile", 0] },
              },
              in: {
                _id: "$$owner._id",
                name: "$$owner.name",
                email: "$$owner.email",
                currency: "$$owner.currency",
                avatarUrl: {
                  $cond: [
                    { $gt: [{ $size: "$ownerAvatarFile" }, 0] },
                    {
                      $concat: [
                        global.config.FILE_BASE_URL,
                        "$$avatar.location",
                      ],
                    },
                    null,
                  ],
                },
              },
            },
          },
          facilities: {
            $map: {
              input: "$facilities",
              as: "facility",
              in: {
                _id: "$$facility._id",
                name: "$$facility.name",
                price: "$$facility.price",
                location: "$$facility.location",
                status: "$$facility.status",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$facilityImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$facility.images"] },
                      },
                    },
                    as: "image",
                    in: {
                      id: "$$image._id",
                      link: {
                        $concat: [
                          global.config.FILE_BASE_URL,
                          "$$image.location",
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
          recommendedPlaces: {
            $map: {
              input: "$recommendedPlaces",
              as: "place",
              in: {
                _id: "$$place._id",
                name: "$$place.name",
                description: "$$place.description",
                status: "$$place.status",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$placeImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$place.images"] },
                      },
                    },
                    as: "image",
                    in: {
                      id: "$$image._id",
                      link: {
                        $concat: [
                          global.config.FILE_BASE_URL,
                          "$$image.location",
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
          attachments: {
            images: {
              $map: {
                input: "$imageFiles",
                as: "img",
                in: {
                  id: "$$img._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$img.location"],
                  },
                },
              },
            },
            videos: {
              $map: {
                input: "$videoFiles",
                as: "video",
                in: {
                  id: "$$video._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$video.location"],
                  },
                },
              },
            },
            documents: {
              $map: {
                input: "$documentFiles",
                as: "doc",
                in: {
                  id: "$$doc._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$doc.location"],
                  },
                },
              },
            },
          },
        },
      }
    );

    const boats = await Boat.aggregate(pipeline);
    const totalBoats = await Boat.countDocuments(query);

    return res.status(200).json({
      status: true,
      data: {
        boats,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: totalBoats,
          totalPages: Math.ceil(totalBoats / Number(limit)),
        },
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const updateBoat = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { id } = req.params;

    const boat = await Boat.findById(id);

     // Parse nested form data first to extract facilities and places
     const { facilities, recommendedPlaces } = parseNestedFormData(
      req.body,
      req.files
    );

    // Create clean boat data without nested form fields
    const cleanBody = { ...req.body };

    // Remove nested form fields from body
    Object.keys(cleanBody).forEach((key) => {
      if (key.match(/^(facilities|recommendedPlaces)\[/)) {
        delete cleanBody[key];
      }
    });

    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("BOAT_NOT_FOUND"),
      });
    }

    // Check if user is the owner of the boat
    if (boat.ownerId.toString() !== userId.toString()) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("BOAT_NOT_OWNER"),
      });
    }

    const updates = {
      ...cleanBody,
      schedule: parseJSON(req.body.schedule, boat.schedule),
      availability: parseJSON(req.body.availability, boat.availability),
      language: parseJSON(req.body.language, boat.language),
      documents: parseJSON(req.body.documents, boat.attachments.documents),
      existingVideos: parseJSON(req.body.existingVideos, []),
      existingImages: parseJSON(req.body.existingImages, []),
      status: req.body.status || boat.status,
    };


    Object.keys(updates).forEach((key) => {
      if (updates[key]?.toString().trim() === "true") {
        updates[key] = true;
      } else if (updates[key]?.toString().trim() === "false") {
        updates[key] = false;
      }
    });

    if (typeof updates.documents !== "undefined") {
      updates.documents = updates.documents.map((doc: any) => doc.toString());
    }

    const { error } = updateBoatValidatorBody.validate(updates);
    if (error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: error.message,
      });
    }

    updates.recommendedPlaces = boat.recommendedPlaces || [];
    updates.facilities = boat.facilities || [];

    // Validate affiliate code if provided and changed
    if (
      updates.affiliateCode !== undefined &&
      updates.affiliateCode !== boat.affiliateCode
    ) {
      if (updates.affiliateCode) {
        const affiliateValidation =
          await affiliateCodeService.validateAffiliateCode(
            updates.affiliateCode
          );
        if (!affiliateValidation.isValid) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__(affiliateValidation.messageKey),
          });
        }
        // Normalize the affiliate code
        updates.affiliateCode = affiliateCodeService.normalizeAffiliateCode(
          updates.affiliateCode
        );
      }
    }
    // Initialize attachments object if it doesn't exist
    if (!updates.attachments) {
      updates.attachments = {
        images: boat.attachments.images || [],
        videos: boat.attachments.videos || [],
        documents: boat.attachments.documents || [],
      };
    }

    // Process images - handle both new uploads and existing images
    let finalImageIds: mongoose.Types.ObjectId[] = [];

    // First, handle existing images if provided
    if (updates.existingImages && Array.isArray(updates.existingImages)) {
      const existingImageIds = updates.existingImages
        .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
        .map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
      finalImageIds = [...existingImageIds];
    }

    // Then, handle new image uploads if any
    if (req.files && req.files.images && req.files.images.length > 0) {
      const newImageIds = await Promise.all(
        req.files.images.map(async (file: any) => {
          const relativePath = getRelativePath(file.path);

          const newFile = await File.create({
            name: file.filename,
            size: file.size,
            fileType: file.mimetype,
            ext: file.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.IMAGE,
            ownerId: userId,
          });
          return newFile._id;
        })
      );

      // Add new images to the final array
      finalImageIds = [...finalImageIds, ...newImageIds];
    }

    // Update attachments with final image array only if we have images to set or if existingImages was provided
    if (finalImageIds.length > 0 || (updates.existingImages && Array.isArray(updates.existingImages))) {
      updates.attachments.images = finalImageIds;
    }

    // Process videos - handle both new uploads and existing videos
    let finalVideoIds: mongoose.Types.ObjectId[] = [];

    // First, handle existing videos if provided
    if (updates.existingVideos && Array.isArray(updates.existingVideos)) {
      const existingVideoIds = updates.existingVideos
        .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
        .map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
      finalVideoIds = [...existingVideoIds];
    }

    // Then, handle new video uploads if any
    if (req.files && req.files.videos && req.files.videos.length > 0) {
      const newVideoIds = await Promise.all(
        req.files.videos.map(async (file: any) => {
          const relativePath = getRelativePath(file.path);

          const newFile = await File.create({
            name: file.filename,
            size: file.size,
            fileType: file.mimetype,
            ext: file.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.VIDEO,
            ownerId: userId,
          });
          return newFile._id;
        })
      );

      // Add new videos to the final array
      finalVideoIds = [...finalVideoIds, ...newVideoIds];
    }

    // Update attachments with final video array only if we have videos to set or if existingVideos was provided
    if (finalVideoIds.length > 0 || (updates.existingVideos && Array.isArray(updates.existingVideos))) {
      updates.attachments.videos = finalVideoIds;
    }
    // Process document IDs (not file uploads, just references to existing docs)
    if (updates.documents && Array.isArray(updates.documents)) {
      if (updates.documents.length > 0) {
        // Validate that all document IDs exist and belong to this user
        const documentIds = updates.documents
          .filter((id: string) => Types.ObjectId.isValid(id))
          .map((id: string) => new Types.ObjectId(id));

        if (documentIds.length === 0) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__("BOAT_INVALID_DOCUMENT_IDS"),
          });
        }

        // Check if documents exist and belong to user
        const existingDocs = await File.find({
          _id: { $in: documentIds },
          ownerId: userId,
          type: FileType.DOCUMENT,
        });

        if (existingDocs.length !== documentIds.length) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__("BOAT_INVALID_DOCUMENT_IDS"),
          });
        }

        // Set document IDs in updates
        updates.attachments.documents = documentIds;
      }
    }

    // Process recommended places
    if (recommendedPlaces) {
      const placesArray = recommendedPlaces

      if (placesArray.length > 0) {
        // Create an array to store all place IDs (new and existing)
        const updatedPlaceIds: any[] = [];

        for (const placeData of placesArray) {
          // Check if the place has an _id (existing place)
          if (placeData._id && Types.ObjectId.isValid(placeData._id)) {
            // Update existing place
            const existingPlace = await RecommendedPlace.findById(
              placeData._id
            );

            if (
              existingPlace &&
              existingPlace.ownerId.toString() === userId.toString()
            ) {
              // Update fields
              existingPlace.name = placeData.name || existingPlace.name;
              existingPlace.description =
                placeData.description || existingPlace.description;
              existingPlace.images = existingPlace.images?.length ? [ ...existingPlace.images ] : [];
              // Handle existing images if provided in placeData.existingImages
              if (placeData.existingImages && Array.isArray(placeData.existingImages)) {
                existingPlace.images = placeData.existingImages
                  .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
                  .map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
              }

              // Process new images if any
              if (placeData.images && Array.isArray(placeData.images)) {
                for (const imageFile of placeData.images) {
                  // Only process new image files (not existing image IDs)
                  if (imageFile.path) {
                    const relativePath = getRelativePath(imageFile.path);

                    const newFile = await File.create({
                      name: imageFile.filename,
                      size: imageFile.size,
                      fileType: imageFile.mimetype,
                      ext: imageFile.originalname.split(".").pop(),
                      location: relativePath,
                      type: FileType.IMAGE,
                      ownerId: userId,
                    });

                    existingPlace?.images?.push(newFile._id as any);
                  }
                }
              }

              await existingPlace.save();
              updatedPlaceIds.push(existingPlace._id);
            }
          } else {
            // Create new place
            const newPlace: {
              name: string;
              description: string;
              status: string;
              ownerId: any;
              images: mongoose.Types.ObjectId[];
            } = {
              name: placeData.name,
              description: placeData.description,
              status: "active",
              ownerId: userId,
              images: [],
            };

            // Process images
            if (placeData.images && Array.isArray(placeData.images)) {
              for (const imageFile of placeData.images) {
                if (imageFile.path) {
                  const relativePath = getRelativePath(imageFile.path);

                  const newFile = await File.create({
                    name: imageFile.filename,
                    size: imageFile.size,
                    fileType: imageFile.mimetype,
                    ext: imageFile.originalname.split(".").pop(),
                    location: relativePath,
                    type: FileType.IMAGE,
                    ownerId: userId,
                  });

                  newPlace.images.push(
                    newFile._id as any
                  );
                }
              }
            }

            const createdPlace = await RecommendedPlace.create(newPlace);
            updatedPlaceIds.push(createdPlace._id);
          }
        }

        // Replace the boat's places with the updated list
        updates.recommendedPlaces = updatedPlaceIds;
      }
    }

    // Process facilities
    if (facilities) {
      const facilitiesArray = facilities

      if (facilitiesArray.length > 0) {
        // Create an array to store all facility IDs (new and existing)
        const updatedFacilityIds: any[] = [];

        for (const facilityData of facilitiesArray) {
          // Check if the facility has an _id (existing facility)
          if (facilityData._id && Types.ObjectId.isValid(facilityData._id)) {
            // Update existing facility
            const existingFacility = await Facility.findById(facilityData._id);

            if (
              existingFacility &&
              existingFacility.ownerId.toString() === userId.toString()
            ) {
              // Update fields
              existingFacility.name =
                facilityData.name || existingFacility.name;
              existingFacility.price =
                facilityData.price || existingFacility.price;

              existingFacility.images = existingFacility.images?.length ? [ ...existingFacility.images ] : []

              // Handle existing images if provided in facilityData.existingImages
              if (facilityData.existingImages && Array.isArray(facilityData.existingImages)) {
                existingFacility.images = facilityData.existingImages
                  .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
                  .map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
              }

              // Process new images if any
              if (facilityData.images && Array.isArray(facilityData.images)) {
                for (const imageFile of facilityData.images) {
                  // Only process new image files (not existing image IDs)
                  if (imageFile.path) {
                    const relativePath = getRelativePath(imageFile.path);

                    const newFile = await File.create({
                      name: imageFile.filename,
                      size: imageFile.size,
                      fileType: imageFile.mimetype,
                      ext: imageFile.originalname.split(".").pop(),
                      location: relativePath,
                      type: FileType.IMAGE,
                      ownerId: userId,
                    });

                    existingFacility.images.push(
                      newFile._id as unknown as mongoose.Types.ObjectId
                    );
                  }
                }
              }

              await existingFacility.save();
              updatedFacilityIds.push(existingFacility._id);
            }
          } else {
            // Create new facility
            const newFacility: {
              name: string;
              price: number;
              status: string;
              ownerId: any;
              images: mongoose.Types.ObjectId[];
            } = {
              name: facilityData.name,
              price: facilityData.price,
              status: "active",
              ownerId: userId,
              images: [],
            };

            // Process images
            if (facilityData.images && Array.isArray(facilityData.images)) {
              for (const imageFile of facilityData.images) {
                if (imageFile.path) {
                  const relativePath = getRelativePath(imageFile.path);

                  const newFile = await File.create({
                    name: imageFile.filename,
                    size: imageFile.size,
                    fileType: imageFile.mimetype,
                    ext: imageFile.originalname.split(".").pop(),
                    location: relativePath,
                    type: FileType.IMAGE,
                    ownerId: userId,
                  });

                  newFacility.images.push(
                    newFile._id as unknown as mongoose.Types.ObjectId
                  );
                }
              }
            }

            const createdFacility = await Facility.create(newFacility);
            updatedFacilityIds.push(createdFacility._id);
          }
        }

        // Replace the boat's facilities with the updated list
        updates.facilities = updatedFacilityIds;
      }
    }

    // Get changes before update
    const changes = validateAndGetChanges(boat, updates);

    const updatedBoat = await Boat.findByIdAndUpdate(id, updates, { new: true })
      .populate("attachments.images")
      .populate("attachments.videos")
      .populate("attachments.documents")
      .populate("recommendedPlaces")
      .populate("facilities")
      .populate("recommendedPlaces.images")
      .populate("facilities.images");

    // Log the changes if any
    if (changes.length > 0) {
      await createActivityLog("boats", id, "UPDATE", changes, userId);
    }

    // Process for response
    const processedBoat: any = updatedBoat?.toObject();
    if (processedBoat.attachments) {
      if (processedBoat.attachments.images?.length > 0) {
        processedBoat.attachments.images = processedBoat.attachments.images.map(
          (img: any) => `${global.config.FILE_BASE_URL}${img.location}`
        );
      }
      if (processedBoat.attachments.videos?.length > 0) {
        processedBoat.attachments.videos = processedBoat.attachments.videos.map(
          (video: any) => `${global.config.FILE_BASE_URL}${video.location}`
        );
      }
      if (processedBoat.attachments.documents?.length > 0) {
        processedBoat.attachments.documents =
          processedBoat.attachments.documents.map(
            (doc: any) => `${global.config.FILE_BASE_URL}${doc.location}`
          );
      }
    }
    if (processedBoat.recommendedPlaces) {
      if (processedBoat.recommendedPlaces.images?.length > 0) {
        processedBoat.recommendedPlaces.images =
          processedBoat.recommendedPlaces.images.map(
            (img: any) => `${global.config.FILE_BASE_URL}${img.location}`
          );
      }
    }
    if (processedBoat.facilities) {
      if (processedBoat.facilities.images?.length > 0) {
        processedBoat.facilities.images = processedBoat.facilities.images.map(
          (img: any) => `${global.config.FILE_BASE_URL}${img.location}`
        );
      }
    }

    res.json({
      status: true,
      message: res.__("BOAT_UPDATED_SUCCESS"),
      data: processedBoat,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const deleteBoat = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { id } = req.params;

    const boat = await Boat.findById(id);

    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("BOAT_NOT_FOUND"),
      });
    }

    // Check if user is the owner of the boat
    if (boat.ownerId.toString() !== userId.toString()) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("BOAT_NOT_OWNER"),
      });
    }

    // Get changes before update
    const changes = validateAndGetChanges(boat, { status: BoatStatus.DELETED });

    // Soft delete by updating status
    await Boat.findByIdAndUpdate(id, { status: BoatStatus.DELETED });

    // Log the deletion
    await createActivityLog("boats", id, "DELETE", changes, userId);

    res.json({
      status: true,
      message: res.__("BOAT_DELETED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const uploadAttachment = async (req: any, res: any): Promise<any> => {
  try {
    const { id, type } = req.params;
    const file = req.files ? req.files[0] : null;

    if (!file) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "No file provided",
      });
    }

    // Check if file type is valid based on MIME type
    const isImage = file.mimetype.startsWith('image/');
    const isVideo = file.mimetype.startsWith('video/');

    if (!isImage && !isVideo) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Invalid attachment type. Only images and videos are allowed.",
      });
    }

    const boat: any = await Boat.findById(id);
    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("BOAT_NOT_FOUND"),
      });
    }

    // Check if type is valid
    if (!["images", "videos"].includes(type)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("DOCUMENT_USE_USER_API"),
      });
    }

    // Determine file type
    let fileType;
    switch (type) {
      case "images":
        fileType = FileType.IMAGE;
        break;
      case "videos":
        fileType = FileType.VIDEO;
        break;
      default:
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: "Invalid attachment type",
        });
    }

    // Store only the path after uploads/
    const relativePath = getRelativePath(file.path);

    // Create file record
    const newFile = await File.create({
      name: file.filename,
      size: file.size,
      fileType: file.mimetype,
      ext: file.originalname.split(".").pop(),
      location: relativePath,
      type: fileType,
      ownerId: req.user._id,
    });

    // Add file to the appropriate attachment array
    if (!boat.attachments) {
      boat.attachments = {
        images: [],
        videos: [],
        documents: [],
      };
    }

    boat.attachments[type].push(newFile._id);
    await boat.save();

    // Log the attachment upload
    const changes: IChange[] = [
      {
        field: `attachments.${type}`,
        old: null,
        new: newFile._id,
      },
    ];

    await createActivityLog("boats", boat._id, "UPDATE", changes, req.user._id);

    // Populate file references
    await boat.populate(`attachments.${type}`);

    // Process file URL
    const processedFile = newFile.toObject();
    const fileUrl = `${global.config.FILE_BASE_URL}${processedFile.location}`;

    res.json({
      status: true,
      message: res.__("DOCUMENT_UPLOAD_SUCCESS"),
      data: {
        file: {
          ...processedFile,
          url: fileUrl,
        },
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const setAvailability = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { id } = req.params;
    const { start, end } = req.body;

    const boat = await Boat.findById(id);

    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("BOAT_NOT_FOUND"),
      });
    }

    // Check if user is the owner of the boat
    if (boat.ownerId.toString() !== userId.toString()) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("BOAT_NOT_OWNER"),
      });
    }

    // Get changes before update
    const changes = validateAndGetChanges(boat, {
      availability: {
        start: new Date(start),
        end: new Date(end),
      },
    });

    boat.availability = {
      start: new Date(start),
      end: new Date(end),
    };

    await boat.save();

    // Log the availability change
    await createActivityLog("boats", id, "UPDATE", changes, userId);

    res.json({
      status: true,
      message: res.__("BOAT_AVAILABILITY_SET"),
      data: boat,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getMyBoats = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10, search, isBoat } = req.query;
    const skip = (page - 1) * limit;

    // Build match query
    const matchQuery: any = {
      ownerId: userId,
      status: { $ne: BoatStatus.DELETED },
      recordType: RecordType.BOAT
    };

    // Add search filter
    if (search) {
      matchQuery.name = { $regex: search, $options: "i" };
    }

    // Add type filter (boat vs activity)
    if (isBoat !== undefined) {
      matchQuery.recordType = isBoat === "true" ? RecordType.BOAT : RecordType.ACTIVITY;
    }

    // Use aggregation pipeline instead of find with multiple populate calls
    const pipeline = [
      // Match stage - filter user's boats
      { $match: matchQuery },

      // Add default attachments structure if missing
      {
        $addFields: {
          "attachments.images": { $ifNull: ["$attachments.images", []] },
          "attachments.videos": { $ifNull: ["$attachments.videos", []] },
          "attachments.documents": { $ifNull: ["$attachments.documents", []] },
        },
      },

      // Sort stage
      { $sort: { createdAt: -1 } },

      // Skip stage for pagination
      { $skip: Number(skip) },

      // Limit stage for pagination
      { $limit: Number(limit) },

      // Lookup facilities
      {
        $lookup: {
          from: "facilities",
          localField: "facilities",
          foreignField: "_id",
          as: "facilities",
        },
      },

      // Lookup recommended places
      {
        $lookup: {
          from: "recommendedplaces",
          localField: "recommendedPlaces",
          foreignField: "_id",
          as: "recommendedPlaces",
        },
      },

      // Lookup wishlist status
      {
        $lookup: {
          from: "wishlists",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$userId", mongoose.Types.ObjectId.createFromHexString(userId)] },
                  ],
                },
              },
            },
          ],
          as: "wishlist",
        },
      },

      // Lookup booking status to check if boat is currently booked
      {
        $lookup: {
          from: "bookings",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $in: ["$status", ["Pending", "Confirmed", "Accepted"]] },
                    { $gte: ["$endDate", new Date()] }, // Only active bookings
                  ],
                },
              },
            },
          ],
          as: "activeBookings",
        },
      },

      // Lookup reviews to calculate average rating
      {
        $lookup: {
          from: "reviews",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$status", ReviewStatus.ACTIVE] },
                  ],
                },
              },
            },
          ],
          as: "reviews",
        },
      },

      // Calculate average rating
      {
        $addFields: {
          averageRating: {
            $cond: [
              { $gt: [{ $size: "$reviews" }, 0] },
              { $avg: "$reviews.rating" },
              0,
            ],
          },
          reviewCount: { $size: "$reviews" },
        },
      },

      // Unwind and restructure owner info
      {
        $addFields: {
          ownerId: { $arrayElemAt: ["$ownerInfo", 0] },
          isWishlisted: { $gt: [{ $size: "$wishlist" }, 0] },
          isBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
        },
      },

      // Lookup attachments - images
      {
        $lookup: {
          from: "files",
          let: { imageIds: { $ifNull: ["$attachments.images", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$imageIds" }, 0] },
                    { $in: ["$_id", "$$imageIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "imageFiles",
        },
      },

      // Lookup attachments - videos
      {
        $lookup: {
          from: "files",
          let: { videoIds: { $ifNull: ["$attachments.videos", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$videoIds" }, 0] },
                    { $in: ["$_id", "$$videoIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "videoFiles",
        },
      },

      // Lookup attachments - documents
      {
        $lookup: {
          from: "files",
          let: { docIds: { $ifNull: ["$attachments.documents", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$docIds" }, 0] },
                    { $in: ["$_id", "$$docIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "documentFiles",
        },
      },

      // Lookup facility images
      {
        $lookup: {
          from: "files",
          localField: "facilities.images",
          foreignField: "_id",
          as: "facilityImageFiles",
        },
      },

      // Lookup place images
      {
        $lookup: {
          from: "files",
          localField: "recommendedPlaces.images",
          foreignField: "_id",
          as: "placeImageFiles",
        },
      },

      // Lookup owner information
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "ownerInfo",
        },
      },

      // Lookup owner avatar
      {
        $lookup: {
          from: "files",
          localField: "ownerInfo.avatar",
          foreignField: "_id",
          as: "ownerAvatarFile",
        },
      },

      // Final project stage - transform data
      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          guestsCapacity: 1,
          cabins: 1,
          baths: 1,
          pricePerDay: 1,
          visualizationUrl: 1,
          description: 1,
          fullDayWithPatron: 1,
          fullDayWithoutPatron: 1,
          halfDayWithPatron: 1,
          halfDayWithoutPatron: 1,
          affiliateCode: 1,
          availability: 1,
          language: 1,
          fuel: 1,
          patron: 1,
          license: 1,
          schedule: 1,
          checkInNotes: 1,
          checkOutNotes: 1,
          location: 1,
          lat: 1,
          lng: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          isWishlisted: 1,
          isBooked: 1,
          averageRating: 1,
          reviewCount: 1,
          ownerInfo: {
            $let: {
              vars: {
                owner: { $arrayElemAt: ["$ownerInfo", 0] },
                avatar: { $arrayElemAt: ["$ownerAvatarFile", 0] },
              },
              in: {
                _id: "$$owner._id",
                name: "$$owner.name",
                email: "$$owner.email",
                avatarUrl: {
                  $cond: [
                    { $gt: [{ $size: "$ownerAvatarFile" }, 0] },
                    {
                      $concat: [
                        global.config.FILE_BASE_URL,
                        "$$avatar.location",
                      ],
                    },
                    null,
                  ],
                },
              },
            },
          },
          facilities: {
            $map: {
              input: "$facilities",
              as: "facility",
              in: {
                _id: "$$facility._id",
                name: "$$facility.name",
                price: "$$facility.price",
                location: "$$facility.location",
                status: "$$facility.status",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$facilityImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$facility.images"] },
                      },
                    },
                    as: "image",
                    in: {
                      id: "$$image._id",
                      link: {
                        $concat: [
                          global.config.FILE_BASE_URL,
                          "$$image.location",
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
          recommendedPlaces: {
            $map: {
              input: "$recommendedPlaces",
              as: "place",
              in: {
                _id: "$$place._id",
                name: "$$place.name",
                description: "$$place.description",
                status: "$$place.status",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$placeImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$place.images"] },
                      },
                    },
                    as: "image",
                    in: {
                      id: "$$image._id",
                      link: {
                        $concat: [
                          global.config.FILE_BASE_URL,
                          "$$image.location",
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
          attachments: {
            images: {
              $map: {
                input: "$imageFiles",
                as: "img",
                in: {
                  id: "$$img._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$img.location"],
                  },
                },
              },
            },
            videos: {
              $map: {
                input: "$videoFiles",
                as: "video",
                in: {
                  id: "$$video._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$video.location"],
                  },
                },
              },
            },
            documents: {
              $map: {
                input: "$documentFiles",
                as: "doc",
                in: {
                  id: "$$doc._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$doc.location"],
                  },
                },
              },
            },
          },
        },
      },
    ] as any[]; // Cast to any[] to avoid TypeScript pipeline errors

    const boats = await Boat.aggregate(pipeline);
    const totalBoats = await Boat.countDocuments({
      ownerId: userId,
      status: { $ne: BoatStatus.DELETED },
    });

    return res.json({
      status: true,
      data: {
        boats,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: totalBoats,
          totalPages: Math.ceil(totalBoats / Number(limit)),
        },
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Get boat types by record type
 * @param req
 * @param res
 * @returns
 */
export const getBoatTypes = async (req: any, res: any): Promise<any> => {
  try {
    const { isBoat } = req.query;

    const whereQuery: any = {
      status: { $ne: BoatStatus.DELETED },
    };
    if (isBoat) {
      // If isBoat is provided, filter by boat type
      whereQuery.recordType =
        isBoat == "true" ? RecordType.BOAT : RecordType.ACTIVITY;
    }

    // Fetch distinct boat types from the database
    const boatTypes = await Boat.distinct("type", whereQuery);

    return res.status(StatusCodes.OK).json({
      success: true,
      data: boatTypes || [],
      message: res.__("BOAT_TYPES_FETCHED"),
    });
  } catch (error: any) {
    logger.error(`Error fetching boat types: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Get popular boat locations
 * @param req
 * @param res
 * @returns
 */
export const getPopularLocations = async (req: any, res: any): Promise<any> => {
  try {
    const whereQuery: any = {
      status: { $ne: BoatStatus.DELETED },
    };

    // Fetch distinct boat types from the database
    const boatLocations = await Boat.distinct("location", whereQuery);

    return res.status(StatusCodes.OK).json({
      success: true,
      data: boatLocations || [],
      message: res.__("BOAT_LOCATIONS_FETCHED"),
    });
  } catch (error: any) {
    logger.error(`Error fetching boat locations: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Get popular destinations based on reviews and ratings
 * @param req
 * @param res
 * @returns
 */
export const getPopularDestinations = async (req: any, res: any): Promise<any> => {
  try {
    const { limit = 10 } = req.query;

    // Import Review model
    const Review = require("../../../reviews-ms/src/models/Review").default;

    // Aggregate popular destinations based on reviews and ratings
    const pipeline = [
      {
        $match: {
          status: BoatStatus.PUBLISHED
        }
      },
      {
        $lookup: {
          from: "reviews",
          localField: "_id",
          foreignField: "boatId",
          as: "reviews"
        }
      },
      // Lookup recommended places for each boat
      {
        $lookup: {
          from: "recommendedplaces",
          localField: "recommendedPlaces",
          foreignField: "_id",
          as: "recommendedPlacesData"
        }
      },
      // Lookup images for recommended places
      {
        $lookup: {
          from: "files",
          localField: "recommendedPlacesData.images",
          foreignField: "_id",
          as: "placeImageFiles"
        }
      },
      {
        $addFields: {
          averageRating: {
            $cond: {
              if: { $gt: [{ $size: "$reviews" }, 0] },
              then: { $avg: "$reviews.rating" },
              else: 0
            }
          },
          reviewCount: { $size: "$reviews" },
          popularityScore: {
            $add: [
              { $multiply: [{ $avg: "$reviews.rating" }, 0.7] },
              { $multiply: [{ $size: "$reviews" }, 0.3] }
            ]
          },
          // Format recommended places with images
          formattedRecommendedPlaces: {
            $map: {
              input: "$recommendedPlacesData",
              as: "place",
              in: {
                _id: "$$place._id",
                name: "$$place.name",
                description: "$$place.description",
                status: "$$place.status",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$placeImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$place.images"] }
                      }
                    },
                    as: "image",
                    in: {
                      id: "$$image._id",
                      link: {
                        $concat: [
                          global.config.FILE_BASE_URL,
                          "$$image.location"
                        ]
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      {
        $group: {
          _id: "$location",
          location: { $first: "$location" },
          boatCount: { $sum: 1 },
          averageRating: { $avg: "$averageRating" },
          totalReviews: { $sum: "$reviewCount" },
          popularityScore: { $avg: "$popularityScore" },
          sampleBoats: {
            $push: {
              id: "$_id",
              name: "$name",
              type: "$type",
              pricePerDay: "$pricePerDay",
              rating: "$averageRating",
              lat: "$lat",
              lng: "$lng"
            }
          }
        }
      },
      {
        $addFields: {
          sampleBoats: { $slice: ["$sampleBoats", 3] } // Limit to 3 sample boats
        }
      },
      {
        $sort: { popularityScore: -1 as -1 }
      },
      {
        $limit: Number(limit)
      },
      {
        $project: {
          _id: 0,
          location: 1,
          lat: 1,
          lng: 1,
          boatCount: 1,
          averageRating: { $round: ["$averageRating", 1] },
          totalReviews: 1,
          popularityScore: { $round: ["$popularityScore", 1] },
          sampleBoats: 1,
          recommendedPlaces: "$formattedRecommendedPlaces"
        }
      }
    ];

    const destinations = await Boat.aggregate(pipeline);

    return res.status(StatusCodes.OK).json({
      success: true,
      data: destinations,
      message: res.__("POPULAR_DESTINATIONS_FETCHED"),
    });
  } catch (error: any) {
    logger.error(`Error fetching popular destinations: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Get boat details specifically for booking creation
 * Includes all necessary information for price calculation and booking
 * @param req
 * @param res
 * @returns
 */
export const getBoatForBooking = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_BOAT_ID"),
      });
    }

    // Comprehensive aggregation pipeline for booking requirements
    const pipeline = [
      // Match stage - find boat by ID and ensure it's published
      {
        $match: {
          _id: mongoose.Types.ObjectId.createFromHexString(id),
          status: BoatStatus.PUBLISHED
        }
      },

      // Add default attachments structure if missing
      {
        $addFields: {
          "attachments.images": { $ifNull: ["$attachments.images", []] },
          "attachments.videos": { $ifNull: ["$attachments.videos", []] },
          "attachments.documents": { $ifNull: ["$attachments.documents", []] },
        },
      },

      // Lookup facilities with full details (required for price calculation)
      {
        $lookup: {
          from: "facilities",
          localField: "facilities",
          foreignField: "_id",
          as: "facilitiesDetails",
        },
      },

      // Lookup owner details (required for currency conversion)
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "ownerInfo",
        },
      },

      // Lookup owner avatar
      {
        $lookup: {
          from: "files",
          localField: "ownerInfo.avatar",
          foreignField: "_id",
          as: "ownerAvatarFile",
        },
      },

      // Lookup boat images for display
      {
        $lookup: {
          from: "files",
          let: { imageIds: { $ifNull: ["$attachments.images", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$imageIds" }, 0] },
                    { $in: ["$_id", "$$imageIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "imageFiles",
        },
      },

      // Lookup facility images
      {
        $lookup: {
          from: "files",
          localField: "facilitiesDetails.images",
          foreignField: "_id",
          as: "facilityImageFiles",
        },
      },

      // Check if boat is currently booked for any dates
      {
        $lookup: {
          from: "bookings",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $in: ["$status", ["Pending", "Confirmed", "Accepted", "ReadyForPayment"]] },
                    { $gte: ["$endDate", new Date()] }, // Only future/active bookings
                  ],
                },
              },
            },
            {
              $project: {
                startDate: 1,
                endDate: 1,
                status: 1
              }
            }
          ],
          as: "activeBookings",
        },
      },

      // Lookup reviews for rating calculation
      {
        $lookup: {
          from: "reviews",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$status", ReviewStatus.ACTIVE] },
                  ],
                },
              },
            },
          ],
          as: "reviews",
        },
      },

      // Calculate average rating
      {
        $addFields: {
          averageRating: {
            $cond: [
              { $gt: [{ $size: "$reviews" }, 0] },
              { $avg: "$reviews.rating" },
              0,
            ],
          },
          reviewCount: { $size: "$reviews" },
        },
      },

      // Final project stage - format response for booking
      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          recordType: 1,
          guestsCapacity: 1,
          cabins: 1,
          baths: 1,
          pricePerDay: 1,
          price: 1, // For activities
          description: 1,
          location: 1,
          lat: 1,
          lng: 1,

          // Pricing fields required for booking calculation
          fullDayWithPatron: 1,
          fullDayWithoutPatron: 1,
          halfDayWithPatron: 1,
          halfDayWithoutPatron: 1,

          // Affiliate information
          affiliateCode: 1,

          // Availability information
          availability: 1,
          schedule: 1,

          // Boat features
          language: 1,
          fuel: 1,
          patron: 1,
          license: 1,

          // Notes
          checkInNotes: 1,
          checkOutNotes: 1,

          // Status and dates
          status: 1,
          createdAt: 1,
          updatedAt: 1,

          // Owner information (formatted)
          owner: {
            $let: {
              vars: {
                ownerData: { $arrayElemAt: ["$ownerInfo", 0] },
                avatarFile: { $arrayElemAt: ["$ownerAvatarFile", 0] }
              },
              in: {
                _id: "$$ownerData._id",
                firstName: "$$ownerData.firstName",
                lastName: "$$ownerData.lastName",
                email: "$$ownerData.email",
                currency: "$$ownerData.currency",
                avatar: {
                  $cond: [
                    { $ne: ["$$avatarFile", null] },
                    {
                      id: "$$avatarFile._id",
                      link: { $concat: [global.config.FILE_BASE_URL, "$$avatarFile.location"] }
                    },
                    null
                  ]
                }
              }
            }
          },

          // Facilities with full details (required for booking)
          facilities: {
            $map: {
              input: "$facilitiesDetails",
              as: "facility",
              in: {
                _id: "$$facility._id",
                name: "$$facility.name",
                price: "$$facility.price",
                description: "$$facility.description",
                status: "$$facility.status",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$facilityImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$facility.images"] }
                      }
                    },
                    as: "img",
                    in: {
                      id: "$$img._id",
                      link: { $concat: [global.config.FILE_BASE_URL, "$$img.location"] }
                    }
                  }
                }
              }
            }
          },

          // Boat images
          images: {
            $map: {
              input: "$imageFiles",
              as: "img",
              in: {
                id: "$$img._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$$img.location"] }
              }
            }
          },

          // Booking availability status
          isCurrentlyBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
          activeBookings: "$activeBookings",

          // Rating information
          averageRating: { $round: ["$averageRating", 1] },
          reviewCount: 1,
        },
      },
    ];

    const result = await Boat.aggregate(pipeline);

    if (!result || result.length === 0) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("BOAT_NOT_FOUND_OR_NOT_AVAILABLE"),
      });
    }

    const boat = result[0];

    // Additional validation for booking requirements
    if (!boat.owner || !boat.owner._id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("BOAT_OWNER_NOT_FOUND"),
      });
    }

    // Check if user is trying to book their own boat
    if (boat.owner._id.toString() === userId.toString()) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_BOOK_OWN_BOAT"),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("BOAT_FETCH_SUCCESS"),
      data: boat,
    });
  } catch (error: any) {
    logger.error(`Error fetching boat for booking: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Get nearby boats and activities based on geolocation
 * @param req
 * @param res
 * @returns
 */
export const getNearbyBoats = async (req: any, res: any): Promise<any> => {
  try {
    const { lat, lng, radius = 50, page = 1, limit = 10, isBoat } = req.query;
    const userId = req.user._id;
    const skip = (page - 1) * limit;

    if (!lat || !lng) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("LAT_LNG_REQUIRED"),
      });
    }

    const latitude = parseFloat(lat);
    const longitude = parseFloat(lng);
    const radiusInKm = parseFloat(radius);

    // Build match query
    const matchQuery: any = {
      status: BoatStatus.PUBLISHED,
      ownerId: { $ne: userId }, // Exclude owner's own boats
      lat: { $exists: true, $ne: null },
      lng: { $exists: true, $ne: null },
    };

    // Add type filter (boat vs activity)
    if (isBoat !== undefined) {
      matchQuery.recordType = isBoat === "true" ? RecordType.BOAT : RecordType.ACTIVITY;
    }

    // Aggregation pipeline with geospatial query
    const pipeline: any = [
      { $match: matchQuery },
      {
        $addFields: {
          distance: {
            $multiply: [
              6371, // Earth's radius in kilometers
              {
                $acos: {
                  $add: [
                    {
                      $multiply: [
                        { $sin: { $multiply: [{ $degreesToRadians: latitude }, 1] } },
                        { $sin: { $multiply: [{ $degreesToRadians: "$lat" }, 1] } }
                      ]
                    },
                    {
                      $multiply: [
                        { $cos: { $multiply: [{ $degreesToRadians: latitude }, 1] } },
                        { $cos: { $multiply: [{ $degreesToRadians: "$lat" }, 1] } },
                        { $cos: { $multiply: [{ $degreesToRadians: { $subtract: [longitude, "$lng"] } }, 1] } }
                      ]
                    }
                  ]
                }
              }
            ]
          }
        }
      },
      { $match: { distance: { $lte: radiusInKm } } },
      { $sort: { distance: 1 } },
      {
        $lookup: {
          from: "files",
          localField: "images",
          foreignField: "_id",
          as: "imageFiles"
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "owner"
        }
      },
      {
        $lookup: {
          from: "reviews",
          localField: "_id",
          foreignField: "boatId",
          as: "reviews"
        }
      },
      {
        $lookup: {
          from: "bookings",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $in: ["$status", ["Pending", "Confirmed", "Accepted"]] },
                    { $gte: ["$endDate", new Date()] }, // Only active bookings
                  ],
                },
              },
            },
          ],
          as: "activeBookings",
        },
      },
      {
        $unwind: { path: "$owner", preserveNullAndEmptyArrays: true }
      },
      {
        $addFields: {
          averageRating: {
            $cond: {
              if: { $gt: [{ $size: "$reviews" }, 0] },
              then: { $avg: "$reviews.rating" },
              else: 0
            }
          },
          reviewCount: { $size: "$reviews" },
          isBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
          images: {
            $map: {
              input: { $slice: ["$imageFiles", 3] }, // Limit to first 3 images
              as: "img",
              in: {
                id: "$$img._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$$img.location"] }
              }
            }
          },
          ownerProfile: {
            id: "$owner._id",
            username: "$owner.username",
            email: "$owner.email"
          }
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          recordType: 1,
          pricePerDay: 1,
          guestsCapacity: 1,
          location: 1,
          lat: 1,
          lng: 1,
          distance: { $round: ["$distance", 2] },
          images: 1,
          averageRating: { $round: ["$averageRating", 1] },
          reviewCount: 1,
          isBooked: 1,
          ownerProfile: 1,
          createdAt: 1
        }
      },
      {
        $facet: {
          data: [
            { $skip: skip },
            { $limit: Number(limit) }
          ],
          metadata: [
            { $count: "total" }
          ]
        }
      }
    ];

    const result = await Boat.aggregate(pipeline);
    const boats = result[0].data;
    const total = result[0].metadata[0]?.total || 0;

    return res.status(StatusCodes.OK).json({
      success: true,
      data: {
        boats,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: total,
          totalPages: Math.ceil(total / Number(limit))
        },
        searchCenter: {
          lat: latitude,
          lng: longitude,
          radius: radiusInKm
        }
      },
      message: res.__("NEARBY_BOATS_FETCHED"),
    });
  } catch (error: any) {
    logger.error(`Error fetching nearby boats: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

// Helper function to create a place with images
const createPlaceWithImages = async ( 
  placeData: any,
  fileIdsByKey: Map<string, mongoose.Types.ObjectId[]>
) => {
  const newPlace = {
    name: placeData.name,
    description: placeData.description,
    lat: placeData.lat,
    lng: placeData.lng,
    images: [] as mongoose.Types.ObjectId[],
    status: "active",
  };

  // Process place images if available
  if (placeData.images && Array.isArray(placeData.images)) {
    const imageKeys = placeData.images.map((img: string) => img);

    // Find image IDs that match the provided keys
    for (const key of imageKeys) {
      const fileIds = fileIdsByKey.get(key);
      if (fileIds && fileIds.length > 0) {
        newPlace.images.push(fileIds[0]); // Use the first file ID
      }
    }
  }

  return await mongoose.model("RecommendedPlace").create(newPlace);
};

// Helper function to create a facility with images
const createFacilityWithImages = async (
  facilityData: any,
  fileIdsByKey: Map<string, mongoose.Types.ObjectId[]>
) => {
  const newFacility = {
    name: facilityData.name,
    description: facilityData.description,
    images: [] as mongoose.Types.ObjectId[],
    status: "active",
  };

  // Process facility images if available
  if (facilityData.images && Array.isArray(facilityData.images)) {
    const imageKeys = facilityData.images.map((img: string) => img);

    // Find image IDs that match the provided keys
    for (const key of imageKeys) {
      const fileIds = fileIdsByKey.get(key);
      if (fileIds && fileIds.length > 0) {
        newFacility.images.push(fileIds[0]); // Use the first file ID
      }
    }
  }

  return await mongoose.model("Facility").create(newFacility);
};

/**
 * Add images to a boat
 * @param req
 * @param res
 * @returns
 */
export const addBoatImages = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = req.user._id;
    const files = req.files;

    if (!files || files.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("NO_FILES_UPLOADED"),
      });
    }

    // Find the boat and verify ownership
    const boat = await Boat.findOne({
      _id: id,
      ownerId: userId,
      status: { $ne: BoatStatus.DELETED },
    });

    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("BOAT_NOT_FOUND"),
      });
    }

    // Save files and get their IDs
    const fileIds: mongoose.Types.ObjectId[] = [];

    for (const file of files) {
      // Store only the path after uploads/
      const relativePath = getRelativePath(file.path);

      const newFile = await File.create({
        name: file.filename,
        size: file.size,
        fileType: file.mimetype,
        ext: file.originalname.split(".").pop(),
        location: relativePath,
        type: FileType.IMAGE,
        ownerId: userId,
      });
      fileIds.push(newFile._id as mongoose.Types.ObjectId);
    }

    // Add images to boat
    boat.attachments.images.push(...fileIds);
    await boat.save();

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("BOAT_IMAGES_ADDED"),
      data: {
        addedImages: fileIds.length,
        totalImages: boat.attachments.images.length,
      },
    });
  } catch (error: any) {
    logger.error(`Error adding boat images: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Remove images from a boat
 * @param req
 * @param res
 * @returns
 */
export const removeBoatImages = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const { imageIds } = req.body;
    const userId = req.user._id;

    if (!imageIds || !Array.isArray(imageIds) || imageIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("IMAGE_IDS_REQUIRED"),
      });
    }

    // Find the boat and verify ownership
    const boat = await Boat.findOne({
      _id: id,
      ownerId: userId,
      status: { $ne: BoatStatus.DELETED },
    });

    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("BOAT_NOT_FOUND"),
      });
    }

    // Validate that image IDs exist in boat's images
    const objectIdImageIds = imageIds.map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
    const validImageIds = objectIdImageIds.filter((removeId) =>
      boat.attachments.images.some((imageId) => removeId.equals(imageId))
    );

    if (validImageIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("INVALID_IMAGE_IDS"),
      });
    }

    // Remove images from boat
    boat.attachments.images = boat.attachments.images.filter(
      (imageId) => !validImageIds.some((removeId) => removeId.equals(imageId))
    );

    await boat.save();

    // Optionally delete file records (soft delete by updating status)
    await File.updateMany(
      { _id: { $in: validImageIds } },
      { status: "deleted" }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("BOAT_IMAGES_REMOVED"),
      data: {
        removedImages: validImageIds.length,
        totalImages: boat.attachments.images.length,
      },
    });
  } catch (error: any) {
    logger.error(`Error removing boat images: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Remove videos from a boat
 * @param req
 * @param res
 * @returns
 */
export const removeBoatVideos = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const { videoIds } = req.body;
    const userId = req.user._id;

    if (!videoIds || !Array.isArray(videoIds) || videoIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("VIDEO_IDS_REQUIRED"),
      });
    }

    // Find the boat and verify ownership
    const boat = await Boat.findOne({
      _id: id,
      ownerId: userId,
      status: { $ne: BoatStatus.DELETED },
    });

    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("BOAT_NOT_FOUND"),
      });
    }

    // Validate that video IDs exist in boat's videos
    const objectIdVideoIds = videoIds.map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
    const validVideoIds = objectIdVideoIds.filter((removeId) =>
      boat.attachments.videos.some((videoId) => removeId.equals(videoId))
    );

    if (validVideoIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("INVALID_VIDEO_IDS"),
      });
    }

    // Remove videos from boat
    boat.attachments.videos = boat.attachments.videos.filter(
      (videoId) => !validVideoIds.some((removeId) => removeId.equals(videoId))
    );

    await boat.save();

    // Optionally delete file records (soft delete by updating status)
    await File.updateMany(
      { _id: { $in: validVideoIds } },
      { status: "deleted" }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("BOAT_VIDEOS_REMOVED"),
      data: {
        removedVideos: validVideoIds.length,
        totalVideos: boat.attachments.videos.length,
      },
    });
  } catch (error: any) {
    logger.error(`Error removing boat videos: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Get owner activities (separate from boats)
 * @param req
 * @param res
 * @returns
 */
export const getOwnerActivities = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10, search } = req.query;
    const skip = (page - 1) * limit;

    // Build match query for activities only
    const matchQuery: any = {
      ownerId: userId,
      status: { $ne: BoatStatus.DELETED },
      recordType: RecordType.ACTIVITY
    };

    // Add search filter
    if (search) {
      matchQuery.name = { $regex: search, $options: "i" };
    }

    // Use aggregation pipeline for activities
    const pipeline = [
      { $match: matchQuery },
      {
        $lookup: {
          from: "files",
          let: { imageIds: { $ifNull: ["$attachments.images", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$imageIds" }, 0] },
                    { $in: ["$_id", "$$imageIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "imageFiles"
        }
      },
      {
        $lookup: {
          from: "reviews",
          localField: "_id",
          foreignField: "boatId",
          as: "reviews"
        }
      },
      {
        $lookup: {
          from: "bookings",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $in: ["$status", ["Pending", "Confirmed", "Accepted"]] },
                    { $gte: ["$endDate", new Date()] }, // Only active bookings
                  ],
                },
              },
            },
          ],
          as: "activeBookings",
        },
      },
      {
        $addFields: {
          averageRating: {
            $cond: {
              if: { $gt: [{ $size: "$reviews" }, 0] },
              then: { $avg: "$reviews.rating" },
              else: 0
            }
          },
          reviewCount: { $size: "$reviews" },
          isBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
          images: {
            $map: {
              input: { $slice: ["$imageFiles", 3] },
              as: "img",
              in: {
                id: "$$img._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$$img.location"] }
              }
            }
          }
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          recordType: 1,
          price: 1,
          duration: 1,
          guestsCapacity: 1,
          location: 1,
          description: 1,
          safetyInstructions: 1,
          images: 1,
          lat: 1,
          lng: 1,
          affiliateCode: 1,
          availability: 1,
          averageRating: { $round: ["$averageRating", 1] },
          reviewCount: 1,
          isBooked: 1,
          status: 1,
          createdAt: 1
        }
      },
      { $sort: { createdAt: -1 as -1 } },
      {
        $facet: {
          data: [
            { $skip: skip },
            { $limit: Number(limit) }
          ],
          metadata: [
            { $count: "total" }
          ]
        }
      }
    ];

    const result = await Boat.aggregate(pipeline);
    const activities = result[0].data;
    const total = result[0].metadata[0]?.total || 0;

    return res.status(StatusCodes.OK).json({
      success: true,
      data: {
        activities,
        pagination: {
          total,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(total / Number(limit))
        }
      },
      message: res.__("OWNER_ACTIVITIES_FETCHED"),
    });
  } catch (error: any) {
    logger.error(`Error fetching owner activities: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};
