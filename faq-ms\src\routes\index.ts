import { Router } from "express";
import {
  createFAQ,
  getAllFAQs,
  getFAQById,
  updateFAQ,
  deleteFAQ,
} from "../controller";
import {
  createFAQValidator,
  updateFAQValidator,
  getFAQValidator,
  deleteFAQValidator,
} from "../validator/faq.validator";
import { adminAccess } from "../../../shared/middleware/admin";

const routes = Router();

// Public route - Get all FAQs
routes.get("/", getAllFAQs as any);

// Public route - Get a specific FAQ by ID
routes.get("/:id", getFAQValidator, getFAQById as any);

// Admin routes - protected with adminAccess middleware
routes.post("/", adminAccess, createFAQValidator, createFAQ as any);
routes.put(
  "/:id",
  adminAccess,
  getFAQValidator,
  updateFAQValidator,
  updateFAQ as any
);
routes.delete("/:id", adminAccess, deleteFAQValidator, deleteFAQ as any);

export default routes;
