import { Router } from "express";
import {
  getNotifications,
  mark<PERSON><PERSON><PERSON>,
  markAll<PERSON><PERSON><PERSON>,
} from "../controller/notification.controller";
import {
  testFirebaseNotification,
  checkFirebaseConfig,
  validateFCMToken,
} from "../controller/test.controller";
import {
  getNotificationsValidator,
  markAsR<PERSON>Validator,
} from "../validator/notification.validator";

const routes = Router();

// Notification management routes
routes.get("/list", getNotificationsValidator, getNotifications);
routes.put("/:notificationId/read", markAsReadValidator, markAsRead);
routes.put("/read-all", markAllAsRead);

// Firebase testing routes (for development/testing purposes)
routes.post("/test/firebase", testFirebaseNotification);
routes.get("/test/firebase/config", checkFirebaseConfig);
routes.post("/test/firebase/validate-token", validateFCMToken);

export default routes;
