import Booking, {
  BookingStatus,
  PatronType,
  BookingDuration,
} from "../models/Booking";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";
import Boat, { BoatStatus, RecordType } from "../../../boat-ms/src/models/Boat";
import Facility, { FacilityStatus } from "../../../boat-ms/src/models/Facility";
import logger from "../../../shared/services/logger.service";
import { BookingNotificationService } from "../services/notification.service";
import mongoose from "mongoose";
import { StatusCodes } from "http-status-codes";
import CommissionRate from "../models/CommissionRate";
import { CommissionRateStatus } from "../models/CommissionRate";
import User, { UserCurrency } from "../../../user-ms/src/models/User";
import affiliateCodeService from "../../../shared/services/affiliateCode.service";
import StripeService from "../../../payment-ms/src/services/stripe.service";

// Currency conversion rates (in a real app, these would come from an API)
const CURRENCY_CONVERSION_RATES = {
  [UserCurrency.USD]: 1.0, // Base currency
  [UserCurrency.GBP]: 0.78, // 1 USD = 0.78 GBP
  [UserCurrency.EUR]: 0.92, // 1 USD = 0.92 EUR
};

/**
 * Convert amount from one currency to another
 * @param amount The amount to convert
 * @param fromCurrency The source currency
 * @param toCurrency The target currency
 * @returns The converted amount
 */
const convertCurrency = (
  amount: number,
  fromCurrency: UserCurrency,
  toCurrency: UserCurrency
): number => {
  if (fromCurrency === toCurrency) {
    return amount;
  }

  // Convert to USD first (as base currency)
  const amountInUSD =
    fromCurrency === UserCurrency.USD
      ? amount
      : amount / CURRENCY_CONVERSION_RATES[fromCurrency];

  // Then convert from USD to target currency
  const convertedAmount =
    toCurrency === UserCurrency.USD
      ? amountInUSD
      : amountInUSD * CURRENCY_CONVERSION_RATES[toCurrency];

  return parseFloat(convertedAmount.toFixed(2));
};

// Create a new booking
export const createBooking = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const {
      boatId,
      startDate,
      endDate,
      location,
      duration,
      patronType,
      extraFacilities,
      referralName,
    } = req.body;

    // Get renter (current user) details including currency preference
    const renter = await User.findById(userId);
    if (!renter) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("USER_NOT_FOUND"),
      });
    }

    // Convert renter currency to UserCurrency enum
    const renterCurrency = renter.currency as UserCurrency;
    if (!Object.values(UserCurrency).includes(renterCurrency)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("INVALID_CURRENCY"),
      });
    }

    // Check if boat exists and is available
    const boat: any = await Boat.findOne({
      _id: boatId,
      status: BoatStatus.PUBLISHED,
    }).populate("ownerId").populate("facilities");

    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOAT_NOT_FOUND_OR_NOT_AVAILABLE"),
      });
    }

    // Handle affiliateCode for both boats and activities with validation
    let affiliateCode = null;
    let referralAffiliateCode = null;
    let finalAffiliateCode = null;

    // First, check if boat has an affiliate code
    if (boat.affiliateCode) {
      // Validate the affiliate code exists and is active
      try {
        const affiliateValidation =
          await affiliateCodeService.validateAffiliateCode(boat.affiliateCode);
        if (!affiliateValidation.isValid) {
          logger.warn(
            `Invalid affiliate code found on boat ${boatId}: ${boat.affiliateCode}`,
            {
              boatId,
              affiliateCode: boat.affiliateCode,
              reason: affiliateValidation.messageKey,
            }
          );
          // Don't fail the booking, but log the issue and don't use the affiliate code
          affiliateCode = null;
        } else {
          affiliateCode = affiliateCodeService.normalizeAffiliateCode(
            boat.affiliateCode
          );
          logger.info(
            `Valid affiliate code found for booking: ${affiliateCode}`,
            {
              boatId,
              affiliateCode,
            }
          );
        }
      } catch (error) {
        logger.error(
          `Error validating affiliate code for boat ${boatId}:`,
          error
        );
        // Don't fail the booking, but don't use the affiliate code
        affiliateCode = null;
      }
    }

    // Second, check if referral name is provided and validate it
    if (referralName) {
      try {
        const referralValidation =
          await affiliateCodeService.validateReferralName(referralName);
        if (referralValidation.isValid && referralValidation.affiliateCode) {
          referralAffiliateCode = referralValidation.affiliateCode;
          logger.info(
            `Valid referral name found: ${referralName} -> ${referralAffiliateCode}`,
            {
              boatId,
              referralName,
              referralAffiliateCode,
              affiliateName: referralValidation.affiliateName,
            }
          );
        } else {
          logger.warn(`Invalid referral name provided: ${referralName}`, {
            boatId,
            referralName,
            reason: referralValidation.messageKey,
          });
        }
      } catch (error) {
        logger.error(`Error validating referral name ${referralName}:`, error);
      }
    }

    // Determine final affiliate code to use
    // Priority: Referral name affiliate code > Boat affiliate code
    if (referralAffiliateCode) {
      finalAffiliateCode = referralAffiliateCode;
      logger.info(
        `Using referral affiliate code for booking: ${finalAffiliateCode}`,
        { boatId, referralName, finalAffiliateCode }
      );
    } else if (affiliateCode) {
      finalAffiliateCode = affiliateCode;
      logger.info(
        `Using boat affiliate code for booking: ${finalAffiliateCode}`,
        { boatId, finalAffiliateCode }
      );
    } else {
      console.log(`No valid affiliate code found for booking`, {
        boatId,
        referralName,
      });
    }

    // Get boat owner details including currency preference
    const boatOwner = boat.ownerId

    if (!boatOwner || !boatOwner._id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("BOAT_OWNER_NOT_FOUND"),
      });
    }

    // Convert boat owner currency to UserCurrency enum
    const ownerCurrency = boatOwner.currency as UserCurrency;

    // Format dates
    const requestedStartDate = new Date(startDate);
    const requestedEndDate = new Date(endDate);

    // Validate date format
    if (
      isNaN(requestedStartDate.getTime()) ||
      isNaN(requestedEndDate.getTime())
    ) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("BOOKING_INVALID_DATE_FORMAT"),
      });
    }

    // Check if the boat is already booked for the requested dates
    const existingBooking = await Booking.findOne({
      boatId,
      $or: [
        {
          startDate: { $lte: requestedEndDate },
          endDate: { $gte: requestedStartDate },
        },
      ],
      status: {
        $in: [
          BookingStatus.Accepted,
          BookingStatus.Pending,
          BookingStatus.ReadyForPayment,
        ],
      },
    });

    if (existingBooking) {
      return res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: req.__("BOOKING_BOAT_ALREADY_BOOKED"),
      });
    }

    // Process extra facilities
    let extraFacilitiesData: Array<{
      facilityId: mongoose.Types.ObjectId;
      name: string;
      price: number;
      quantity: number;
    }> = [];

    console.log(extraFacilities)
    if (extraFacilities && extraFacilities.length > 0) {
      extraFacilitiesData = boat.facilities.map((facility: any) => {
        const requestedFacility = extraFacilities.find(
          (f: any) => f.facilityId.toString() === facility._id.toString()
        );
        return {
          facilityId: facility._id,
          name: facility.name,
          price: facility.price,
          quantity: requestedFacility?.quantity || 1,
        };
      });
    }
    // Get base price based on patron type (only for boats, not activities)
    let basePrice = 0;
    if (boat.recordType === RecordType.BOAT) {
      // For boats, patronType is required
      if (!patronType) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: req.__("BOOKING_PATRON_TYPE_REQUIRED_FOR_BOATS"),
        });
      }

      switch (patronType) {
        case PatronType.FullDayWithPatron:
          basePrice = boat.fullDayWithPatron;
          break;
        case PatronType.FullDayWithoutPatron:
          basePrice = boat.fullDayWithoutPatron;
          break;
        case PatronType.HalfDayWithPatron:
          basePrice = boat.halfDayWithPatron;
          break;
        case PatronType.HalfDayWithoutPatron:
          basePrice = boat.halfDayWithoutPatron;
          break;
        default:
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: req.__("BOOKING_INVALID_PATRON_TYPE"),
          });
      }
    }

    // Calculate amounts
    let totalAmount = 0;
    try {
      // Validate duration calculation
      if (requestedEndDate <= requestedStartDate) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: req.__("BOOKING_END_DATE_MUST_BE_AFTER_START_DATE"),
        });
      }

      const durationInDays = Math.ceil(
        (requestedEndDate.getTime() - requestedStartDate.getTime()) /
          (1000 * 60 * 60 * 24)
      );

      if (durationInDays <= 0) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: req.__("BOOKING_INVALID_DURATION"),
        });
      }

      // Calculate total amount based on record type
      if (boat.recordType === RecordType.BOAT) {
        // For boats: use patron type pricing + pricePerDay + facilities
        totalAmount = calculateTotalAmount(
          basePrice,
          extraFacilitiesData,
          durationInDays
        );

        if (isNaN(Number(boat.pricePerDay)) || Number(boat.pricePerDay) < 0) {
          throw new Error("Invalid boat price per day");
        }
        totalAmount += Number(boat.pricePerDay) * durationInDays;
      } else {
        // For activities: use activity price + facilities (no patron type pricing)
        if (isNaN(Number(boat.price)) || Number(boat.price) < 0) {
          throw new Error("Invalid activity price");
        }

        // Calculate facilities cost
        const facilitiesCost = extraFacilitiesData.reduce((sum, facility) => {
          return sum + (facility.price * facility.quantity);
        }, 0);

        // For activities, price is per session, not per day
        totalAmount = Number(boat.price) + facilitiesCost;
      }

      // Handle currency conversion if boat owner and renter have different currencies
      // Prices in the boat model are stored in the owner's currency
      const targetCurrency = renterCurrency; // Show the renter prices in their preferred currency
      const ownerActualCurrency = Object.values(UserCurrency).includes(
        ownerCurrency
      )
        ? ownerCurrency
        : UserCurrency.USD; // Default to USD if owner currency is invalid

      // Convert the total amount to the renter's currency
      if (ownerActualCurrency !== targetCurrency) {
        // Convert to renter's currency
        totalAmount = convertCurrency(
          totalAmount,
          ownerActualCurrency,
          targetCurrency
        );
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: req.__("BOOKING_CALCULATION_ERROR"),
          error: error.message,
        });
      }
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("BOOKING_CALCULATION_ERROR"),
        error: "Unknown calculation error",
      });
    }

    // Get commission rates
    const commissionRate = await CommissionRate.findOne({
      status: CommissionRateStatus.ACTIVE,
    });

    // Use default rates if no commission rate is set
    const adminFeePercentage = commissionRate ? commissionRate.adminFee : 10;
    const adminFee = (totalAmount * adminFeePercentage) / 100;

    // Net amount is what the boat owner will receive
    const netAmount = totalAmount - adminFee;

    // Create booking
    const booking = new Booking({
      boatId,
      userId,
      startDate,
      endDate,
      location,
      duration: duration || BookingDuration.FullDay,
      extraFacilities: extraFacilitiesData,
      patronType: boat.recordType === RecordType.BOAT ? patronType : PatronType.FullDayWithoutPatron, // Default for activities
      referralName,
      totalAmount,
      adminFee,
      netAmount,
      status: BookingStatus.Pending,
      currency: renterCurrency, // Store the currency used for this booking
      ownerCurrency: ownerCurrency, // Store the owner's currency for later conversions
      affiliateCode: finalAffiliateCode,
    });

    await booking.save();

    const changes = validateAndGetChanges({}, booking.toJSON());
    // Log the creation
    await createActivityLog("bookings", booking._id, "CREATE", changes, userId);

    // Send notification to boat owner
    await BookingNotificationService.sendBookingNotification(booking, boat);

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: req.__("BOOKING_CREATED_SUCCESS"),
      data: JSON.parse(JSON.stringify(booking)),
    });
  } catch (error) {
    console.log(error)
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get my bookings
export const getMyBookings = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status as string;
    const skip = (page - 1) * limit;

    // Using aggregate to fetch bookings with boat and facilities details with pagination
    const aggregation = [
      {
        $match: { userId: new mongoose.Types.ObjectId(userId) },
      },
      // Add status filter based on query parameter
      ...(status === "Accepted"
        ? [
            {
              $match: {
                status: { $in: ["Accepted", "ReadyForPayment"] },
              },
            },
          ]
        : status === "Rejected"
          ? [
              {
                $match: {
                  status: "Rejected",
                },
              },
            ]
          : status != ""
          ? [
            {
              $match: {
                status: status
              }
            }
          ]: []),
      {
        $lookup: {
          from: "boats",
          localField: "boatId",
          foreignField: "_id",
          as: "boat",
        },
      },
      {
        $unwind: {
          path: "$boat",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Lookup boat images
      {
        $lookup: {
          from: "files",
          localField: "boat.attachments.images",
          foreignField: "_id",
          as: "imageFiles",
        },
      },
      // Lookup boat owner details
      {
        $lookup: {
          from: "users",
          localField: "boat.ownerId",
          foreignField: "_id",
          as: "boatOwnerInfo",
        },
      },
      // Lookup boat owner avatar
      {
        $lookup: {
          from: "files",
          localField: "boatOwnerInfo.avatar",
          foreignField: "_id",
          as: "boatOwnerAvatarFile",
        },
      },
      // Lookup wishlist status for the boat
      {
        $lookup: {
          from: "wishlists",
          let: { boatId: "$boat._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$userId", new mongoose.Types.ObjectId(userId)] },
                  ],
                },
              },
            },
          ],
          as: "wishlist",
        },
      },
      // Lookup reviews to calculate average rating
      {
        $lookup: {
          from: "reviews",
          let: { boatId: "$boat._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$status", "active"] },
                  ],
                },
              },
            },
          ],
          as: "reviews",
        },
      },
      // Lookup boat facilities
      {
        $lookup: {
          from: "facilities",
          localField: "boat.facilities",
          foreignField: "_id",
          as: "boatFacilities",
        },
      },
      // Lookup boat videos
      {
        $lookup: {
          from: "files",
          localField: "boat.attachments.videos",
          foreignField: "_id",
          as: "videoFiles",
        },
      },
      // Lookup boat documents
      {
        $lookup: {
          from: "files",
          localField: "boat.attachments.documents",
          foreignField: "_id",
          as: "documentFiles",
        },
      },
      // Lookup recommended places
      {
        $lookup: {
          from: "recommendedplaces",
          localField: "boat.recommendedPlaces",
          foreignField: "_id",
          as: "recommendedPlaces",
        },
      },
      // Lookup recommended place images
      {
        $lookup: {
          from: "files",
          localField: "recommendedPlaces.images",
          foreignField: "_id",
          as: "placeImageFiles",
        },
      },
      {
        $lookup: {
          from: "payments",
          localField: "paymentId",
          foreignField: "_id",
          as: "payment",
        },
      },
      {
        $unwind: {
          path: "$payment",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          extraFacilitiesIds: {
            $map: {
              input: "$extraFacilities",
              as: "facility",
              in: "$$facility.facilityId",
            },
          },
        },
      },
      {
        $lookup: {
          from: "facilities",
          localField: "extraFacilitiesIds",
          foreignField: "_id",
          as: "facilitiesDetails",
        },
      },
      // Lookup facility images
      {
        $lookup: {
          from: "files",
          localField: "facilitiesDetails.images",
          foreignField: "_id",
          as: "facilityImageFiles",
        },
      },
      // Format boat attachments and recommended places with proper file URLs
      {
        $addFields: {
          "boat.averageRating": {
            $cond: [
              { $gt: [{ $size: "$reviews" }, 0] },
              { $avg: "$reviews.rating" },
              0
            ]
          },
          "boat.isWishlisted": { $gt: [{ $size: "$wishlist" }, 0] },
          "boat.facilities": {
            $map: {
              input: "$boatFacilities",
              as: "facility",
              in: {
                _id: "$$facility._id",
                name: "$$facility.name",
                price: "$$facility.price",
                status: "$$facility.status",
                quantity: {
                  $let: {
                    vars: {
                      extraFacility: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: "$extraFacilities",
                              as: "ef",
                              cond: { $eq: ["$$ef.facilityId", "$$facility._id"] }
                            }
                          },
                          0
                        ]
                      }
                    },
                    in: { $ifNull: ["$$extraFacility.quantity", 1] }
                  }
                },
                totalPrice: {
                  $let: {
                    vars: {
                      extraFacility: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: "$extraFacilities",
                              as: "ef",
                              cond: { $eq: ["$$ef.facilityId", "$$facility._id"] }
                            }
                          },
                          0
                        ]
                      }
                    },
                    in: {
                      $multiply: [
                        "$$facility.price",
                        { $ifNull: ["$$extraFacility.quantity", 1] }
                      ]
                    }
                  }
                }
              }
            }
          },
          "boat.ownerInfo": {
            $let: {
              vars: {
                owner: { $arrayElemAt: ["$boatOwnerInfo", 0] },
                avatar: { $arrayElemAt: ["$boatOwnerAvatarFile", 0] },
              },
              in: {
                _id: "$$owner._id",
                username: "$$owner.username",
                email: "$$owner.email",
                avatarUrl: {
                  $cond: [
                    { $gt: [{ $size: "$boatOwnerAvatarFile" }, 0] },
                    {
                      $concat: [
                        global.config.FILE_BASE_URL,
                        "$$avatar.location",
                      ],
                    },
                    null,
                  ],
                },
              },
            },
          },
          "boat.attachments": {
            images: {
              $map: {
                input: "$imageFiles",
                as: "img",
                in: {
                  id: "$$img._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$img.location"],
                  },
                },
              },
            },
            videos: {
              $map: {
                input: "$videoFiles",
                as: "video",
                in: {
                  id: "$$video._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$video.location"],
                  },
                },
              },
            },
            documents: {
              $map: {
                input: "$documentFiles",
                as: "doc",
                in: {
                  id: "$$doc._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$doc.location"],
                  },
                },
              },
            },
          },
          "boat.recommendedPlaces": {
            $map: {
              input: "$recommendedPlaces",
              as: "place",
              in: {
                _id: "$$place._id",
                name: "$$place.name",
                description: "$$place.description",
                status: "$$place.status",
                lat: "$$place.lat",
                lng: "$$place.lng",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$placeImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$place.images"] },
                      },
                    },
                    as: "image",
                    in: {
                      id: "$$image._id",
                      link: {
                        $concat: [
                          global.config.FILE_BASE_URL,
                          "$$image.location",
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
          // Add facility details with images
          facilitiesDetails: {
            $map: {
              input: "$facilitiesDetails",
              as: "facility",
              in: {
                _id: "$$facility._id",
                name: "$$facility.name",
                price: "$$facility.price",
                status: "$$facility.status",
                images: {
                  $map: {
                    input: {
                      $filter: {
                        input: "$facilityImageFiles",
                        as: "img",
                        cond: { $in: ["$$img._id", "$$facility.images"] },
                      },
                    },
                    as: "image",
                    in: {
                      id: "$$image._id",
                      link: {
                        $concat: [
                          global.config.FILE_BASE_URL,
                          "$$image.location",
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      // Remove the temporary file arrays
      {
        $project: {
          imageFiles: 0,
          videoFiles: 0,
          documentFiles: 0,
          recommendedPlaces: 0,
          placeImageFiles: 0,
          facilityImageFiles: 0,
          boatOwnerInfo: 0,
          boatOwnerAvatarFile: 0,
          wishlist: 0,
          reviews: 0,
          boatFacilities: 0,
        },
      },
      {
        $sort: { startDate: 1 },
      },
      {
        $facet: {
          metadata: [
            { $count: "total" },
            {
              $addFields: {
                page,
                size: limit,
                totalCount: "$total",
                totalPages: { $ceil: { $divide: ["$total", limit] } },
              },
            },
          ],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ] as any[];

    const result = await Booking.aggregate(aggregation);

    // Format the response
    const metadata = result[0].metadata[0] || {
      page,
      size: limit,
      totalCount: 0,
      totalPages: 0,
    };
    const bookings = result[0].data;

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_FETCH_SUCCESS"),
      data: {
        bookings,
        pagination: metadata
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get bookings by boat ID and user ID
export const getBookingsByBoatAndUser = async (req: any, res: any) => {
  try {
    const { boatId } = req.params;
    const userId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const aggregation = [
      {
        $match: {
          boatId: new mongoose.Types.ObjectId(boatId),
          userId: new mongoose.Types.ObjectId(userId),
        },
      },
      {
        $lookup: {
          from: "boats",
          localField: "boatId",
          foreignField: "_id",
          as: "boat",
        },
      },
      {
        $unwind: {
          path: "$boat",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          extraFacilitiesIds: {
            $map: {
              input: "$extraFacilities",
              as: "facility",
              in: "$$facility.facilityId",
            },
          },
        },
      },
      {
        $lookup: {
          from: "facilities",
          localField: "extraFacilitiesIds",
          foreignField: "_id",
          as: "facilitiesDetails",
        },
      },
      {
        $sort: { startDate: 1 },
      },
      {
        $facet: {
          metadata: [
            { $count: "total" },
            {
              $addFields: {
                page,
                size: limit,
                totalCount: "$total",
                totalPages: { $ceil: { $divide: ["$total", limit] } },
              },
            },
          ],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ] as any[];

    const result = await Booking.aggregate(aggregation);

    // Format the response
    const metadata = result[0].metadata[0] || {
      page,
      size: limit,
      totalCount: 0,
      totalPages: 0,
    };
    const bookings = result[0].data;

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_FETCH_SUCCESS"),
      data: {
        bookings,
        pagination: metadata,
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getBookingByBoatOwner = async (req: any, res: any) => {
  try {
    const ownerId = req.query.ownerId || req.user._id;
    const boatId = req.query.boatId;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status; // 'request' (Pending) or 'booked' (Accepted)

    const whereCondition: any = {};
    if (boatId) {
      whereCondition.boatId = boatId;
    } else {
      whereCondition.ownerId = ownerId;
    }
    // First verify that the boat belongs to this owner
    const boat = await Boat.find(whereCondition);

    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOAT_NOT_FOUND_OR_NOT_OWNER"),
      });
    }

    // Build match condition
    const matchCondition: any = {
      boatId: { $in: boat.map((b: any) => b._id) },
    };

    // Add status filter with support for 'request' and 'booked' keywords
    if (status === 'request') {
      matchCondition.status = BookingStatus.Pending;
    } else if (status === 'booked') {
      matchCondition.status = BookingStatus.Accepted;
    } else if (status) {
      // If status is provided but not 'request' or 'booked', use it as is
      matchCondition.status = status;
    }

    // Get commission rates for admin percentage calculation
    const CommissionRate = require("../models/CommissionRate").default;
    const commissionRate = await CommissionRate.findOne({ status: "active" });
    const adminPercentage = commissionRate ? commissionRate.adminFee : 10;

    const aggregation = [
      { $match: matchCondition },
      {
        $lookup: {
          from: "boats",
          localField: "boatId",
          foreignField: "_id",
          as: "boat",
        },
      },
      {
        $unwind: {
          path: "$boat",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        // Lookup user details
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "user",
        },
      },
      {
        $unwind: {
          path: "$user",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "files",
          localField: "user.avatar",
          foreignField: "_id",
          as: "userAvatar",
        },
      },
      {
        $unwind: { path: "$userAvatar", preserveNullAndEmptyArrays: true }
      },
      // Lookup boat images
      {
        $lookup: {
          from: "files",
          localField: "boat.attachments.images",
          foreignField: "_id",
          as: "boatImageFiles",
        },
      },
      // Lookup boat videos
      {
        $lookup: {
          from: "files",
          localField: "boat.attachments.videos",
          foreignField: "_id",
          as: "boatVideoFiles",
        },
      },
      // Lookup boat documents
      {
        $lookup: {
          from: "files",
          localField: "boat.attachments.documents",
          foreignField: "_id",
          as: "boatDocumentFiles",
        },
      },
      {
        $addFields: {
          extraFacilitiesIds: {
            $map: {
              input: "$extraFacilities",
              as: "facility",
              in: "$$facility.facilityId",
            },
          },
        },
      },
      {
        $lookup: {
          from: "facilities",
          localField: "extraFacilitiesIds",
          foreignField: "_id",
          as: "facilitiesDetails",
        },
      },
      {
        $addFields: {
          // Calculate admin deduction and net amount
          adminDeduction: { $multiply: ["$totalAmount", adminPercentage / 100] },
          totalReceived: { $subtract: ["$totalAmount", { $multiply: ["$totalAmount", adminPercentage / 100] }] },
          userProfile: {
            id: "$user._id",
            name: "$user.username",
            email: "$user.email",
            phone: "$user.phoneNo",
            avatar: {
              $cond: {
                if: "$userAvatar",
                then: {
                  id: "$userAvatar._id",
                  link: { $concat: [global.config.FILE_BASE_URL, "$userAvatar.location"] }
                },
                else: null
              }
            }
          },
          // Format boat attachments with proper file URLs
          "boat.attachments": {
            images: {
              $map: {
                input: "$boatImageFiles",
                as: "img",
                in: {
                  id: "$$img._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$img.location"],
                  },
                },
              },
            },
            videos: {
              $map: {
                input: "$boatVideoFiles",
                as: "video",
                in: {
                  id: "$$video._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$video.location"],
                  },
                },
              },
            },
            documents: {
              $map: {
                input: "$boatDocumentFiles",
                as: "doc",
                in: {
                  id: "$$doc._id",
                  link: {
                    $concat: [global.config.FILE_BASE_URL, "$$doc.location"],
                  },
                },
              },
            },
          }
        }
      },
      {
        $project: {
          _id: 1,
          userProfile: 1,
          bookingStartDate: "$startDate",
          bookingEndDate: "$endDate",
          bookingStatus: "$status",
          totalAmount: 1,
          boatType: "$boat.type",
          recordType: "$boat.recordType",
          guestCapacity: "$boat.guestsCapacity",
          cabins: "$boat.cabins",
          baths: "$boat.baths",
          extraFacilities: 1,
          totalReceived: 1,
          adminPercentage: { $literal: adminPercentage },
          adminDeduction: 1,
          netAmount: "$netAmount",
          location: 1,
          duration: 1,
          patronType: 1,
          currency: 1,
          createdAt: 1,
          updatedAt: 1,
          boatId: 1,
          boat: {
            _id: "$boat._id",
            name: "$boat.name",
            type: "$boat.type",
            attachments: "$boat.attachments"
          }
        }
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $facet: {
          data: [
            { $skip: skip },
            { $limit: limit }
          ],
          metadata: [
            { $count: "total" }
          ]
        }
      }
    ] as any[];

    const result = await Booking.aggregate(aggregation);
    const bookings = result[0].data;
    const totalCount = result[0].metadata[0]?.total || 0;

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_FETCH_SUCCESS"),
      data: {
        bookings,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount,
          totalPages: Math.ceil(totalCount / Number(limit))
        },
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get booking details by ID
export const getBookingById = async (req: any, res: any) => {
  try {
    const { id } = req.params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_BOOKING_ID"),
      });
    }

    const aggregation = [
      {
        $match: { _id: new mongoose.Types.ObjectId(id) },
      },
      {
        $lookup: {
          from: "boats",
          localField: "boatId",
          foreignField: "_id",
          as: "boat",
        },
      },
      {
        $unwind: {
          path: "$boat",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          extraFacilitiesIds: {
            $map: {
              input: "$extraFacilities",
              as: "facility",
              in: "$$facility.facilityId",
            },
          },
        },
      },
      {
        $lookup: {
          from: "facilities",
          localField: "extraFacilitiesIds",
          foreignField: "_id",
          as: "facilitiesDetails",
        },
      },
    ] as any[];

    const result = await Booking.aggregate(aggregation);

    if (!result || result.length === 0) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    const booking = result[0];

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_FETCH_SUCCESS"),
      data: booking,
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Modify booking
export const modifyBooking = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user._id;

    const booking = await Booking.findOne({
      _id: id,
      userId,
      status: BookingStatus.Pending,
    });

    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    // Get boat details for price calculation
    const boat = await Boat.findById(booking.boatId);
    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_BOAT_NOT_AVAILABLE"),
      });
    }

    // Check for date conflicts if dates are being updated
    if (updateData.startDate || updateData.endDate) {
      const startDate = updateData.startDate || booking.startDate;
      const endDate = updateData.endDate || booking.endDate;

      const existingBooking = await Booking.findOne({
        _id: { $ne: id },
        boatId: booking.boatId,
        status: { $nin: [BookingStatus.Cancelled, BookingStatus.Rejected] },
        $or: [
          {
            startDate: { $lte: new Date(startDate) },
            endDate: { $gte: new Date(startDate) },
          },
          {
            startDate: { $lte: new Date(endDate) },
            endDate: { $gte: new Date(endDate) },
          },
        ],
      });

      if (existingBooking) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: req.__("BOOKING_DATES_OVERLAP"),
        });
      }
    }

    // Validate and update facilities if being modified
    let extraFacilitiesData = booking.extraFacilities;
    if (updateData.extraFacilities) {
      const facilityIds = updateData.extraFacilities.map(
        (f: any) => f.facilityId
      );
      const facilities = await Facility.find({
        _id: { $in: facilityIds },
        status: FacilityStatus.ACTIVE,
      });

      if (facilities.length !== facilityIds.length) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: req.__("BOOKING_INVALID_FACILITIES"),
        });
      }

      extraFacilitiesData = facilities.map((facility: any) => {
        const requestedFacility = updateData.extraFacilities.find(
          (f: any) => f.facilityId.toString() === facility._id.toString()
        );
        return {
          facilityId: facility._id,
          name: facility.name,
          price: facility.price,
          quantity: requestedFacility.quantity || 1,
        };
      });
    }

    // Get base price based on patron type
    let basePrice = booking.totalAmount;
    if (updateData.patronType) {
      switch (updateData.patronType) {
        case PatronType.FullDayWithPatron:
          basePrice = boat.fullDayWithPatron;
          break;
        case PatronType.FullDayWithoutPatron:
          basePrice = boat.fullDayWithoutPatron;
          break;
        case PatronType.HalfDayWithPatron:
          basePrice = boat.halfDayWithPatron;
          break;
        case PatronType.HalfDayWithoutPatron:
          basePrice = boat.halfDayWithoutPatron;
          break;
        default:
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: req.__("BOOKING_INVALID_PATRON_TYPE"),
          });
      }
    }

    // Calculate amounts if relevant fields are being updated
    if (
      updateData.startDate ||
      updateData.endDate ||
      updateData.patronType ||
      updateData.extraFacilities
    ) {
      try {
        const startDate = updateData.startDate || booking.startDate;
        const endDate = updateData.endDate || booking.endDate;

        // Validate duration calculation
        if (new Date(endDate) <= new Date(startDate)) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: req.__("BOOKING_END_DATE_MUST_BE_AFTER_START_DATE"),
          });
        }

        const durationInDays = Math.ceil(
          (new Date(endDate).getTime() - new Date(startDate).getTime()) /
            (1000 * 60 * 60 * 24)
        );

        if (durationInDays <= 0) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: req.__("BOOKING_INVALID_DURATION"),
          });
        }

        let totalAmount = boat.recordType === RecordType.BOAT ? calculateTotalAmount(
          basePrice,
          extraFacilitiesData,
          durationInDays
        ) : 0;

        // Add boat price based on record type
        if (boat.recordType === RecordType.BOAT) {
          if (isNaN(Number(boat.pricePerDay)) || Number(boat.pricePerDay) < 0) {
            throw new Error("Invalid boat price per day");
          }
          totalAmount += Number(boat.pricePerDay) * durationInDays;
        } else {
          if (isNaN(Number(boat.price)) || Number(boat.price) < 0) {
            throw new Error("Invalid boat price");
          }
          totalAmount += Number(boat.price) * durationInDays;
        }

        updateData.totalAmount = totalAmount;
        updateData.adminFee = 0; // totalAmount * 0.2; // 20% admin fee
        updateData.netAmount = totalAmount; // - adminFee;
      } catch (error: unknown) {
        if (error instanceof Error) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: req.__("BOOKING_CALCULATION_ERROR"),
            error: error.message,
          });
        }
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: req.__("BOOKING_CALCULATION_ERROR"),
          error: "Unknown calculation error",
        });
      }
    }

    // Get changes before update
    const changes = validateAndGetChanges(booking.toJSON(), updateData);

    // Update booking
    const updatedBooking = await Booking.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true }
    );

    // Log the changes if any
    if (changes.length > 0) {
      await createActivityLog("bookings", id, "UPDATE", changes, userId);

      // Send notification to boat owner
      await BookingNotificationService.sendBookingModificationNotification(
        updatedBooking,
        boat
      );
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_UPDATED_SUCCESS"),
      data: updatedBooking,
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Cancel booking
export const cancelBooking = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    const booking = await Booking.findOne({
      _id: id,
      userId,
      status: { $in: [BookingStatus.Pending, BookingStatus.ReadyForPayment] },
    });

    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    // Cancel payment intent if it exists
    if (booking.paymentIntentId) {
      try {
        const cancelResult = await StripeService.cancelPaymentIntent(booking.paymentIntentId);

        if (cancelResult.success) {
          logger.info(`Payment intent cancelled successfully for booking ${id}`, {
            service: 'booking-ms',
            bookingId: id,
            paymentIntentId: booking.paymentIntentId
          });
        } else {
          logger.warn(`Failed to cancel payment intent for booking ${id}`, {
            service: 'booking-ms',
            bookingId: id,
            paymentIntentId: booking.paymentIntentId,
            error: cancelResult.error
          });
          // Continue with booking cancellation even if payment intent cancellation fails
        }
      } catch (stripeError) {
        logger.error(`Error cancelling payment intent for booking ${id}`, {
          service: 'booking-ms',
          bookingId: id,
          paymentIntentId: booking.paymentIntentId,
          error: stripeError
        });
        // Continue with booking cancellation even if payment intent cancellation fails
      }
    }

    // Get changes before update
    const changes = validateAndGetChanges(booking.toJSON(), {
      status: BookingStatus.Cancelled,
      paymentIntentStatus: booking.paymentIntentId ? 'canceled' : booking.paymentIntentStatus,
    });

    // Update status to cancelled
    await Booking.findByIdAndUpdate(id, {
      status: BookingStatus.Cancelled,
      paymentIntentStatus: booking.paymentIntentId ? 'canceled' : booking.paymentIntentStatus,
    });

    // Log the cancellation
    await createActivityLog("bookings", id, "UPDATE", changes, userId);

    // Get boat details for notification
    const boat = await Boat.findById(booking.boatId);
    if (boat) {
      // Send notification to boat owner
      await BookingNotificationService.sendBookingCancellationNotification(
        booking,
        boat
      );
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_CANCELLED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Accept booking
export const acceptBooking = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    const booking = await Booking.findOne({
      _id: id,
      status: BookingStatus.Pending,
    });

    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    // Check for conflicting bookings for the same boat and overlapping dates
    const conflictingBooking = await Booking.findOne({
      _id: { $ne: id },
      boatId: booking.boatId,
      status: { $in: [BookingStatus.ReadyForPayment, BookingStatus.Accepted] },
      $or: [
        {
          startDate: { $lte: booking.startDate },
          endDate: { $gte: booking.startDate },
        },
        {
          startDate: { $lte: booking.endDate },
          endDate: { $gte: booking.endDate },
        },
        {
          startDate: { $gte: booking.startDate },
          endDate: { $lte: booking.endDate },
        },
      ],
    });

    if (conflictingBooking) {
      return res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: req.__("BOOKING_CONFLICTING_BOOKING"),
      });
    }

    // Get changes before update
    const changes = validateAndGetChanges(booking.toJSON(), {
      status: BookingStatus.ReadyForPayment,
    });

    // Update status to ready for payment
    await Booking.findByIdAndUpdate(id, {
      status: BookingStatus.ReadyForPayment,
    });

    // Log the acceptance
    await createActivityLog("bookings", id, "UPDATE", changes, userId);

    // Get boat details for notification
    const boat = await Boat.findById(booking.boatId);
    if (boat) {
      // Send notification to user
      await BookingNotificationService.sendBookingApprovalNotification(
        booking,
        boat
      );
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_ACCEPTED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Reject booking
export const rejectBooking = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const userId = req.user._id;

    const booking = await Booking.findOne({
      _id: id,
      status: { $in: [BookingStatus.Pending, BookingStatus.ReadyForPayment] },
    });

    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    // Cancel payment intent if it exists
    if (booking.paymentIntentId) {
      try {
        const cancelResult = await StripeService.cancelPaymentIntent(booking.paymentIntentId);

        if (cancelResult.success) {
          logger.info(`Payment intent cancelled successfully for rejected booking ${id}`, {
            service: 'booking-ms',
            bookingId: id,
            paymentIntentId: booking.paymentIntentId
          });
        } else {
          logger.warn(`Failed to cancel payment intent for rejected booking ${id}`, {
            service: 'booking-ms',
            bookingId: id,
            paymentIntentId: booking.paymentIntentId,
            error: cancelResult.error
          });
          // Continue with booking rejection even if payment intent cancellation fails
        }
      } catch (stripeError) {
        logger.error(`Error cancelling payment intent for rejected booking ${id}`, {
          service: 'booking-ms',
          bookingId: id,
          paymentIntentId: booking.paymentIntentId,
          error: stripeError
        });
        // Continue with booking rejection even if payment intent cancellation fails
      }
    }

    // Get changes before update
    const changes = validateAndGetChanges(booking.toJSON(), {
      status: BookingStatus.Rejected,
      rejectionReason: reason,
      paymentIntentStatus: booking.paymentIntentId ? 'canceled' : booking.paymentIntentStatus,
    });

    // Update status to rejected
    await Booking.findByIdAndUpdate(id, {
      status: BookingStatus.Rejected,
      rejectionReason: reason,
      paymentIntentStatus: booking.paymentIntentId ? 'canceled' : booking.paymentIntentStatus,
    });

    // Log the rejection
    await createActivityLog("bookings", id, "UPDATE", changes, userId);

    // Get boat details for notification
    const boat = await Boat.findById(booking.boatId);
    if (boat) {
      // Send notification to user
      await BookingNotificationService.sendBookingRejectionNotification(
        booking,
        boat,
        reason
      );
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_REJECTED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Helper function to calculate total amount
const calculateTotalAmount = (
  basePrice: number,
  extraFacilities: Array<{ price: number; quantity: number }>,
  durationInDays: number
): number => {
  // Validate inputs
  if (isNaN(basePrice) || basePrice < 0) {
    throw new Error("Invalid base price");
  }

  if (isNaN(durationInDays) || durationInDays <= 0) {
    throw new Error("Invalid duration");
  }

  let total = basePrice;

  // Add extra facilities cost
  if (extraFacilities && extraFacilities.length > 0) {
    extraFacilities.forEach((facility) => {
      if (isNaN(facility.price) || facility.price < 0) {
        throw new Error(`Invalid price for facility`);
      }
      if (isNaN(facility.quantity) || facility.quantity <= 0) {
        throw new Error(`Invalid quantity for facility`);
      }
      total += facility.price * facility.quantity * durationInDays;
    });
  }

  // Round to 2 decimal places to avoid floating point issues
  return Math.round(total * 100) / 100;
};

// Update booking status
export const updateBookingStatus = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const { status, paymentId } = req.body;
    const userId = req.user._id;

    // Check if booking exists
    const booking = await Booking.findById(id);
    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    // Validate status transition
    const validStatusTransitions: { [key: string]: string[] } = {
      [BookingStatus.Pending]: [
        BookingStatus.ReadyForPayment,
        BookingStatus.Rejected,
        BookingStatus.Cancelled,
      ],
      [BookingStatus.ReadyForPayment]: [
        BookingStatus.Accepted,
        BookingStatus.Cancelled,
      ],
      [BookingStatus.Accepted]: [
        BookingStatus.Completed,
        BookingStatus.Cancelled,
      ],
      [BookingStatus.Rejected]: [],
      [BookingStatus.Cancelled]: [],
      [BookingStatus.Completed]: [],
    };

    if (
      !validStatusTransitions[booking.status] ||
      !validStatusTransitions[booking.status].includes(status)
    ) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("BOOKING_INVALID_STATUS_TRANSITION"),
        data: {
          currentStatus: booking.status,
          requestedStatus: status,
          allowedTransitions: validStatusTransitions[booking.status],
        },
      });
    }

    // Update booking status
    const originalBooking = booking.toJSON();
    booking.status = status;

    // Add payment ID if provided
    if (paymentId) {
      booking.paymentId = paymentId;
    }

    await booking.save();

    // Log the update
    const changes = validateAndGetChanges(originalBooking, booking.toJSON());
    await createActivityLog("bookings", booking._id, "UPDATE", changes, userId);

    // Send notification about status change
    await BookingNotificationService.sendStatusChangeNotification(booking);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_STATUS_UPDATED_SUCCESS"),
      data: JSON.parse(JSON.stringify(booking)),
    });
  } catch (error) {
    logger.error("Error updating booking status", {
      service: "booking-ms",
      error,
    });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get booking status by ID - useful for other services to check status
export const getBookingStatus = async (req: any, res: any) => {
  try {
    const { id } = req.params;

    const booking = await Booking.findById(id);
    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_STATUS_FETCH_SUCCESS"),
      data: {
        _id: booking._id,
        status: booking.status,
        userId: booking.userId,
        boatOwnerId: booking.boatId, // This will need to be populated to get actual owner ID
        totalAmount: booking.totalAmount,
        adminFee: booking.adminFee,
        netAmount: booking.netAmount,
        referralName: booking.referralName,
      },
    });
  } catch (error) {
    logger.error("Error getting booking status", {
      service: "booking-ms",
      error,
    });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get bookings by boat ID with detailed user and financial information
export const getBookingsByBoatId = async (req: any, res: any) => {
  try {
    const { boatId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status; // 'request' (Pending) or 'booked' (Accepted)
    const skip = (page - 1) * limit;

    // Validate boatId
    if (!mongoose.Types.ObjectId.isValid(boatId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("INVALID_BOAT_ID"),
      });
    }

    // Build match condition
    const matchCondition: any = {
      boatId: new mongoose.Types.ObjectId(boatId),
    };

    // Add status filter
    if (status === 'request') {
      matchCondition.status = BookingStatus.Pending;
    } else if (status === 'booked') {
      matchCondition.status = BookingStatus.Accepted;
    } else if (status) {
      // If status is provided but not 'request' or 'booked', use it as is
      matchCondition.status = status;
    }

    // Get commission rates for admin percentage calculation
    const CommissionRate = require("../models/CommissionRate").default;
    const commissionRate = await CommissionRate.findOne({ status: "active" });
    const adminPercentage = commissionRate ? commissionRate.adminFee : 10;

    const aggregation: any = [
      { $match: matchCondition },
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "user",
        },
      },
      {
        $lookup: {
          from: "files",
          localField: "user.avatar",
          foreignField: "_id",
          as: "userAvatar",
        },
      },
      {
        $lookup: {
          from: "boats",
          localField: "boatId",
          foreignField: "_id",
          as: "boat",
        },
      },
      {
        $unwind: { path: "$user", preserveNullAndEmptyArrays: true }
      },
      {
        $unwind: { path: "$userAvatar", preserveNullAndEmptyArrays: true }
      },
      {
        $unwind: { path: "$boat", preserveNullAndEmptyArrays: true }
      },
      {
        $addFields: {
          // Calculate admin deduction and net amount
          adminDeduction: { $multiply: ["$totalAmount", adminPercentage / 100] },
          totalReceived: { $subtract: ["$totalAmount", { $multiply: ["$totalAmount", adminPercentage / 100] }] },
          userProfile: {
            id: "$user._id",
            name: "$user.username",
            email: "$user.email",
            phone: "$user.phoneNo",
            avatar: {
              $cond: {
                if: "$userAvatar",
                then: {
                  id: "$userAvatar._id",
                  link: { $concat: [global.config.FILE_BASE_URL, "$userAvatar.location"] }
                },
                else: null
              }
            }
          }
        }
      },
      {
        $project: {
          _id: 1,
          userProfile: 1,
          bookingStartDate: "$startDate",
          bookingEndDate: "$endDate",
          bookingStatus: "$status",
          totalAmount: 1,
          boatType: "$boat.type",
          guestCapacity: "$boat.guestsCapacity",
          cabins: "$boat.cabins",
          baths: "$boat.baths",
          extraFacilities: 1,
          totalReceived: 1,
          adminPercentage: { $literal: adminPercentage },
          adminDeduction: 1,
          netAmount: "$netAmount",
          location: 1,
          duration: 1,
          patronType: 1,
          currency: 1,
          createdAt: 1,
          updatedAt: 1
        }
      },
      { $sort: { createdAt: -1 }},
      {
        $facet: {
          data: [
            { $skip: skip },
            { $limit: limit }
          ],
          metadata: [
            { $count: "total" }
          ]
        }
      }
    ];

    const result = await Booking.aggregate(aggregation);
    const bookings = result[0].data;
    const totalCount = result[0].metadata[0]?.total || 0;

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("BOOKING_FETCH_SUCCESS"),
      data: {
        bookings,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount,
          totalPages: Math.ceil(totalCount / Number(limit))
        },
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
