import { StatusCodes } from "http-status-codes";
import logger from "../../../shared/services/logger.service";
import File, { FIleStatus, FileType } from "../../../shared/models/Files";
import fs from "fs";
import path from "path";
import { Types } from "mongoose";
import { getRelativePath } from "../../../shared/middleware/fileUpload.middleware";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";

/**
 * Upload user documents
 */
export const uploadDocument = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;

    // Check if files were uploaded
    if (!req.files || req.files.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("DOCUMENT_UPLOAD_REQUIRED"),
      });
    }

    const docFiles = req.files;
    const documentIds = [];

    // Process each uploaded document
    for (const file of docFiles) {
      // Get relative path for storage
      const relativePath = getRelativePath(file.path);

      // Create file record in database
      const newFile = await File.create({
        name: file.originalname,
        size: file.size,
        fileType: file.mimetype,
        ext: file.originalname.split(".").pop(),
        status: FIleStatus.ACTIVE,
        location: relativePath,
        type: FileType.DOCUMENT,
        ownerId: userId,
        isChat: false,
      });

      documentIds.push(newFile._id);

      // Log the document creation
      await createActivityLog("files", newFile._id, "CREATE", [], userId);
    }

    // Fetch complete document data
    const documents = await File.find({ _id: { $in: documentIds } });

    // Transform document URLs
    const processedDocuments = documents.map((doc) => {
      const document = doc.toObject();
      return {
        ...document,
        url: `${global.config.FILE_BASE_URL}${document.location}`,
      };
    });

    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("DOCUMENT_UPLOAD_SUCCESS"),
      data: {
        documents: processedDocuments,
      },
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Upload chat documents
 */
export const uploadChatDocument = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;

    if (!req.files || req.files.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("DOCUMENT_UPLOAD_REQUIRED"),
      });
    }

    const chatFiles = req.files;
    const chatDocumentIds = [];

    for (const file of chatFiles) {
      const relativePath = getRelativePath(file.path);

      const newFile = await File.create({
        name: file.originalname,
        size: file.size,
        fileType: file.mimetype,
        ext: file.originalname.split(".").pop(),
        location: relativePath,
        type:
          FileType[
            `${file.mimetype.split("/")[0]}`?.toUpperCase() as keyof typeof FileType
          ] || FileType.OTHER,
        ownerId: userId,
        isChat: true,
      });

      chatDocumentIds.push(newFile._id);
    }

    const chatDocuments = await File.find({ _id: { $in: chatDocumentIds } });

    const processedDocuments = chatDocuments.map((doc) => {
      const document = doc.toObject();
      return {
        ...document,
        url: `${global.config.FILE_BASE_URL}${document.location}`,
      };
    });

    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("DOCUMENT_UPLOAD_SUCCESS"),
      data: {
        documents: processedDocuments,
      },
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Get all user documents
 */
export const getUserDocuments = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    // const { page, limit } = req.query;
    // const skip = (page - 1) * limit;

    // Find documents owned by this user
    const documents = await File.find({
      ownerId: userId,
      status: FIleStatus.ACTIVE,
      type: FileType.DOCUMENT,
    })

      .sort({ createdAt: -1 });
    // if (page && limit) {
    //   documents.skip(skip);
    //   documents.limit(Number(limit));
    // }

    const totalDocuments = await File.countDocuments({
      ownerId: userId,
      type: FileType.DOCUMENT,
    });

    // Transform document URLs
    const processedDocuments = documents.map((doc) => {
      const document = doc.toObject();
      return {
        ...document,
        url: `${global.config.FILE_BASE_URL}${document.location}`,
      };
    });

    res.status(StatusCodes.OK).json({
      status: true,
      data: {
        documents: processedDocuments,
        totalDocuments,
        // currentPage: Number(page),
        // totalPages: Math.ceil(totalDocuments / Number(limit)),
      },
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Delete user document
 */
export const deleteDocument = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { id } = req.params;

    if (!Types.ObjectId.isValid(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_DOCUMENT_ID"),
      });
    }

    // Find the document
    const document = await File.findOne({
      _id: id,
      ownerId: userId,
      type: FileType.DOCUMENT,
    });

    if (!document) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("DOCUMENT_NOT_FOUND"),
      });
    }

    // Delete the physical file
    try {
      const filePath = path.resolve(
        __dirname,
        "../../../shared/uploads",
        document.location
      );
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (fileError) {
      logger.error(`Error deleting file: ${fileError}`);
      // Continue with record deletion even if file deletion fails
    }

    // Log the document deletion
    await createActivityLog("files", document._id, "DELETE", [], userId);

    // Delete the record from database
    await File.findByIdAndDelete(id);

    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("DOCUMENT_DELETED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getAllDocuments = async (req: any, res: any): Promise<any> => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const { status } = req.query;
    if (status && !Object.values(FIleStatus).includes(status)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_STATUS"),
      });
    }

    const skip = (page - 1) * limit;
    const documents = await File.find({
      status: status ? status : { $ne: FIleStatus.DELETED },
    })
      .skip(skip)
      .limit(Number(limit))
      .sort({ createdAt: -1 });

    const totalDocuments = await File.countDocuments({
      status: { $ne: "DELETED" },
    });

    res.status(StatusCodes.OK).json({
      status: true,
      data: {
        documents,
        totalDocuments,
        currentPage: Number(page),
        totalPages: Math.ceil(totalDocuments / Number(limit)),
      },
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Verify a document (admin only)
 * @param req Request with document ID and status
 * @param res Response
 */
export const verifyDocument = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const adminId = req.user._id;

    // Validate document ID
    if (!Types.ObjectId.isValid(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_DOCUMENT_ID"),
      });
    }

    // Find the document
    const document = await File.findById(id);

    if (!document) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("DOCUMENT_NOT_FOUND"),
      });
    }

    // Get original document data for activity log
    const originalDocument = document.toObject();

    // Update document status
    document.status = status;
    await document.save();

    // Log the verification action
    const changes = validateAndGetChanges(originalDocument, document.toJSON());

    await createActivityLog("files", document._id, "UPDATE", changes, adminId);

    // Return success response
    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("DOCUMENT_VERIFIED_SUCCESS"),
      data: document.toJSON(),
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
