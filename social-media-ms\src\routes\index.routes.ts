import { Router } from "express";
import {
  createSocialMediaProfile,
  getActiveSocialMediaProfile,
  getSocialMediaProfileByVersion,
  getAllSocialMediaProfiles,
  updateSocialMediaProfile,
  deleteSocialMediaProfile,
} from "../controller/socialMedia.controller";
import {
  validateIdParam,
  validateVersionParam,
} from "../validator/socialMedia.validator";

import { adminAccess } from "../../../shared/middleware/admin";
import multer from "multer";
import path from "path";
import fs from "fs";
const router = Router();

// Dynamic multer middleware for social media icons
const dynamicUpload = (req: any, res: any, next: any) => {
  const storage = multer.diskStorage({
    destination: (_req: any, _file: any, cb: any) => {
      const uploadPath = path.resolve(
        __dirname,
        "../../../shared/uploads/social-media"
      );
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }
      cb(null, uploadPath);
    },
    filename: (_req: any, file: any, cb: any) => {
      cb(null, `${Date.now()}-${file.originalname.replace(/\s+/g, "-")}`);
    },
  });

  const upload = multer({
    storage,
    fileFilter: (_req: any, file: any, cb: any) => {
      // Accept only image files
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('Only image files are allowed'), false);
      }
    },
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    }
  }).any(); // Accept any field name dynamically

  upload(req, res, next);
};

// Public routes
router.get("/", getActiveSocialMediaProfile);
router.get(
  "/version/:version",
  validateVersionParam,
  getSocialMediaProfileByVersion,
);

// Admin routes with file upload support (no celebrate validators for form-data)
router.post(
  "/",
  adminAccess,
  dynamicUpload,
  createSocialMediaProfile,
);
router.get("/all", adminAccess, getAllSocialMediaProfiles);
router.put(
  "/:id",
  adminAccess,
  dynamicUpload,
  validateIdParam,
  updateSocialMediaProfile,
);
router.delete(
  "/:id",
  adminAccess,
  validateIdParam,
  deleteSocialMediaProfile,
);

export default router;
