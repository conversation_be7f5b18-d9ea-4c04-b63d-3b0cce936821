import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import logger from '../services/logger.service';

/**
 * Comprehensive validation middleware for robust error handling
 */

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Validate MongoDB ObjectId
 */
export const validateObjectId = (id: string, fieldName: string = 'id'): ValidationResult => {
  if (!id) {
    return {
      isValid: false,
      errors: [{
        field: fieldName,
        message: `${fieldName} is required`,
        code: 'REQUIRED'
      }]
    };
  }

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return {
      isValid: false,
      errors: [{
        field: fieldName,
        message: `Invalid ${fieldName} format`,
        code: 'INVALID_FORMAT'
      }]
    };
  }

  return { isValid: true, errors: [] };
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): ValidationResult => {
  if (!email) {
    return {
      isValid: false,
      errors: [{
        field: 'email',
        message: 'Email is required',
        code: 'REQUIRED'
      }]
    };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      errors: [{
        field: 'email',
        message: 'Invalid email format',
        code: 'INVALID_FORMAT'
      }]
    };
  }

  return { isValid: true, errors: [] };
};

/**
 * Validate pagination parameters
 */
export const validatePagination = (page?: string, limit?: string): ValidationResult => {
  const errors: ValidationError[] = [];

  if (page) {
    const pageNum = parseInt(page, 10);
    if (isNaN(pageNum) || pageNum < 1) {
      errors.push({
        field: 'page',
        message: 'Page must be a positive integer',
        code: 'INVALID_FORMAT'
      });
    }
  }

  if (limit) {
    const limitNum = parseInt(limit, 10);
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      errors.push({
        field: 'limit',
        message: 'Limit must be between 1 and 100',
        code: 'INVALID_RANGE'
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate required fields
 */
export const validateRequiredFields = (data: any, requiredFields: string[]): ValidationResult => {
  const errors: ValidationError[] = [];

  for (const field of requiredFields) {
    if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
      errors.push({
        field,
        message: `${field} is required`,
        code: 'REQUIRED'
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate numeric fields
 */
export const validateNumericFields = (data: any, numericFields: { field: string, min?: number, max?: number }[]): ValidationResult => {
  const errors: ValidationError[] = [];

  for (const { field, min, max } of numericFields) {
    if (data[field] !== undefined) {
      const value = Number(data[field]);
      
      if (isNaN(value)) {
        errors.push({
          field,
          message: `${field} must be a valid number`,
          code: 'INVALID_TYPE'
        });
        continue;
      }

      if (min !== undefined && value < min) {
        errors.push({
          field,
          message: `${field} must be at least ${min}`,
          code: 'MIN_VALUE'
        });
      }

      if (max !== undefined && value > max) {
        errors.push({
          field,
          message: `${field} must be at most ${max}`,
          code: 'MAX_VALUE'
        });
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize input data
 */
export const sanitizeInput = (data: any): any => {
  if (typeof data === 'string') {
    return data.trim();
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeInput);
  }
  
  if (data && typeof data === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return data;
};

/**
 * Express middleware for parameter validation
 */
export const validateParams = (validations: ((req: Request) => ValidationResult)[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const allErrors: ValidationError[] = [];

      // Run all validations
      for (const validation of validations) {
        const result = validation(req);
        if (!result.isValid) {
          allErrors.push(...result.errors);
        }
      }

      // If there are validation errors, return them
      if (allErrors.length > 0) {
        return res.status(400).json({
          status: false,
          message: 'Validation failed',
          errors: allErrors
        });
      }

      // Sanitize request data
      req.body = sanitizeInput(req.body);
      req.query = sanitizeInput(req.query);
      req.params = sanitizeInput(req.params);

      next();
    } catch (error) {
      logger.error('Validation middleware error:', error);
      return res.status(500).json({
        status: false,
        message: 'Internal validation error'
      });
    }
  };
};

/**
 * Common validation functions for reuse
 */
export const commonValidations = {
  objectId: (field: string) => (req: Request) => validateObjectId(req.params[field], field),
  email: (req: Request) => validateEmail(req.body.email),
  pagination: (req: Request) => validatePagination(req.query.page as string, req.query.limit as string),
  requiredFields: (fields: string[]) => (req: Request) => validateRequiredFields(req.body, fields),
  numericFields: (fields: { field: string, min?: number, max?: number }[]) => (req: Request) => validateNumericFields(req.body, fields)
};

/**
 * Error response formatter
 */
export const formatValidationError = (errors: ValidationError[], req: any) => {
  return {
    status: false,
    message: req.__('VALIDATION_FAILED') || 'Validation failed',
    errors: errors.map(error => ({
      field: error.field,
      message: req.__(error.code) || error.message,
      code: error.code
    }))
  };
};
