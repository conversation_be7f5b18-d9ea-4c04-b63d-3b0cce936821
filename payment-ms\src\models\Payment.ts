import mongoose, { Document, Schema } from "mongoose";
import { UserCurrency } from "../../../user-ms/src/models/User";

export enum PaymentStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  REFUNDED = "refunded",
  CANCELLED = "cancelled"
}

export enum PaymentMethod {
  WALLET = "wallet",
  CARD = "card",
  PAYPAL = "paypal"
}

export interface IPayment extends Document {
  bookingId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  boatOwnerId: mongoose.Types.ObjectId;
  amount: number;
  adminFee: number;
  ownerAmount: number;
  affiliateFee?: number;
  affiliateId?: mongoose.Types.ObjectId;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  paymentReference?: string;
  cardId?: string;
  transactionId?: mongoose.Types.ObjectId;
  refundAmount?: number;
  refundReason?: string;
  refundTransactionId?: mongoose.Types.ObjectId;
  transactionDetails?: any;
  currency: string; // User's payment currency
  originalCurrency?: string; // Booking original currency
  ownerCurrency?: string; // Boat owner's currency
  pointsUsed: number;
  welcomeDiscountApplied: boolean;
  originalAmount: number;
  discountAmount: number;
  createdAt: Date;
  updatedAt: Date;
}

const PaymentSchema = new Schema<IPayment>(
  {
    bookingId: {
      type: Schema.Types.ObjectId,
      ref: "Booking",
      required: true,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    boatOwnerId: {
      type: Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    adminFee: {
      type: Number,
      required: true,
    },
    ownerAmount: {
      type: Number,
      required: true,
      description: "Amount that goes to the boat owner after all fees",
    },
    affiliateFee: {
      type: Number,
      required: false,
      default: 0,
    },
    affiliateId: {
      type: Schema.Types.ObjectId,
      ref: "Affiliate",
      required: false,
    },
    status: {
      type: String,
      enum: Object.values(PaymentStatus),
      default: PaymentStatus.PENDING,
    },
    paymentMethod: {
      type: String,
      enum: Object.values(PaymentMethod),
      required: true,
    },
    paymentReference: {
      type: String,
      required: false,
    },
    cardId: {
      type: String,
      required: false,
    },
    transactionId: {
      type: Schema.Types.ObjectId,
      ref: "Transaction",
      required: false,
    },
    refundAmount: {
      type: Number,
      required: false,
    },
    refundReason: {
      type: String,
      required: false,
    },
    refundTransactionId: {
      type: Schema.Types.ObjectId,
      ref: "Transaction",
      required: false,
    },
    transactionDetails: {
      type: mongoose.Schema.Types.Mixed,
      required: false,
      description: "Transaction details from payment processor",
    },
    currency: {
      type: String,
      enum: Object.values(UserCurrency),
      required: true,
      default: UserCurrency.USD,
      description: "Currency used for payment",
    },
    originalCurrency: {
      type: String,
      enum: Object.values(UserCurrency),
      required: false,
      description: "Original currency of the booking",
    },
    ownerCurrency: {
      type: String,
      enum: Object.values(UserCurrency),
      required: false,
      description: "Currency of the boat owner",
    },
    pointsUsed: { type: Number, default: 0 },
    welcomeDiscountApplied: { type: Boolean, default: false },
    originalAmount: { type: Number },
    discountAmount: { type: Number, default: 0 },
  },
  { timestamps: true },
);

// Add indexes for faster queries
PaymentSchema.index({ bookingId: 1 });
PaymentSchema.index({ userId: 1 });
PaymentSchema.index({ boatOwnerId: 1 });
PaymentSchema.index({ status: 1 });
PaymentSchema.index({ createdAt: -1 });

const Payment = mongoose.model<IPayment>("Payment", PaymentSchema);
export default Payment;
