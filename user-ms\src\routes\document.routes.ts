import { Router } from "express";
import {
  deleteDocument,
  getAllDocuments,
  getUserDocuments,
  uploadDocument,
  uploadChatDocument,
  verifyDocument,
} from "../controller/document.controller";
import { uploadFiles } from "../../../shared/middleware/fileUpload.middleware";
import { idParamValidator, verifyDocumentValidator } from "../validator/document.validator";
import { adminAccess } from "../../../shared/middleware/admin";

const router = Router();

// Upload documents
router.post("/upload", uploadFiles("documents", "documents"), uploadDocument);

router.post("/upload-chat", uploadFiles("chats", "media"), uploadChatDocument);

// Get all user documents
router.get("/", getUserDocuments);

// Delete a document
router.delete("/:id", idParamValidator, deleteDocument);

router.get("/all", adminAccess, getAllDocuments);

// Verify a document (admin only)
router.put("/verify/:id", adminAccess, verifyDocumentValidator, verifyDocument);

export default router;
