/**
 * PM2 Memory and CPU Usage Monitor
 * 
 * This script helps monitor memory and CPU usage of all PM2 processes
 * and can be used to detect potential memory leaks or high CPU usage.
 */

const { exec } = require('child_process');
const fs = require('fs');

// Configuration
const LOG_FILE = './pm2-resource-usage.log';
const MEMORY_THRESHOLD_MB = 200; // Alert if memory usage exceeds this value (MB)
const CPU_THRESHOLD = 50;        // Alert if CPU usage exceeds this percentage
const CHECK_INTERVAL = 60000;    // Check every minute

// Initialize log file
if (!fs.existsSync(LOG_FILE)) {
  fs.writeFileSync(LOG_FILE, 'Timestamp,Service,Memory(MB),CPU(%),Status\n');
}

/**
 * Get PM2 process list with memory and CPU usage
 */
function getProcessStats() {
  return new Promise((resolve, reject) => {
    exec('pm2 jlist', (error, stdout) => {
      if (error) {
        return reject(error);
      }
      
      try {
        const processes = JSON.parse(stdout);
        resolve(processes);
      } catch (err) {
        reject(new Error('Failed to parse PM2 output'));
      }
    });
  });
}

/**
 * Log resource usage and check for issues
 */
async function monitorResources() {
  try {
    const processes = await getProcessStats();
    const timestamp = new Date().toISOString();
    let issuesFound = false;
    
    console.log('\n===== PM2 Resource Usage Report =====');
    console.log(`Time: ${timestamp}`);
    console.log('Name\t\tMemory\t\tCPU');
    console.log('----------------------------------------');
    
    processes.forEach(proc => {
      // Calculate memory in MB
      const memoryMB = Math.round(proc.monit.memory / (1024 * 1024));
      const cpuPercent = proc.monit.cpu;
      
      // Determine status
      let status = 'OK';
      if (memoryMB > MEMORY_THRESHOLD_MB || cpuPercent > CPU_THRESHOLD) {
        status = 'WARNING';
        issuesFound = true;
      }
      
      // Log to console
      console.log(`${proc.name}\t\t${memoryMB} MB\t\t${cpuPercent}%`);
      
      // Log to file
      fs.appendFileSync(LOG_FILE, `${timestamp},${proc.name},${memoryMB},${cpuPercent},${status}\n`);
    });
    
    if (issuesFound) {
      console.log('\n⚠️ WARNING: Some services exceed resource thresholds!');
      console.log('Consider restarting problematic services with: pm2 reload <service-name>');
    }
    
    console.log('\nResource monitoring log saved to:', LOG_FILE);
    
  } catch (error) {
    console.error('Error monitoring resources:', error.message);
  }
}

// Run once immediately
monitorResources();

// Then schedule regular checks if running as a continuous process
if (process.env.CONTINUOUS_MONITORING === 'true') {
  console.log(`Continuous monitoring enabled. Checking every ${CHECK_INTERVAL/1000} seconds...`);
  setInterval(monitorResources, CHECK_INTERVAL);
}

// Export for use in other scripts
module.exports = { monitorResources };