import { StatusCodes } from "http-status-codes";
import logger from "../../../shared/services/logger.service";
import RecommendedPlace, { PlaceStatus } from "../models/RecommendedPlace";
import File, { FileType } from "../../../shared/models/Files";
import {
  placeCreateBodyValidator,
  placeUpdateBodyValidator,
} from "../validator/recommendedPlace.validator";
import { UserRole } from "../../../user-ms/src/models/User";
import { getRelativePath } from "../../../shared/middleware/fileUpload.middleware";
import mongoose from "mongoose";

export const createPlace = async (req: any, res: any): Promise<any> => {
  try {
    // Validate directly in the controller
    const validationResult = placeCreateBodyValidator.validate(req.body);
    if (validationResult.error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: validationResult.error.details[0].message,
      });
    }

    // Create file records and get file IDs
    const fileIds = [];
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        // Store only the path after uploads/
        const relativePath = getRelativePath(file.path);

        const newFile = await File.create({
          name: file.filename,
          size: file.size,
          fileType: file.mimetype,
          ext: file.originalname.split(".").pop(),
          location: relativePath,
          type: FileType.IMAGE,
          ownerId: req.user._id,
        });
        fileIds.push(newFile._id);
      }
    }

    const place = await RecommendedPlace.create({
      ...req.body,
      images: fileIds,
      ownerId: req.user._id,
    });

    // Populate file references
    await place.populate("images");

    // Process image URL if exists
    const processedPlace: any = place.toObject();
    if (processedPlace.images && processedPlace.images.length > 0) {
      processedPlace.images = processedPlace.images.map(
        (img: any) => ({
          id: img._id,
          link: `${global.config.FILE_BASE_URL}${img.location}`
        })
      );
    }

    res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("PLACE_CREATED_SUCCESS"),
      data: processedPlace,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getPlace = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const place = await RecommendedPlace.findById(id).populate("images");

    if (!place) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("PLACE_NOT_FOUND"),
      });
    }

    // Process image URL if exists
    const processedPlace: any = place.toObject();
    if (processedPlace.images && processedPlace.images.length > 0) {
      processedPlace.images = processedPlace.images.map(
        (img: any) => ({
          id: img._id,
          link: `${global.config.FILE_BASE_URL}${img.location}`
        })
      );
    }

    res.json({
      status: true,
      data: processedPlace,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getAllPlaces = async (req: any, res: any): Promise<any> => {
  try {
    const { page = 1, limit = 10, status, location, search } = req.query;
    const skip = (page - 1) * limit;

    const query: any = {};

    // Add filters if provided
    if (status) query.status = status;
    if (location) query.location = { $regex: location, $options: "i" };

    if (search) query.name = { $regex: search, $options: "i" };

    if (req.user.role != UserRole.Admin) {
      query.ownerId = req.user._id;
      query.status = { $ne: PlaceStatus.DELETED };
    }

    const places = await RecommendedPlace.find(query)
      .populate("images")
      .skip(skip)
      .limit(Number(limit))
      .sort({ createdAt: -1 });

    const totalPlaces = await RecommendedPlace.countDocuments(query);

    // Process places data
    const processedPlaces = places.map((place) => {
      const placeObj: any = place.toObject();
      if (placeObj.images && placeObj.images.length > 0) {
        placeObj.images = placeObj.images.map(
          (img: any) => ({
            id: img._id,
            link: `${global.config.FILE_BASE_URL}${img.location}`
          })
        );
      }
      return placeObj;
    });

    res.json({
      status: true,
      data: {
        places: processedPlaces,
        totalPlaces,
        currentPage: Number(page),
        totalPages: Math.ceil(totalPlaces / Number(limit)),
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const updatePlace = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Validate directly in the controller
    const validationResult = placeUpdateBodyValidator.validate(updates);
    if (validationResult.error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: validationResult.error.details[0].message,
      });
    }

    // Handle images - support both existing and new uploads
    let finalImageIds: any[] = [];

    // First, handle existing images if provided
    if (req.body.existingImages) {
      try {
        const existingImagesArray = JSON.parse(req.body.existingImages || "[]");
        if (Array.isArray(existingImagesArray) && existingImagesArray.length > 0) {
          const existingImageIds = existingImagesArray
            .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
            .map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
          finalImageIds = [...existingImageIds];
        }
      } catch (error) {
        console.error("Error parsing existingImages:", error);
      }
    }

    // Then, handle new image uploads if any
    if (req.files && req.files.length > 0) {
      const newImageIds = [];
      for (const file of req.files) {
        // Store only the path after uploads/
        const relativePath = getRelativePath(file.path);

        const newFile = await File.create({
          name: file.filename,
          size: file.size,
          fileType: file.mimetype,
          ext: file.originalname.split(".").pop(),
          location: relativePath,
          type: FileType.IMAGE,
          ownerId: req.user._id,
        });
        newImageIds.push(newFile._id);
      }

      // Add new images to the final array
      finalImageIds = [...finalImageIds, ...newImageIds];
    }

    // Update images only if we have images to set or if existingImages was provided
    if (finalImageIds.length > 0 || req.body.existingImages) {
      updates.images = finalImageIds;
    }

    const place = await RecommendedPlace.findByIdAndUpdate(id, updates, {
      new: true,
    }).populate("images");

    if (!place) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("PLACE_NOT_FOUND"),
      });
    }

    // Process image URL if exists
    const processedPlace: any = place.toObject();
    if (processedPlace.images && processedPlace.images.length > 0) {
      processedPlace.images = processedPlace.images.map(
        (img: any) => ({
          id: img._id,
          link: `${global.config.FILE_BASE_URL}${img.location}`
        })
      );
    }

    res.json({
      status: true,
      message: res.__("PLACE_UPDATED_SUCCESS"),
      data: processedPlace,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const deletePlace = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;

    // Get place to delete associated files
    const place = await RecommendedPlace.findById(id);
    if (!place) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("PLACE_NOT_FOUND"),
      });
    }

    // Delete associated files (optional)
    if (place.images && place.images.length > 0) {
      await File.deleteMany({ _id: { $in: place.images } });
    }

    // Update place status to deleted
    await RecommendedPlace.findByIdAndUpdate(id, {
      status: PlaceStatus.DELETED,
    });

    res.json({
      status: true,
      message: res.__("PLACE_DELETED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Upload image to a recommended place
 * @param req
 * @param res
 * @returns
 */
export const uploadImage = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const file = req.file;

    if (!file) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "No file provided",
      });
    }

    // Validate file type - only images allowed for recommended places
    if (!file.mimetype.startsWith('image/')) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Invalid file type. Only images are allowed for recommended places.",
      });
    }

    const place: any = await RecommendedPlace.findById(id);
    if (!place) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("PLACE_NOT_FOUND"),
      });
    }

    // Check if user is the owner
    if (place.ownerId.toString() !== req.user._id.toString()) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("PLACE_NOT_OWNER"),
      });
    }

    // Store only the path after uploads/
    const relativePath = getRelativePath(file.path);

    // Create file record
    const newFile = await File.create({
      name: file.filename,
      size: file.size,
      fileType: file.mimetype,
      ext: file.originalname.split(".").pop(),
      location: relativePath,
      type: FileType.IMAGE,
      ownerId: req.user._id,
    });

    // Add file ID to place images
    place.images.push(newFile._id);
    await place.save();

    // Populate the updated place with file details
    await place.populate("images");

    // Process image URLs
    const processedPlace = place.toObject();
    if (processedPlace.images?.length > 0) {
      processedPlace.images = processedPlace.images.map(
        (img: any) => ({
          id: img._id,
          link: `${global.config.FILE_BASE_URL}${img.location}`
        })
      );
    }

    res.json({
      status: true,
      message: res.__("PLACE_IMAGE_ADDED"),
      data: processedPlace,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Remove images from a recommended place
 * @param req
 * @param res
 * @returns
 */
export const removeRecommendedPlaceImages = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const { imageIds } = req.body;
    const userId = req.user._id;

    if (!imageIds || !Array.isArray(imageIds) || imageIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("IMAGE_IDS_REQUIRED"),
      });
    }

    // Find the place and verify ownership
    const place = await RecommendedPlace.findOne({
      _id: id,
      ownerId: userId,
      status: { $ne: PlaceStatus.DELETED },
    });

    if (!place) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("PLACE_NOT_FOUND"),
      });
    }

    // Validate that image IDs exist in place's images
    const objectIdImageIds = imageIds.map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
    const validImageIds = objectIdImageIds.filter((removeId) =>
      place.images?.some((imageId) => removeId.equals(imageId))
    );

    if (validImageIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("INVALID_IMAGE_IDS"),
      });
    }

    // Remove images from place
    place.images = place.images?.filter(
      (imageId) => !validImageIds.some((removeId) => removeId.equals(imageId))
    ) || [];

    await place.save();

    // Optionally delete file records (soft delete by updating status)
    await File.updateMany(
      { _id: { $in: validImageIds } },
      { status: "deleted" }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("PLACE_IMAGES_REMOVED"),
      data: {
        removedImages: validImageIds.length,
        totalImages: place.images?.length || 0,
      },
    });
  } catch (error: any) {
    logger.error(`Error removing place images: ${error.message}`, {
      service: "place-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};
