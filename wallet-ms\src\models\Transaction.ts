import mongoose, { Document, Schema } from "mongoose";
import { UserCurrency } from "../../../user-ms/src/models/User";

export enum TransactionType {
  CREDIT = "credit",
  DEBIT = "debit",
}

export enum TransactionStatus {
  PENDING = "pending",
  COMPLETED = "completed",
  FAILED = "failed",
}

export enum TransactionSource {
  BOOKING = "booking",
  WITHDRAWAL = "withdrawal",
  REFUND = "refund",
  AFFILIATE = "affiliate",
  ADMIN = "admin",
}

export interface ITransaction extends Document {
  userId: mongoose.Types.ObjectId;
  amount: number;
  type: TransactionType;
  status: TransactionStatus;
  source: TransactionSource;
  sourceId?: mongoose.Types.ObjectId;
  bookingId?: mongoose.Types.ObjectId;
  description: string;
  paymentMethod?: string;
  reference?: string;
  metadata?: any;
  currency?: string; // Transaction currency
  createdAt: Date;
  updatedAt: Date;
}

const TransactionSchema = new Schema<ITransaction>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    type: {
      type: String,
      enum: Object.values(TransactionType),
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(TransactionStatus),
      default: TransactionStatus.PENDING,
    },
    source: {
      type: String,
      enum: Object.values(TransactionSource),
      required: true,
    },
    sourceId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    bookingId: {
      type: Schema.Types.ObjectId,
      ref: "Booking",
      required: false,
    },
    description: {
      type: String,
      required: true,
    },
    paymentMethod: {
      type: String,
      required: false,
    },
    reference: {
      type: String,
      required: false,
    },
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      required: false,
    },
    currency: {
      type: String,
      enum: Object.values(UserCurrency),
      default: UserCurrency.USD,
      required: false,
      description: "Currency used for the transaction",
    },
  },
  { timestamps: true },
);

// Add indexes for faster queries
TransactionSchema.index({ userId: 1 });
TransactionSchema.index({ bookingId: 1 });
TransactionSchema.index({ status: 1 });
TransactionSchema.index({ type: 1 });
TransactionSchema.index({ createdAt: -1 });

const Transaction = mongoose.model<ITransaction>(
  "Transaction",
  TransactionSchema,
);
export default Transaction;
