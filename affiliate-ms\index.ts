import express from "express";
import cors from "cors";
import * as config from "../shared/config.json";
import i18n from "./src/services/i18n.service";
import HandleErrorMessage from "../shared/middleware/validator";

import routes from "./src/routes/affiliate.routes";
import connectDB from "../shared/db";
import { authenticateJWT } from "../shared/middleware/auth";
import responseFormatter from "../shared/middleware/responseFormatter.middleware";
import logger from "../shared/services/logger.service";

const app = express();

require("dotenv").config();

const environment = process.env.NODE_ENV! || "dev";

const envConfig: any = JSON.parse(JSON.stringify(config))[environment];

try {
  connectDB();
  logger.info("Database connected successfully", { service: "affiliate-ms" });
} catch (error: any) {
  logger.error(`Database connection failed: ${error.message}`, {
    service: "affiliate-ms",
    error,
  });
  process.exit(1);
}

global.config = envConfig;

// Enable CORS for all origins
app.use(cors({ origin: "*" }));

app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(i18n.init);

// Apply response formatter middleware
app.use(responseFormatter);

// Authenticate all routes except Swagger docs
app.use("/", authenticateJWT, routes);

// Error handling for celebrate validation errors
app.use(HandleErrorMessage);

const PORT = envConfig.services["affiliate-ms"].PORT || 3105;

app.listen(PORT, () => {
  logger.info(`Affiliate microservice is running on port ${PORT}`, {
    service: "affiliate-ms",
    port: PORT,
  });
  console.log(`Affiliate microservice is running on port ${PORT}`);
});

export default app;
