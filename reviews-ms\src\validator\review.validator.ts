import { celebrate, Joi, Segments } from "celebrate";

export const createReviewValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    boatId: Joi.string()
      .required()
      .regex(/^[0-9a-fA-F]{24}$/),
    rating: Joi.number().required().min(1).max(5),
    comment: Joi.string().required(),
  }),
});

export const getReviewsValidator = celebrate({
  [Segments.QUERY]: Joi.object().keys({
    boatId: Joi.string()
      .optional()
      .regex(/^[0-9a-fA-F]{24}$/),
    userId: Joi.string()
      .optional()
      .regex(/^[0-9a-fA-F]{24}$/),
    page: Joi.number().optional().default(1),
    limit: Joi.number().optional().default(10),
  }),
});

export const deleteReviewValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string()
      .required()
      .regex(/^[0-9a-fA-F]{24}$/),
  }),
});
