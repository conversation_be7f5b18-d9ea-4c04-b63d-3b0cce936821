import { NotificationService } from "../services/notification.service";
import logger from "../../../shared/services/logger.service";
import { StatusCodes } from "http-status-codes";

const getNotifications = async (req: any, res: any) => {
  try {
    // Validate user exists
    if (!req.user || !req.user._id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED"),
      });
    }

    const userId = req.user._id.toString();
    const page = Math.max(1, parseInt(req.query.page as string) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 10));

    const result = await NotificationService.getNotifications(
      userId,
      page,
      limit,
    );

    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("NOTIFICATION_LIST_SUCCESS"),
      data: result,
    });
  } catch (error: any) {
    logger.error("Error in getNotifications controller:", error);

    // Handle specific error types
    if (error.message === "Invalid user ID format") {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_USER_ID"),
      });
    }

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
    });
  }
};

const markAsRead = async (req: any, res: any) => {
  try {
    // Validate user exists
    if (!req.user || !req.user._id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED"),
      });
    }

    const userId = req.user._id.toString();
    const { notificationId } = req.params;

    // Validate notificationId
    if (!notificationId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("NOTIFICATION_ID_REQUIRED"),
      });
    }

    const notification = await NotificationService.markAsRead(
      notificationId,
      userId,
    );
    if (!notification) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("NOTIFICATION_NOT_FOUND"),
      });
    }

    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("NOTIFICATION_MARK_READ_SUCCESS"),
      data: notification,
    });
  } catch (error: any) {
    logger.error("Error in markAsRead controller:", error);

    // Handle specific error types
    if (error.message === "Invalid notification ID format" || error.message === "Invalid user ID format") {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_ID_FORMAT"),
      });
    }

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
    });
  }
};

const markAllAsRead = async (req: any, res: any) => {
  try {
    // Validate user exists
    if (!req.user || !req.user._id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED"),
      });
    }

    const userId = req.user._id.toString();
    await NotificationService.markAllAsRead(userId);

    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("NOTIFICATION_MARK_ALL_READ_SUCCESS"),
    });
  } catch (error: any) {
    logger.error("Error in markAllAsRead controller:", error);

    // Handle specific error types
    if (error.message === "Invalid user ID format") {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_USER_ID"),
      });
    }

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
    });
  }
};

export { getNotifications, markAsRead, markAllAsRead };
