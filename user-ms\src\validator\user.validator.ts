import { celebrate, Joi, Segments } from "celebrate";
import { UserCurrency, UserCurrentRole, UserLanguage } from "../models/User";

export const updateProfileValidator = Joi.object().keys({
  username: Joi.string().optional().allow(null, ""),
  currency: Joi.string()
    .valid(...Object.values(UserCurrency))
    .optional()
    .allow(null, ""),
  language: Joi.string()
    .valid(...Object.values(UserLanguage))
    .optional()
    .allow(null, ""),
});

export const changePasswordValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    currentPassword: Joi.string().required().min(6),
    newPassword: Joi.string().required().min(6),
    confirmPassword: Joi.string().required().valid(Joi.ref("newPassword")),
  }),
});

export const switchRoleValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    role: Joi.string()
      .required()
      .valid(...Object.values(UserCurrentRole)),
  }),
});

export const deleteAccountValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    deleteReason: Joi.string().required(),
  }),
});
