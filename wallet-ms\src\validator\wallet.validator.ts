import { celebrate, Joi, Segments } from "celebrate";

export const withdrawalValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    amount: Joi.number().required().min(1).messages({
      "number.base": "Amount must be a number",
      "number.min": "Amount must be greater than 0",
      "any.required": "Amount is required",
    }),
    bankHolderName: Joi.string().required().messages({
      "string.base": "Bank holder name must be a string",
      "any.required": "Bank holder name is required",
    }),
    bankName: Joi.string().required().messages({
      "string.base": "Bank name must be a string",
      "any.required": "Bank name is required",
    }),
    accountNumber: Joi.string().required().messages({
      "string.base": "Account number must be a string",
      "any.required": "Account number is required",
    }),
  }),
});
