import mongoose, { Document, Schema } from "mongoose";
import { UserCurrency } from "../../../user-ms/src/models/User";

export enum WalletStatus {
  ACTIVE = "active",
  DELETED = "deleted",
}

export interface IWallet extends Document {
  userId: mongoose.Types.ObjectId;
  balance: number;
  currency: string;
  status: WalletStatus;
  createdAt: Date;
  updatedAt: Date;
}

const WalletSchema = new Schema<IWallet>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "Users",
      required: true,
      unique: true,
    },
    balance: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    currency: {
      type: String,
      enum: Object.values(UserCurrency),
      default: UserCurrency.USD,
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(WalletStatus),
      default: WalletStatus.ACTIVE,
    },
  },
  { timestamps: true },
);

// Add index for faster queries
WalletSchema.index({ userId: 1 });

const Wallet = mongoose.model<IWallet>("Wallet", WalletSchema);
export default Wallet;
