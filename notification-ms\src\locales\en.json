{"NOTIFICATION_LIST_SUCCESS": "Notifications retrieved successfully", "NOTIFICATION_NOT_FOUND": "Notification not found", "NOTIFICATION_MARK_READ_SUCCESS": "Notification marked as read", "NOTIFICATION_MARK_ALL_READ_SUCCESS": "All notifications marked as read", "NOTIFICATION_ERROR_GET": "Error getting notifications", "NOTIFICATION_ERROR_MARK_READ": "Error marking notification as read", "NOTIFICATION_ERROR_MARK_ALL_READ": "Error marking all notifications as read", "SOMETHING_WENT_WRONG": "Something went wrong. Please try again later.", "FAIL_TOKEN_EXPIRED": "Session is expired. Please log in again.", "TOKEN_REQUIRED": "Authorization token is required.", "USER_NOT_FOUND": "User not found.", "ACCOUNT_INACTIVE": "Account is inactive or deleted. Please contact support for assistance.", "ACCESS_DENIED": "Access denied.", "FORBIDDEN": "You are not authorized to access this resource.", "INTERNAL_SERVER_ERROR": "Internal server error occurred.", "INVALID_ID": "Invalid ID provided.", "INVALID_PAGINATION_PARAMS": "Invalid pagination parameters.", "INVALID_FILTER_PARAMS": "Invalid filter parameters."}