import express from "express";
import { authenticateJWT } from "../../../shared/middleware/auth";
import { adminAccess } from "../../../shared/middleware/admin";
import { uploadSingeFiles } from "../../../shared/middleware/fileUpload.middleware";
import {
  validateNewsletterCreation,
  validateNewsletterUpdate,
  validateNewsletterParams,
} from "../validator/newsletter.validator";
import {
  createNewsletter,
  getNewsletterList,
  getNewsletterById,
  updateNewsletter,
  deleteNewsletter,
} from "../controller/newsletter.controller";

const router = express.Router();

// Create newsletter with image upload
router.post(
  "/",
  authenticateJWT,
  uploadSingeFiles("uploads/newsletters", "image"),
  validateNewsletterCreation,
  createNewsletter
);

// Get all newsletters
router.get("/", authenticateJWT, getNewsletterList);

// Get newsletter by ID
router.get(
  "/:id",
  authenticateJWT,
  validateNewsletterParams,
  getNewsletterById
);

// Update newsletter with optional image upload
router.put(
  "/:id",
  authenticateJWT,
  uploadSingeFiles("image", "uploads/newsletters"),
  validateNewsletterUpdate,
  updateNewsletter
);

// Delete newsletter
router.delete(
  "/:id",
  authenticateJWT,
  validateNewsletterParams,
  deleteNewsletter
);

export default router;
