{"ACCESS_DENIED": "Access denied. You don't have permission to perform this action.", "INTERNAL_SERVER_ERROR": "An internal server error occurred. Please try again later.", "INVALID_ID": "Invalid ID format provided.", "INVALID_PAGINATION_PARAMS": "Invalid pagination parameters.", "INVALID_FILTER_PARAMS": "Invalid filter parameters.", "INVALID_CURRENCY": "Invalid currency specified.", "BOAT_OWNER_NOT_FOUND": "Boat owner not found.", "BOOKING_INVALID_DATE_FORMAT": "Invalid date format provided.", "BOOKING_BOAT_ALREADY_BOOKED": "This boat is already booked for the selected dates.", "INVALID_BOOKING_ID": "Invalid booking ID provided.", "BOOKING_CREATED_SUCCESS": "Booking created successfully", "BOOKING_UPDATED_SUCCESS": "Booking updated successfully", "BOOKING_CANCELLED_SUCCESS": "Booking cancelled successfully", "BOOKING_ACCEPTED_SUCCESS": "Booking accepted successfully", "BOOKING_REJECTED_SUCCESS": "Booking rejected successfully", "BOOKING_FETCH_SUCCESS": "Booking details fetched successfully", "BOOKING_NOT_FOUND": "Booking not found", "BOOKING_ALREADY_EXISTS": "A booking already exists for this time period", "BOOKING_INVALID_DATES": "Invalid booking dates", "BOOKING_DATES_OVERLAP": "Selected dates overlap with existing bookings", "BOOKING_CANNOT_MODIFY": "Cannot modify this booking", "BOOKING_CANNOT_CANCEL": "Can<PERSON> cancel this booking", "BOOKING_INVALID_STATUS": "Invalid booking status", "BOOKING_BOAT_NOT_AVAILABLE": "Boat is not available for selected dates", "BOOKING_INSUFFICIENT_PERMISSIONS": "You don't have permission to perform this action", "BOOKING_CONFLICTING_BOOKING": "Cannot accept this booking as it conflicts with an already accepted booking", "BOOKING_INVALID_FACILITIES": "One or more selected facilities are invalid", "BOOKING_INVALID_PATRON_TYPE": "Invalid patron type selected", "BOOKING_BOAT_NOT_AVAILABLE_FOR_DATES": "The boat is not available for the requested dates", "BOAT_NOT_FOUND_OR_NOT_OWNER": "Boat not found or you are not the owner", "BOOKING_END_DATE_MUST_BE_AFTER_START_DATE": "End date must be after start date", "BOOKING_INVALID_DURATION": "Invalid booking duration", "BOOKING_CALCULATION_ERROR": "Error calculating booking price", "SOMETHING_WENT_WRONG": "Something went wrong. Please try again later.", "BOOKING_DELETED_SUCCESSFULLY": "Booking deleted successfully", "BOOKING_CANCELLED_SUCCESSFULLY": "Booking cancelled successfully", "BOAT_NOT_AVAILABLE": "Boat is not available for the requested dates", "USER_NOT_FOUND": "User not found", "COMMISSION_RATE_CREATED_SUCCESS": "Commission rate created successfully", "COMMISSION_RATE_UPDATED_SUCCESS": "Commission rate updated successfully", "COMMISSION_RATE_FETCH_SUCCESS": "Commission rate retrieved successfully", "COMMISSION_RATE_NOT_FOUND": "No commission rate found, using default values", "FORBIDDEN": "You are not authorized to access this resource", "FAIL_TOKEN_EXPIRED": "Session is expired. Please log in again.", "TOKEN_REQUIRED": "Authorization token is required.", "ACCOUNT_INACTIVE": "Account is inactive or deleted. Please contact support for assistance.", "BOOKING_STATUS_UPDATED_SUCCESS": "Booking status updated successfully", "BOOKING_STATUS_FETCH_SUCCESS": "Booking status fetched successfully", "BOOKING_INVALID_STATUS_TRANSITION": "Invalid booking status transition", "BOOKING_NOT_READY_FOR_PAYMENT": "Booking is not ready for payment", "BOAT_NOT_FOUND_OR_NOT_AVAILABLE": "BOAT_NOT_FOUND_OR_NOT_AVAILABLE"}