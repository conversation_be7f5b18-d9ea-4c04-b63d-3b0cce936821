import mongoose, { Document, Schema } from "mongoose";

export enum FacilityStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export interface IFacility extends Document {
  ownerId: mongoose.Types.ObjectId;
  name: string;
  images: mongoose.Types.ObjectId[]; // Array of File IDs
  price: number;
  status: FacilityStatus;
  createdAt: Date;
  updatedAt: Date;
}

const FacilitySchema = new Schema<IFacility>(
  {
    ownerId: { type: Schema.Types.ObjectId, ref: "Users", required: true },
    name: { type: String, required: true },
    images: [{ type: Schema.Types.ObjectId, ref: "File" }],
    price: { type: Number, required: true },
    status: {
      type: String,
      enum: Object.values(FacilityStatus),
      default: FacilityStatus.ACTIVE,
    },
  },
  { timestamps: true },
);

const Facility = mongoose.model<IFacility>("Facility", FacilitySchema);

export default Facility;
