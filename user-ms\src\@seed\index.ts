import seedAdminUser from "./adminUserSeeder";
import logger from "../../../shared/services/logger.service";

/**
 * Run all seeders in sequence
 */
async function runAllSeeders() {
  try {
    logger.info("Starting seeders...");
    
    // Run admin user seeder
    await seedAdminUser();
    
    // Add more seeders here as needed
    
    logger.info("All seeders completed successfully.");
    process.exit(0);
  } catch (error) {
    logger.error("Error running seeders:", error);
    process.exit(1);
  }
}

// Run all seeders if this file is directly executed
if (require.main === module) {
  runAllSeeders();
}

export default runAllSeeders; 