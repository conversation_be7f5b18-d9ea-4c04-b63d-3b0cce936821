import { StatusCodes } from "http-status-codes";
import {
  generateOtp,
  encrypt,
  comparePassword,
  otpExpire,
} from "../../../shared/helper/utils";
import User, {
  UserCurrency,
  UserCurrentRole,
  UserLanguage,
  UserRole,
  UserStatus,
} from "../../../user-ms/src/models/User";
import { emailSender } from "../../../shared/services/sendMail.service";
import {
  generateRefreshToken,
  generateToken,
} from "../../../shared/helper/auth.helper";
import logger from "../../../shared/services/logger.service";
import { EMAIL_CONSTANT } from "../constant/emailContant";
import { formatUserData } from "../helper/utils";

export const register = async (req: any, res: any) => {
  try {
    const { username, email, password, currency, language, interestedIn, otp } =
      req.body;

    const existingUser = await User.findOne({ email });
    if (existingUser) {
      if (
        existingUser.status === UserStatus.Deleted ||
        existingUser.status === UserStatus.Inactive || existingUser.status === UserStatus.Pending
      ) {
        // Verify OTP
        if (existingUser.otp !== otp) {
          return res
            .status(StatusCodes.BAD_REQUEST)
            .json({ status: false, message: res.__("INVALID_OTP") });
        }
        if (existingUser.otpExpireTime && existingUser.otpExpireTime < new Date()) {
          return res
            .status(StatusCodes.BAD_REQUEST)
            .json({ status: false, message: res.__("OTP_EXPIRED") });
        }

        const hashedPassword = await encrypt(password);
        existingUser.username = username;
        existingUser.currency = currency || UserCurrency.USD;
        existingUser.language = language || UserLanguage.English;
        existingUser.interestedIn = interestedIn || [];
        existingUser.password = hashedPassword;
        existingUser.status = UserStatus.Active;
        await existingUser.save();
        const token = await await generateToken({
          _id: existingUser._id,
        });
 
       return res.status(StatusCodes.CREATED).send({
          status: true,
          message: res.__("ACCOUNT_REGISTERED_SUCCESSFULLY")
        });
      }
      return res
        .status(StatusCodes.BAD_REQUEST)
        .send({ status: false, message: res.__("ACCOUNT_ALREADY_EXISTS") });
    }

    const hashedPassword = await encrypt(password);   

    const user = await User.create({
      username,
      email,
      password: hashedPassword,
      currency: currency || UserCurrency.USD,
      language: language || UserLanguage.English,
      role: UserRole.User,
      currentRole: UserCurrentRole.User,
      status: UserStatus.Pending,
      interestedIn,
      otp: await generateOtp(4),
      otpExpireTime: otpExpire()
    });

    await emailSender(
      email,
      EMAIL_CONSTANT.REGISTER_OTP_EMAIL.subject,
      { username, otp },
      EMAIL_CONSTANT.REGISTER_OTP_EMAIL.templateName
    );

    res.status(StatusCodes.CREATED).send({
      status: true,
      message: res.__("ACCOUNT_REGISTERED_SUCCESSFULLY")
    });
  } catch (error) {
    logger.error(error);
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .send({ status: false, message: res.__("SOMETHING_WENT_WRONG"), error });
  }
};

export const login = async (req: any, res: any) => {
  try {
    const { email, password } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
      return res
        .status(StatusCodes.NOT_FOUND)
        .json({ status: false, message: res.__("USER_NOT_FOUND") });
    }

    if (
      user.status === UserStatus.Inactive ||
      user.status === UserStatus.Deleted
    ) {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ status: false, message: res.__("ACCOUNT_INACTIVE") });
    }

    if (user.status === UserStatus.Pending) {
        // Send OTP to user's email
        const otp = await generateOtp(4) // Generate a 4-digit OTP
        user.otp = otp;
        user.otpExpireTime = otpExpire() // OTP expires in 5 minutes
        await user.save();
        await emailSender(email, EMAIL_CONSTANT.REGISTER_OTP_EMAIL.subject, { username: user.username, otp }, EMAIL_CONSTANT.REGISTER_OTP_EMAIL.templateName);
        return res.status(StatusCodes.OK).json({ status: false, message: res.__("ACCOUNT_PENDING") });
    }

    const isPasswordValid = await comparePassword(password, user.password);
    if (!isPasswordValid) {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ status: false, message: res.__("INVALID_CREDENTIALS") });
    }
    if (user.status === UserStatus.Pending) {
      user.status = UserStatus.Active;
      await user.save();
    }

    // Generate JWT token here
    const token = await generateToken({
      _id: user._id,
    });

    // Generate refresh token with longer expiration
    const refreshToken = await generateRefreshToken({
      _id: user._id,
    });

    // Set refresh token as HTTP-only cookie
    // res.cookie("refreshToken", refreshToken, {
    //   httpOnly: true,
    //   secure: process.env.NODE_ENV === "production", // Use secure cookies in production
    //   maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    //   sameSite: "strict",
    // });

    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("LOGIN_SUCCESS"),
      token,
      refreshToken,
      data: await formatUserData(user),
    });
  } catch (error) {
    logger.error(error);
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .send({ status: false, message: res.__("SOMETHING_WENT_WRONG"), error });
  }
};

export const verifyAccount = async (req: any, res: any) => {
  try {
    const { email, otp } = req.body;
    const user = await User.findOne({ email });
    if (!user) {
      return res
        .status(StatusCodes.NOT_FOUND)
        .json({ status: false, message: res.__("USER_NOT_FOUND") });
    }
    if (user.otp !== otp) {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("INVALID_OTP") });
    }
    if (user.otpExpireTime && user.otpExpireTime < new Date()) {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("OTP_EXPIRED") });
    }
    user.status = UserStatus.Active;
    await user.save();
    res
      .status(StatusCodes.OK)
      .json({ status: true, message: res.__("ACCOUNT_VERIFICATION_SUCCESS") });
  } catch (error) {
    logger.error(error);
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .send({ status: false, message: res.__("SOMETHING_WENT_WRONG"), error });
  }
};

export const sendOtp = async (req: any, res: any) => {
  try {
    const { email } = req.body;

    const user = await User.findOne({ email });

    const otp = await generateOtp(4) // Generate a 4-digit OTP
    const otpExpireTime = otpExpire() // OTP expires in 5 minutes
    if (!user) {
      await User.create({
        email,
        status: UserStatus.Pending,
        otp,
        otpExpireTime
      });
    } else {
      user.otp = otp;
      user.otpExpireTime = otpExpireTime
      await user.save();
    }

    await emailSender(
      email,
      EMAIL_CONSTANT.REGISTER_OTP_EMAIL.subject,
      { username: "There", otp },
      EMAIL_CONSTANT.REGISTER_OTP_EMAIL.templateName
    );

    res
      .status(StatusCodes.OK)
      .json({ status: true, message: res.__("OTP_SENT") });
  } catch (error) {
    logger.error(error);
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .send({ status: false, message: res.__("SOMETHING_WENT_WRONG"), error });
  }
}

export const forgotPasswordSendOTP = async (req: any, res: any) => {
  try {
    const { email } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
      return res
        .status(StatusCodes.NOT_FOUND)
        .json({ status: false, message: res.__("USER_NOT_FOUND") });
    }

    const otp = await generateOtp(4);
    user.otp = otp;
    user.otpExpireTime = otpExpire(); // 5 minutes
    await user.save();

    await emailSender(
      email,
      EMAIL_CONSTANT.RESET_PASSWORD_EMAIL.subject,
      { username: user.username, otp },
      EMAIL_CONSTANT.RESET_PASSWORD_EMAIL.templateName
    );

    res
      .status(StatusCodes.OK)
      .json({ status: true, message: res.__("OTP_SENT") });
  } catch (error) {
    logger.error(error);
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .send({ status: false, message: res.__("SOMETHING_WENT_WRONG"), error });
  }
};

export const resetPassword = async (req: any, res: any) => {
  try {
    const { email, password } = req.body;

    const user = await User.findOne({
      email,
    });

    if (!user) {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("USER_NOT_FOUND") });
    }

    const hashedPassword = await encrypt(password);
    user.password = hashedPassword;
    user.otp = null;
    user.otpExpireTime = null;
    await user.save();

    res
      .status(StatusCodes.OK)
      .json({ status: true, message: res.__("PASSWORD_RESET_SUCCESS") });
  } catch (error) {
    logger.error(error);
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .send({ status: false, message: res.__("SOMETHING_WENT_WRONG"), error });
  }
};

export const getRefreshToken = async (req: any, res: any) => {
  try {
    const { userId } = req.body
    // Extract refresh token from request
    // const refreshToken = req.body.refreshToken || req.cookies.refreshToken;

    // if (!refreshToken) {
    //   return res.status(StatusCodes.BAD_REQUEST).json({
    //     status: false,
    //     message: res.__("REFRESH_TOKEN_REQUIRED"),
    //   });
    // }

    // // Verify refresh token
    // const decoded = jwt.verify(
    //   refreshToken,
    //   global.config.JWT_REFRESH_SECRET
    // );

    // Find user by ID from decoded token
    const user = await User.findById(userId);

    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("USER_NOT_FOUND"),
      });
    }

    if (user.status !== UserStatus.Active) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("ACCOUNT_INACTIVE"),
      });
    }

    // Generate new access token
    const accessToken = generateToken({
      _id: user._id
    });

    // Generate refresh token with longer expiration
    const newRefreshToken = await generateRefreshToken({
      _id: user._id,
    });

    // Return new access token
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("TOKEN_REFRESHED"),
      token: accessToken,
      refreshToken: newRefreshToken,
    });
  } catch (error: any) {
    logger.error("Error refreshing token", { error });

    if (error.name === "TokenExpiredError") {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("REFRESH_TOKEN_EXPIRED"),
      });
    }

    if (error.name === "JsonWebTokenError") {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("INVALID_REFRESH_TOKEN"),
      });
    }

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};
