import File from "../../../shared/models/Files";
import Reward, { RewardStatus } from "../../../shared/models/Reward";
import { IUser } from "../models/User";

const formatUserData = async (user: IUser) => {
  const findFile = await File.findById(user.avatar);
  const getRewards = await Reward.findOne({ userId: user._id, status: RewardStatus.ACTIVE });
  return {
    _id: user._id,
    username: user.username,
    email: user.email,
    currency: user.currency,
    language: user.language,
    interestedIn: user.interestedIn,
    currentRole: user.currentRole,
    status: user.status,
    avatar: user?.avatar ?? '',
    avatarUrl: user?.avatar ? global.config.FILE_BASE_URL + findFile?.location : '',
    deviceToken: user.deviceToken,
    deviceType: user.deviceType,
    createdAt: user.createdAt,
    rewards: getRewards,
  };
};

export { formatUserData };
