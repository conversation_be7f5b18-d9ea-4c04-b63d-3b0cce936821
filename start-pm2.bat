@echo off
setlocal

REM Set default environment to dev if not provided
IF "%1"=="" (
    SET ENV=dev
) ELSE (
    SET ENV=%1
)

echo Starting Sea Escape Microservices with PM2 in %ENV% environment...
echo.

REM Stop and delete any existing PM2 processes to avoid conflicts
call pm2 delete all

echo.
echo Starting microservices...
echo.

REM Start API service first to ensure proper port allocation
call pm2 start ecosystem.config.js --only api --env %ENV%
timeout /t 2 > nul

REM Start all other microservices
call pm2 start ecosystem.config.js --only auth-ms,user-ms,boat-ms,booking-ms,wishlist-ms,affiliate-ms,notification-ms,newsletter-ms,faq-ms,card-ms,contact-us-ms,privacy-policy-ms,terms-condition-ms,about-us-ms,social-media-ms,payment-ms,wallet-ms,reviews-ms --env %ENV%

echo.
echo All microservices started successfully with PM2!
echo Use "npm run pm2:logs" to view logs
echo Use "npm run pm2:status" to check status
echo Use "npm run pm2:monitor" to monitor in real-time
echo.

REM Pause so the user can see the output
pause