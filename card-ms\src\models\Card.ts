import mongoose, { Schema, Document } from "mongoose";

export enum CardStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export interface ICard extends Document {
  ownerId: mongoose.Types.ObjectId;
  cardType: string;
  lastFourDigits: string;
  expiryMonth: number;
  expiryYear: number;
  cardholderName: string;
  isDefault: boolean;
  status: CardStatus;
  stripeSourceId: string;        // Changed from stripePaymentMethodId to stripeSourceId
  stripeCustomerId: string;      // Added to store user's Stripe customer ID
  createdAt: Date;
  updatedAt: Date;
}

const CardSchema: Schema = new Schema(
  {
    ownerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    cardType: {
      type: String,
      required: true,
    },
    lastFourDigits: {
      type: String,
      required: true,
    },
    expiryMonth: {
      type: Number,
      required: true,
    },
    expiryYear: {
      type: Number,
      required: true,
    },
    cardholderName: {
      type: String,
      required: true,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
      enum: Object.values(CardStatus),
      default: CardStatus.ACTIVE,
    },
    stripeSourceId: {
      type: String,
      required: true,
      unique: true,
      description: "Stripe source ID created from card token",
    },
    stripeCustomerId: {
      type: String,
      required: true,
      description: "User's Stripe customer ID",
    },
  },
  {
    timestamps: true,
  },
);

// Pre-save middleware to handle default card
CardSchema.pre("save", async function (next) {
  const card = this;

  if (card.isDefault) {
    // If this card is being set as default, unset any other default cards for this owner
    await Card.updateMany(
      {
        ownerId: card.ownerId,
        _id: { $ne: card._id },
        status: { $ne: CardStatus.DELETED },
      },
      { isDefault: false },
    );
  }

  next();
});

const Card = mongoose.model<ICard>("Card", CardSchema);

export default Card;
