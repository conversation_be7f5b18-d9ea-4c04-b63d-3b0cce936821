import cron from 'node-cron';
import logger from '../../../shared/services/logger.service';
import Booking, { BookingStatus } from '../models/Booking';

/**
 * Booking Cron Service
 * Handles automatic booking status updates based on time
 */
class BookingCronService {
  private static instance: BookingCronService;
  private isRunning: boolean = false;

  private constructor() {}

  public static getInstance(): BookingCronService {
    if (!BookingCronService.instance) {
      BookingCronService.instance = new BookingCronService();
    }
    return BookingCronService.instance;
  }

  /**
   * Start all cron jobs
   */
  public startCronJobs(): void {
    if (this.isRunning) {
      logger.info('Booking cron jobs are already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting booking cron jobs');

    // Run every 10 minutes to check for expired pending bookings
    cron.schedule('*/10 * * * *', async () => {
      await this.processExpiredPendingBookings();
    });

    // Run every 15 minutes to check for completed bookings
    cron.schedule('*/15 * * * *', async () => {
      await this.processCompletedBookings();
    });

    logger.info('Booking cron jobs started successfully');
  }

  /**
   * Stop all cron jobs
   */
  public stopCronJobs(): void {
    this.isRunning = false;
    logger.info('Booking cron jobs stopped');
  }

  /**
   * Process bookings that have expired without being accepted
   * If booking start date has passed and status is still Pending, reject it
   */
  private async processExpiredPendingBookings(): Promise<void> {
    try {
      const now = new Date();
      
      // Find pending bookings where start date has passed
      const expiredBookings = await Booking.find({
        status: BookingStatus.Pending,
        startDate: { $lt: now } // Start date has passed
      }).populate('boatId', 'name ownerId');

      logger.info(`Found ${expiredBookings.length} expired pending bookings to reject`);

      for (const booking of expiredBookings) {
        await this.rejectExpiredBooking(booking);
      }
    } catch (error) {
      logger.error('Error processing expired pending bookings', {
        service: 'booking-ms',
        error: error
      });
    }
  }

  /**
   * Process bookings that have completed (end date has passed)
   * If booking end date has passed and status is Accepted, mark as Completed
   */
  private async processCompletedBookings(): Promise<void> {
    try {
      const now = new Date();
      
      // Find accepted bookings where end date has passed
      const completedBookings = await Booking.find({
        status: BookingStatus.Accepted,
        endDate: { $lt: now } // End date has passed
      }).populate('boatId', 'name ownerId');

      logger.info(`Found ${completedBookings.length} bookings to mark as completed`);

      for (const booking of completedBookings) {
        await this.completeBooking(booking);
      }
    } catch (error) {
      logger.error('Error processing completed bookings', {
        service: 'booking-ms',
        error: error
      });
    }
  }

  /**
   * Reject an expired pending booking
   */
  private async rejectExpiredBooking(booking: any): Promise<void> {
    try {
      await Booking.findByIdAndUpdate(booking._id, {
        status: BookingStatus.Rejected,
        rejectionReason: 'Booking expired - start date passed without owner acceptance',
        updatedAt: new Date()
      });

      logger.info(`Booking ${booking._id} automatically rejected due to expiration`, {
        service: 'booking-ms',
        bookingId: booking._id,
        boatId: booking.boatId?._id,
        startDate: booking.startDate,
        endDate: booking.endDate
      });

    } catch (error) {
      logger.error(`Error rejecting expired booking ${booking._id}`, {
        service: 'booking-ms',
        bookingId: booking._id,
        error: error
      });
    }
  }

  /**
   * Mark a booking as completed
   */
  private async completeBooking(booking: any): Promise<void> {
    try {
      await Booking.findByIdAndUpdate(booking._id, {
        status: BookingStatus.Completed,
        updatedAt: new Date()
      });

      logger.info(`Booking ${booking._id} automatically marked as completed`, {
        service: 'booking-ms',
        bookingId: booking._id,
        boatId: booking.boatId?._id,
        startDate: booking.startDate,
        endDate: booking.endDate
      });

    } catch (error) {
      logger.error(`Error completing booking ${booking._id}`, {
        service: 'booking-ms',
        bookingId: booking._id,
        error: error
      });
    }
  }
}

// Export singleton instance
export default BookingCronService.getInstance();
