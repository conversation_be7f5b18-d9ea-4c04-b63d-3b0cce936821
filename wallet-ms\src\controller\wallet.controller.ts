import { StatusCodes } from "http-status-codes";
import Wallet, { WalletStatus } from "../models/Wallet";
import Transaction, {
  TransactionStatus,
  TransactionType,
  TransactionSource,
} from "../models/Transaction";
import Withdrawal, { WithdrawalStatus } from "../models/Withdrawal";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";
import logger from "../../../shared/services/logger.service";
import mongoose from "mongoose";
import StripeService from "../../../payment-ms/src/services/stripe.service";
import User, { UserCurrency } from "../../../user-ms/src/models/User";

// Get wallet by user ID or create if doesn't exist
export const getOrCreateWallet = async (userId: mongoose.Types.ObjectId) => {
  try {
    let wallet = await Wallet.findOne({
      userId,
      status: WalletStatus.ACTIVE,
    });

    if (!wallet) {
      // Get user to get their currency preference
      const user = await User.findById(userId);
      if (!user) {
        throw new Error("User not found");
      }

      // Ensure user.currency is a valid UserCurrency enum value
      const userCurrency = (user.currency as UserCurrency) || UserCurrency.USD;

      wallet = new Wallet({
        userId,
        balance: 0,
        currency: userCurrency,
        status: WalletStatus.ACTIVE,
      });
      await wallet.save();

      logger.info("New wallet created for user", {
        service: "wallet-ms",
        userId: userId.toString(),
        currency: userCurrency,
      });
    }

    return wallet;
  } catch (error) {
    logger.error("Error getting or creating wallet", {
      service: "wallet-ms",
      userId,
      error,
    });
    throw error;
  }
};

// Get user wallet balance
export const getWalletBalance = async (req: any, res: any) => {
  try {
    const userId = req.user._id;

    const wallet = await getOrCreateWallet(userId);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("WALLET_BALANCE_FETCH_SUCCESS"),
      data: {
        balance: wallet.balance,
      },
    });
  } catch (error) {
    logger.error("Error in getWalletBalance", {
      service: "wallet-ms",
      userId: req.user._id,
      error,
    });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get transaction history
export const getTransactionHistory = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10, type = "all" } = req.query;
    const pageNumber = parseInt(page as string);
    const limitNumber = parseInt(limit as string);

    const skip = (pageNumber - 1) * limitNumber;

    // Create filter object based on transaction type
    const filter: any = { userId };

    if (type === "sent") {
      filter.type = TransactionType.DEBIT;
    } else if (type === "received") {
      filter.type = TransactionType.CREDIT;
    }

    const transactions = await Transaction.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNumber);

    const totalCount = await Transaction.countDocuments(filter);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("TRANSACTION_HISTORY_FETCH_SUCCESS"),
      data: {
        transactions,
        pagination: {
          page: pageNumber,
          size: limitNumber,
          totalCount: totalCount,
          totalPages: Math.ceil(totalCount / limitNumber),
        },
      },
    });
  } catch (error) {
    logger.error("Error in getTransactionHistory", {
      service: "wallet-ms",
      userId: req.user._id,
      error,
    });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Request withdrawal
export const requestWithdrawal = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const { amount, bankHolderName, bankName, accountNumber, routingNumber } =
      req.body;

    if (!amount || amount <= 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("WITHDRAWAL_INVALID_AMOUNT"),
      });
    }

    // Get wallet and check if balance is sufficient
    const wallet = await Wallet.findOne({
      userId,
      status: WalletStatus.ACTIVE,
    });

    if (!wallet) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("WALLET_NOT_FOUND"),
      });
    }

    if (wallet.balance < amount) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("WITHDRAWAL_INSUFFICIENT_BALANCE"),
      });
    }

    // Get user details to retrieve their currency preference
    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("USER_NOT_FOUND"),
      });
    }

    // Ensure user.currency is a valid UserCurrency enum value
    const userCurrency = (user.currency as UserCurrency) || UserCurrency.USD;
    if (!Object.values(UserCurrency).includes(userCurrency)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("INVALID_CURRENCY"),
      });
    }

    // Create withdrawal request with PROCESSING status
    const withdrawal = new Withdrawal({
      userId,
      amount,
      bankHolderName,
      bankName,
      accountNumber,
      routingNumber,
      currency: userCurrency, // Save currency in withdrawal
      status: WithdrawalStatus.PROCESSING,
    });

    await withdrawal.save();

    // Create transaction with PENDING status
    const transaction = new Transaction({
      userId,
      amount,
      type: TransactionType.DEBIT,
      status: TransactionStatus.PENDING,
      source: TransactionSource.WITHDRAWAL,
      sourceId: withdrawal._id,
      description: `Withdrawal request to ${bankName} account ${accountNumber.slice(-4)}`,
      currency: userCurrency, // Save currency in transaction
    });

    await transaction.save();

    // Update withdrawal with transaction ID
    withdrawal.transactionId = transaction._id as mongoose.Types.ObjectId;
    await withdrawal.save();

    // Check if user already has a Stripe account ID associated
    let stripeAccountId = user.merchantAccountId;

    // If no Stripe account ID exists, create one
    if (!stripeAccountId) {
      try {
        // Create a Stripe Express account
        const accountResult = await StripeService.createExpressAccount({
          firstName: bankHolderName.split(" ")[0],
          lastName: bankHolderName.split(" ").slice(1).join(" "),
          email: user.email,
          accountNumber,
          routingNumber,
          bankName,
          currency: userCurrency,
        });

        if (accountResult.success && accountResult.accountId) {
          stripeAccountId = accountResult.accountId;

          // In a real implementation, you'd update the user record with this Stripe account ID
          // await User.findByIdAndUpdate(userId, { merchantAccountId: stripeAccountId });

          logger.info("Created new Stripe Express account for user", {
            service: "wallet-ms",
            userId,
            stripeAccountId,
            currency: userCurrency,
          });
        } else {
          logger.error("Failed to create Stripe Express account", {
            service: "wallet-ms",
            userId,
            currency: userCurrency,
            error: accountResult.error,
          });

          // Continue with the withdrawal process anyway
        }
      } catch (error) {
        logger.error("Error creating Stripe Express account", {
          service: "wallet-ms",
          userId,
          currency: userCurrency,
          error,
        });
        // Continue with the withdrawal process
      }
    }

    // Process withdrawal through Stripe
    const withdrawalResult = await StripeService.processWithdrawalToBank(
      amount,
      {
        accountHolderName: bankHolderName,
        bankName: bankName,
        accountNumber: accountNumber,
        routingNumber: routingNumber,
        currency: userCurrency,
        email: user.email,
      },
      stripeAccountId
    );

    if (withdrawalResult.success) {
      // Update withdrawal status to COMPLETED
      withdrawal.status = WithdrawalStatus.COMPLETED;
      // Store the external reference ID from Stripe
      if (withdrawalResult.transfer?.id) {
        withdrawal.externalReferenceId = withdrawalResult.transfer.id;
      }
      await withdrawal.save();

      // Update transaction status to COMPLETED
      transaction.status = TransactionStatus.COMPLETED;
      transaction.reference = withdrawal.externalReferenceId;
      if (withdrawalResult.transfer) {
        transaction.metadata = withdrawalResult.transfer;
      }
      await transaction.save();

      // Update wallet balance
      wallet.balance -= amount;
      await wallet.save();

      logger.info("Withdrawal processed successfully", {
        service: "wallet-ms",
        userId,
        amount,
        currency: userCurrency,
        withdrawalId: withdrawal._id,
        transactionId: transaction._id,
      });
    } else {
      // Update withdrawal status to FAILED
      withdrawal.status = WithdrawalStatus.FAILED;
      withdrawal.rejectionReason =
        withdrawalResult.error || "Withdrawal processing failed";
      await withdrawal.save();

      // Update transaction status to FAILED
      transaction.status = TransactionStatus.FAILED;
      if (withdrawalResult.error) {
        transaction.metadata = { error: withdrawalResult.error };
      }
      await transaction.save();

      logger.error("Withdrawal processing failed", {
        service: "wallet-ms",
        userId,
        amount,
        currency: userCurrency,
        withdrawalId: withdrawal._id,
        transactionId: transaction._id,
        error: withdrawalResult.error,
      });

      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("WITHDRAWAL_PROCESSING_FAILED"),
        error: withdrawalResult.error,
      });
    }

    // Log the withdrawal request
    const changes = validateAndGetChanges({}, withdrawal.toJSON());
    await createActivityLog(
      "withdrawals",
      withdrawal._id,
      "CREATE",
      changes,
      userId
    );

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: req.__("WITHDRAWAL_REQUEST_SUCCESS"),
      data: withdrawal,
    });
  } catch (error) {
    logger.error("Error in requestWithdrawal", {
      service: "wallet-ms",
      userId: req.user._id,
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get withdrawal history
export const getWithdrawalHistory = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10 } = req.query;
    const pageNumber = parseInt(page as string);
    const limitNumber = parseInt(limit as string);

    const skip = (pageNumber - 1) * limitNumber;

    const withdrawals = await Withdrawal.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNumber);

    const totalCount = await Withdrawal.countDocuments({ userId });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("WITHDRAWAL_HISTORY_FETCH_SUCCESS"),
      data: {
        withdrawals,
        pagination: {
          page: pageNumber,
          size: limitNumber,
          totalCount: totalCount,
          totalPages: Math.ceil(totalCount / limitNumber),
        },
      },
    });
  } catch (error) {
    logger.error("Error in getWithdrawalHistory", {
      service: "wallet-ms",
      userId: req.user._id,
      error,
    });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
