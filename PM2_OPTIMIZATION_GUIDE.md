# PM2 Optimization Guide for Sea Escape Microservices

## Overview

This guide explains the optimizations made to the PM2 configuration to address memory leaks and high CPU usage issues in the Sea Escape microservices architecture.

## Problem Identified

The original configuration had several issues that led to memory leaks and high CPU usage:

1. **Excessive Memory Allocation**: All microservices were allocated 1-2GB of memory, which was unnecessary for most services and led to memory overcommitment.
2. **No CPU Usage Limits**: Services could consume unlimited CPU, causing system-wide performance degradation.
3. **Lack of Garbage Collection**: No explicit garbage collection settings, allowing memory to accumulate.
4. **Simultaneous Startup**: All services started at once, causing resource spikes during initialization.
5. **Insufficient Restart Strategies**: Basic restart configuration without proper error handling or recovery mechanisms.

## Optimizations Implemented

### 1. Memory Optimization

Memory limits have been adjusted based on service importance and expected load:

- API Gateway: 512MB (reduced from 2GB)
- Core Services (auth, user, boat, booking, payment, wallet, chat): 256MB
- Business Services (wishlist, reviews, card, notification, mail): 200MB
- Content Services (static pages, FAQ, etc.): 150MB

### 2. CPU Usage Control

CPU limits have been implemented to prevent any single service from monopolizing system resources:

- API Gateway: 30%
- Core Services: 20%
- Business Services: 15%
- Content Services: 10%

### 3. Memory Leak Prevention

Several strategies have been implemented to prevent memory leaks:

- **Scheduled Restarts**: All services restart automatically every 12 hours (`cron_restart: "0 */12 * * *"`) to clear accumulated memory.
- **Garbage Collection**: Added `--expose-gc` flag to enable manual garbage collection.
- **Memory Limits**: Set `max_memory_restart` to automatically restart services when they exceed their memory allocation.
- **Node.js Memory Flags**: Added `--max-old-space-size` to limit V8 heap size.

### 4. Improved Restart Strategies

- **Exponential Backoff**: Added `exp_backoff_restart_delay: 100` to prevent rapid restart loops.
- **Kill Timeout**: Set `kill_timeout: 5000` to ensure proper cleanup before restart.
- **Restart Delay**: Added `restart_delay: 3000` to allow system resources to stabilize between restarts.
- **Maximum Restarts**: Limited to `max_restarts: 10` to prevent infinite restart loops.

### 5. Staggered Startup

Implemented a staged startup process in `start-pm2-optimized.sh` and `start-pm2-optimized.bat`:

1. API Gateway starts first
2. Critical services (auth, user, payment)
3. Core business services (boat, booking, wallet)
4. Secondary services (wishlist, reviews, card)
5. Communication services (notification, mail, chat)
6. Content services (newsletter, faq, affiliate)
7. Static content services (about-us, privacy-policy, terms-condition, contact-us)
8. Remaining services (social-media, changelogs)

### 6. Monitoring Tools

Added a resource monitoring script (`pm2-monitor.js`) that:

- Tracks memory and CPU usage for all services
- Logs resource usage over time
- Alerts when services exceed defined thresholds
- Can run as a one-time check or continuous monitor

## How to Use the Optimized Configuration

### Starting Services (Optimized Method)

```bash
npm run pm2:start:optimized
```

This command will start all services in a staggered manner to prevent resource spikes.

### Monitoring Resources

```bash
npm run pm2:resource-check    # One-time resource check
npm run pm2:resource-monitor  # Continuous monitoring
```

### Other Useful Commands

```bash
npm run pm2:status      # Check status of all services
npm run pm2:logs        # View logs
npm run pm2:monitor     # Real-time monitoring dashboard
npm run pm2:reload-all  # Zero-downtime reload of all services
```

## Best Practices for Ongoing Maintenance

1. **Regular Monitoring**: Use `pm2:resource-monitor` to track resource usage trends.
2. **Adjust Resources as Needed**: Modify memory and CPU limits based on actual usage patterns.
3. **Implement Log Rotation**: Set up log rotation to prevent disk space issues.
4. **Consider Scaling**: For high-traffic services, consider increasing instances and using load balancing.
5. **Update Node.js**: Keep Node.js updated to benefit from performance improvements and memory management enhancements.

## Troubleshooting

If you still encounter memory issues:

1. **Identify Leaking Services**: Use `pm2:resource-monitor` to identify which services are consuming excessive resources.
2. **Check for Memory Leaks**: Review code for common memory leak patterns (event listeners, closures, etc.).
3. **Adjust Memory Limits**: Increase memory limits for services that legitimately need more resources.
4. **Implement Clustering**: For CPU-bound services, increase the number of instances to distribute load.
5. **Consider Service Splitting**: Break down large services into smaller, more focused microservices.

## Conclusion

These optimizations should significantly reduce memory leaks and CPU usage across the Sea Escape microservices architecture. The combination of proper resource limits, improved restart strategies, staggered startup, and monitoring tools provides a robust foundation for stable operation.