import { Router } from "express";
import {
  createPlace,
  getPlace,
  getAllPlaces,
  updatePlace,
  deletePlace,
  uploadImage,
  removeRecommendedPlaceImages,
} from "../controller/recommendedPlace.controller";
import {
  idParamValidator,
  uploadImageValidator,
} from "../validator/recommendedPlace.validator";
import { uploadFiles } from "../../../shared/middleware/fileUpload.middleware";

const router = Router();

router.post("/", uploadFiles("places", "images"), createPlace);
router.get("/", getAllPlaces);
router.get("/:id", idParamValidator, getPlace);
router.put(
  "/:id",
  idParamValidator,
  uploadFiles("places", "images"),
  updatePlace,
);
router.delete("/:id", idParamValidator, deletePlace);
router.post(
  "/:id/images",
  idParamValidator,
  uploadImageValidator,
  uploadFiles("places", "images"),
  uploadImage
);
router.delete("/:id/images", idParamValidator, removeRecommendedPlaceImages);

export default router;
