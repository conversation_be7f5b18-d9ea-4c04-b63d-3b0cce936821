import { celebrate, Joi, Segments } from "celebrate";
import { PlaceStatus } from "../models/RecommendedPlace";

export const placeCreateBodyValidator = Joi.object().keys({
  name: Joi.string().required(),
  description: Joi.string().optional().allow(null, ""),
  status: Joi.string()
    .valid(...Object.values(PlaceStatus))
    .optional(),
});

export const placeUpdateBodyValidator = Joi.object().keys({
  name: Joi.string().required(),
  description: Joi.string().optional().allow(null, ""),
  status: Joi.string()
    .valid(...Object.values(PlaceStatus))
    .optional(),
});
export const createPlaceValidator = celebrate({
  [Segments.BODY]: placeCreateBodyValidator,
});

export const updatePlaceValidator = celebrate({
  [Segments.BODY]: placeUpdateBodyValidator,
});

export const idParamValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string().required(),
  }),
});

export const uploadImageValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string().required(),
  }),
});
