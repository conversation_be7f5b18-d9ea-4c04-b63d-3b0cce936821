import express from "express";
import cors from "cors";
import * as config from "../shared/config.json";
import i18n from "./src/services/i18n.service";
import routes from "./src/routes/index";
import connectDB from "../shared/db";
import { authenticateJWT } from "../shared/middleware/auth";
import responseFormatter from "../shared/middleware/responseFormatter.middleware";
import logger from "../shared/services/logger.service";
import HandleErrorMessage from "../shared/middleware/validator";

require("dotenv").config();

const app = express();

const environment = process.env.NODE_ENV! || "dev";
const envConfig: any = JSON.parse(JSON.stringify(config))[environment];

try {
  connectDB();
  logger.info("Database connected successfully", { service: "faq-ms" });
} catch (error: any) {
  logger.error(`Database connection failed: ${error.message}`, {
    service: "faq-ms",
    error,
  });
  process.exit(1);
}

global.config = envConfig;

// Enable CORS for all origins
app.use(cors({ origin: "*" }));

app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(i18n.init);

// Apply response formatter middleware
app.use(responseFormatter);

// Routes with JWT authentication - match the route in the API gateway (/v1/faqs)
app.use("/", authenticateJWT, routes);

// Handle validation errors
app.use(HandleErrorMessage);

app.listen(envConfig.services["faq-ms"].PORT, () => {
  logger.info(
    `FAQ microservice is running on port ${envConfig.services["faq-ms"].PORT}`,
    {
      service: "faq-ms",
      port: envConfig.services["faq-ms"].PORT,
    },
  );
});

export default app;
