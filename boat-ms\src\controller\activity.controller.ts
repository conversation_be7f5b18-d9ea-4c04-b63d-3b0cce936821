import { StatusCodes } from "http-status-codes";
import logger from "../../../shared/services/logger.service";
import Boat, { BoatStatus, RecordType } from "../models/Boat";
import File, { FileType } from "../../../shared/models/Files";
import { getRelativePath } from "../../../shared/middleware/fileUpload.middleware";
import {
  createActivityValidatorBody,
  updateActivityValidatorBody,
} from "../validator/activity.validator";
import { parseJSON } from "../../../shared/helper/utils";
import mongoose, { PipelineStage } from "mongoose";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";
import Wishlist from "../../../wishlist-ms/src/models/Wishlist";
import affiliateCodeService from "../../../shared/services/affiliateCode.service";
import { ReviewStatus } from "../../../reviews-ms/src/models/Review";

export const createActivity = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;

    const activityData = {
      ...req.body,
      availability: parseJSON(req.body.availability, {}),
      status: req.body.status || BoatStatus.PUBLISHED,
      recordType: RecordType.ACTIVITY,
      affiliateCode: req.body.affiliateCode || undefined,
    };

    Object.keys(activityData).forEach((key) => {
      if (activityData[key]?.toString().trim() === "true") {
        activityData[key] = true;
      } else if (activityData[key]?.toString().trim() === "false") {
        activityData[key] = false;
      }
    });

    // Validate body
    const { error } = createActivityValidatorBody.validate(activityData);
    if (error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: error.message,
      });
    }

    // Validate affiliate code if provided
    if (activityData.affiliateCode) {
      const affiliateValidation =
        await affiliateCodeService.validateAffiliateCode(
          activityData.affiliateCode
        );
      if (!affiliateValidation.isValid) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__(affiliateValidation.messageKey),
        });
      }
      // Normalize the affiliate code
      activityData.affiliateCode = affiliateCodeService.normalizeAffiliateCode(
        activityData.affiliateCode
      );
    }

    activityData.ownerId = userId;

    // Handle file uploads
    activityData.attachments = {
      images: [],
      videos: [],
      documents: [],
    };

    // Process images and videos
    console.log("Processing activity files...");
    console.log("req.files structure:", req.files);

    // Handle both array structure (from .any()) and object structure (from .fields())
    let imageFiles = [];
    let videoFiles = [];

    if (req.files) {
      if (Array.isArray(req.files)) {
        // Handle .any() structure - filter by fieldname
        imageFiles = req.files.filter((file: any) => file.fieldname === 'images');
        videoFiles = req.files.filter((file: any) => file.fieldname === 'videos');
      } else {
        // Handle .fields() structure - direct access
        imageFiles = req.files.images || [];
        videoFiles = req.files.videos || [];
      }
    }

    console.log("Image files found:", imageFiles.length);
    console.log("Video files found:", videoFiles.length);

    if (imageFiles.length > 0) {
      const imageIds = await Promise.all(
        imageFiles.map(async (file: any) => {
          const relativePath = getRelativePath(file.path);
          console.log("Processing image file:", file.filename, "at path:", relativePath);

          const newFile = await File.create({
            name: file.filename,
            size: file.size,
            fileType: file.mimetype,
            ext: file.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.IMAGE,
            ownerId: userId,
          });
          console.log("Created image file record with ID:", newFile._id);
          return newFile._id;
        })
      );
      activityData.attachments.images = imageIds;
      console.log("Assigned image IDs to activity:", imageIds);
    }

    // Process videos
    if (videoFiles.length > 0) {
      const videoIds = await Promise.all(
        videoFiles.map(async (file: any) => {
          const relativePath = getRelativePath(file.path);
          console.log("Processing video file:", file.filename, "at path:", relativePath);

          const newFile = await File.create({
            name: file.filename,
            size: file.size,
            fileType: file.mimetype,
            ext: file.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.VIDEO,
            ownerId: userId,
          });
          console.log("Created video file record with ID:", newFile._id);
          return newFile._id;
        })
      );
      activityData.attachments.videos = videoIds;
      console.log("Assigned video IDs to activity:", videoIds);
    }

    // Create the activity
    console.log("Creating activity in database...");
    console.log("Final activity data before save:", JSON.stringify({
      ...activityData,
      attachments: activityData.attachments
    }, null, 2));

    const activity = new Boat(activityData);
    await activity.save();
    console.log("Activity created with ID:", activity._id);
    console.log("Activity attachments after save:", JSON.stringify(activity.attachments, null, 2));

    // Log the creation
    await createActivityLog("boats", activity._id, "CREATE", [], userId);

    // Populate file references
    await activity.populate([
      {
        path: "attachments.images",
        select: "location",
      },
      {
        path: "attachments.videos",
        select: "location",
      },
    ]);

    // Process for response
    const processedActivity: any = activity.toObject();

    if (processedActivity.attachments) {
      if (processedActivity.attachments.images?.length > 0) {
        processedActivity.attachments.images =
          processedActivity.attachments.images.map(
            (img: any) => ({
              id: img._id,
              link: `${global.config.FILE_BASE_URL}${img.location}`
            })
          );
      }
      if (processedActivity.attachments.videos?.length > 0) {
        processedActivity.attachments.videos =
          processedActivity.attachments.videos.map(
            (video: any) => ({
              id: video._id,
              link: `${global.config.FILE_BASE_URL}${video.location}`
            })
          );
      }
    }

    res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("ACTIVITY_CREATED_SUCCESS"),
      data: processedActivity,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getAllActivities = async (req: any, res: any): Promise<any> => {
  try {
    const { page = 1, limit = 10, status, location, search, fromDate, toDate, lat, lng, radius = 50 } = req.query;
    const skip = (page - 1) * limit;
    const userId = req.user._id;

    // Parse location parameters only if they exist
    const hasLocationFilter = lat && lng && !isNaN(parseFloat(lat)) && !isNaN(parseFloat(lng));
    const latitude = hasLocationFilter ? parseFloat(lat) : null;
    const longitude = hasLocationFilter ? parseFloat(lng) : null;
    const radiusInKm = hasLocationFilter ? parseFloat(radius) : null;

    const query: any = {
      recordType: RecordType.ACTIVITY,
      ownerId: { $ne: new mongoose.Types.ObjectId(userId) }
    };

    // Add location constraints only if location filter is provided
    if (hasLocationFilter && latitude !== null && longitude !== null && radiusInKm !== null) {
      query.lat = { $exists: true, $ne: null };
      query.lng = { $exists: true, $ne: null };
      query.$expr = {
        $lte: [
          {
            $multiply: [
              6371, // Earth's radius in kilometers
              {
                $acos: {
                  $add: [
                    {
                      $multiply: [
                        { $sin: { $degreesToRadians: latitude } },
                        { $sin: { $degreesToRadians: "$lat" } }
                      ]
                    },
                    {
                      $multiply: [
                        { $cos: { $degreesToRadians: latitude } },
                        { $cos: { $degreesToRadians: "$lat" } },
                        { $cos: { $degreesToRadians: { $subtract: ["$lng", longitude] } } }
                      ]
                    }
                  ]
                }
              }
            ]
          },
          radiusInKm
        ]
      };
    }

    // Add filters if provided
    if (status) query.status = status;
    if (location) query.location = { $regex: location, $options: "i" };
    if (search) query.name = { $regex: search, $options: "i" };

    // Add date filters for availability (same as boats)
    if (fromDate || toDate) {
      query["availability.start"] = {};
      query["availability.end"] = {};

      if (fromDate) {
        query["availability.start"].$lte = new Date(fromDate);
      }
      if (toDate) {
        query["availability.end"].$gte = new Date(toDate);
      }
    }

    // Normal users can only see their own activities and not deleted ones
    if (req.user.role !== "admin") {
      // query.ownerId = req.user._id;
      query.status = { $ne: BoatStatus.DELETED };
    }

    // Use aggregation to get only required fields based on UI
    const pipeline = [
      { $match: query },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: Number(limit) },
      // Lookup wishlist status
      {
        $lookup: {
          from: "wishlists",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$userId", new mongoose.Types.ObjectId(userId)] },
                  ],
                },
              },
            },
          ],
          as: "wishlist",
        },
      },
      // Lookup active bookings to check booking status
      {
        $lookup: {
          from: "bookings",
          let: { activityId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$activityId"] },
                    { $in: ["$status", ["Pending", "Confirmed", "Accepted", "ReadyForPayment"]] },
                    { $gte: ["$endDate", new Date()] },
                  ],
                },
              },
            },
          ],
          as: "activeBookings",
        },
      },
      // Lookup owner details
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "ownerInfo",
        },
      },
      // Lookup owner avatar
      {
        $lookup: {
          from: "files",
          localField: "ownerInfo.avatar",
          foreignField: "_id",
          as: "ownerAvatarFile",
        },
      },
      // Add isWishlisted and isBooked fields
      {
        $addFields: {
          isWishlisted: { $gt: [{ $size: "$wishlist" }, 0] },
          isBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
        },
      },
      {
        $lookup: {
          from: "files",
          localField: "attachments.images",
          foreignField: "_id",
          as: "attachments.images",
        },
      },
      {
        $lookup: {
          from: "files",
          localField: "attachments.videos",
          foreignField: "_id",
          as: "attachments.videos",
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          description: 1,
          duration: 1,
          location: 1,
          price: 1,
          safetyInstructions: 1,
          availability: 1,
          attachments: 1,
          status: 1,
          createdAt: 1,
          isWishlisted: 1,
          isBooked: 1,
          lat: 1,
          lng: 1,
          affiliateCode: 1,
          ownerInfo: {
            $let: {
              vars: {
                owner: { $arrayElemAt: ["$ownerInfo", 0] },
                avatar: { $arrayElemAt: ["$ownerAvatarFile", 0] },
              },
              in: {
                _id: "$$owner._id",
                name: "$$owner.name",
                email: "$$owner.email",
                currency: "$$owner.currency",
                avatarUrl: {
                  $cond: [
                    { $gt: [{ $size: "$ownerAvatarFile" }, 0] },
                    {
                      $concat: [
                        global.config.FILE_BASE_URL,
                        "$$avatar.location",
                      ],
                    },
                    null,
                  ],
                },
              },
            },
          },
        },
      },
    ];

    const activities = await Boat.aggregate(pipeline as PipelineStage[]);
    const totalActivities = await Boat.countDocuments(query);

    // Process activities data
    const processedActivities = activities.map((activity: any) => {
      if (activity.attachments?.images?.length > 0) {
        activity.attachments.images = activity.attachments.images.map(
          (img: any) => ({
            id: img._id,
            link: `${global.config.FILE_BASE_URL}${img.location}`
          })
        );
      }
      if (activity.attachments?.videos?.length > 0) {
        activity.attachments.videos = activity.attachments.videos.map(
          (video: any) => ({
            id: video._id,
            link: `${global.config.FILE_BASE_URL}${video.location}`
          })
        );
      }
      return activity;
    });

    res.json({
      status: true,
      data: {
        activities: processedActivities,
        totalActivities,
        currentPage: Number(page),
        totalPages: Math.ceil(totalActivities / Number(limit)),
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getActivity = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    // Validate ID formats
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_ACTIVITY_ID"),
      });
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_USER_ID"),
      });
    }

    const pipeline = [
      {
        $match: {
          _id: new mongoose.Types.ObjectId(id),
          recordType: RecordType.ACTIVITY,
        },
      },
      // Lookup wishlist status
      {
        $lookup: {
          from: "wishlists",
          let: { boatId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$userId", new mongoose.Types.ObjectId(userId)] },
                  ],
                },
              },
            },
          ],
          as: "wishlist",
        },
      },
      // Add isWishlisted field
      {
        $addFields: {
          isWishlisted: { $gt: [{ $size: "$wishlist" }, 0] },
        },
      },
      // Lookup owner details
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "ownerInfo",
        },
      },

      // Lookup owner avatar
      {
        $lookup: {
          from: "files",
          localField: "ownerInfo.avatar",
          foreignField: "_id",
          as: "ownerAvatarFile",
        },
      },

      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          description: 1,
          duration: 1,
          location: 1,
          price: 1,
          safetyInstructions: 1,
          availability: 1,
          attachments: 1,
          status: 1,
          createdAt: 1,
          ownerId: 1,
          isWishlisted: 1,
          affiliateCode: 1,
          lat: 1,
          lng: 1,
          ownerInfo: {
            $let: {
              vars: {
                owner: { $arrayElemAt: ["$ownerInfo", 0] },
                avatar: { $arrayElemAt: ["$ownerAvatarFile", 0] },
              },
              in: {
                _id: "$$owner._id",
                name: "$$owner.name",
                email: "$$owner.email",
                currency: "$$owner.currency",
                avatarUrl: {
                  $cond: [
                    { $gt: [{ $size: "$ownerAvatarFile" }, 0] },
                    {
                      $concat: [
                        global.config.FILE_BASE_URL,
                        "$$avatar.location",
                      ],
                    },
                    null,
                  ],
                },
              },
            },
          },
        },
      },
      {
        $lookup: {
          from: "files",
          localField: "attachments.images",
          foreignField: "_id",
          as: "attachments.images",
        },
      },
      {
        $lookup: {
          from: "files",
          localField: "attachments.videos",
          foreignField: "_id",
          as: "attachments.videos",
        },
      },
    ];

    const activities = await Boat.aggregate(pipeline);

    if (activities.length === 0) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ACTIVITY_NOT_FOUND"),
      });
    }

    const activity = activities[0];

    // Process image and video URLs if they exist
    if (activity.attachments?.images?.length > 0) {
      activity.attachments.images = activity.attachments.images.map(
        (img: any) => ({
          id: img._id,
          link: `${global.config.FILE_BASE_URL}${img.location}`
        })
      );
    }
    if (activity.attachments?.videos?.length > 0) {
      activity.attachments.videos = activity.attachments.videos.map(
        (video: any) => ({
          id: video._id,
          link: `${global.config.FILE_BASE_URL}${video.location}`
        })
      );
    }

    res.json({
      status: true,
      data: activity,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Get activity details specifically for booking creation
 * Includes all necessary information for price calculation and booking
 * @param req
 * @param res
 * @returns
 */
export const getActivityForBooking = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_ACTIVITY_ID"),
      });
    }

    // Comprehensive aggregation pipeline for booking requirements
    const pipeline = [
      // Match stage - find activity by ID and ensure it's published
      {
        $match: {
          _id: new mongoose.Types.ObjectId(id),
          recordType: RecordType.ACTIVITY,
          status: BoatStatus.PUBLISHED
        }
      },

      // Add default attachments structure if missing
      {
        $addFields: {
          "attachments.images": { $ifNull: ["$attachments.images", []] },
          "attachments.videos": { $ifNull: ["$attachments.videos", []] },
          "attachments.documents": { $ifNull: ["$attachments.documents", []] },
        },
      },

      // Lookup owner details (required for currency conversion)
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "ownerInfo",
        },
      },

      // Lookup owner avatar
      {
        $lookup: {
          from: "files",
          localField: "ownerInfo.avatar",
          foreignField: "_id",
          as: "ownerAvatarFile",
        },
      },

      // Lookup activity images for display
      {
        $lookup: {
          from: "files",
          let: { imageIds: { $ifNull: ["$attachments.images", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$imageIds" }, 0] },
                    { $in: ["$_id", "$$imageIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "imageFiles",
        },
      },

      // Lookup activity videos
      {
        $lookup: {
          from: "files",
          let: { videoIds: { $ifNull: ["$attachments.videos", []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $gt: [{ $size: "$$videoIds" }, 0] },
                    { $in: ["$_id", "$$videoIds"] },
                    { $ne: ["$status", "deleted"] }
                  ]
                }
              }
            }
          ],
          as: "videoFiles",
        },
      },

      // Check if activity is currently booked for any dates
      {
        $lookup: {
          from: "bookings",
          let: { activityId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$activityId"] }, // boatId field is used for both boats and activities
                    { $in: ["$status", ["Pending", "Confirmed", "Accepted", "ReadyForPayment"]] },
                    { $gte: ["$endDate", new Date()] }, // Only future/active bookings
                  ],
                },
              },
            },
            {
              $project: {
                startDate: 1,
                endDate: 1,
                status: 1
              }
            }
          ],
          as: "activeBookings",
        },
      },

      // Lookup reviews for rating calculation
      {
        $lookup: {
          from: "reviews",
          let: { activityId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$activityId"] }, // boatId field is used for both boats and activities
                    { $eq: ["$status", ReviewStatus.ACTIVE] },
                  ],
                },
              },
            },
          ],
          as: "reviews",
        },
      },

      // Calculate average rating
      {
        $addFields: {
          averageRating: {
            $cond: [
              { $gt: [{ $size: "$reviews" }, 0] },
              { $avg: "$reviews.rating" },
              0,
            ],
          },
          reviewCount: { $size: "$reviews" },
        },
      },

      // Final project stage - format response for booking
      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          recordType: 1,
          description: 1,
          duration: 1,
          location: 1,
          lat: 1,
          lng: 1,

          // Pricing field for activities
          price: 1,

          // Activity-specific fields
          safetyInstructions: 1,

          // Affiliate information
          affiliateCode: 1,

          // Availability information
          availability: 1,

          // Status and dates
          status: 1,
          createdAt: 1,
          updatedAt: 1,

          // Owner information (formatted)
          owner: {
            $let: {
              vars: {
                ownerData: { $arrayElemAt: ["$ownerInfo", 0] },
                avatarFile: { $arrayElemAt: ["$ownerAvatarFile", 0] }
              },
              in: {
                _id: "$$ownerData._id",
                firstName: "$$ownerData.firstName",
                lastName: "$$ownerData.lastName",
                email: "$$ownerData.email",
                currency: "$$ownerData.currency",
                avatar: {
                  $cond: [
                    { $ne: ["$$avatarFile", null] },
                    {
                      id: "$$avatarFile._id",
                      link: { $concat: [global.config.FILE_BASE_URL, "$$avatarFile.location"] }
                    },
                    null
                  ]
                }
              }
            }
          },

          // Activity images
          images: {
            $map: {
              input: "$imageFiles",
              as: "img",
              in: {
                id: "$$img._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$$img.location"] }
              }
            }
          },

          // Activity videos
          videos: {
            $map: {
              input: "$videoFiles",
              as: "video",
              in: {
                id: "$$video._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$$video.location"] }
              }
            }
          },

          // Booking availability status
          isCurrentlyBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
          activeBookings: "$activeBookings",

          // Rating information
          averageRating: { $round: ["$averageRating", 1] },
          reviewCount: 1,
        },
      },
    ];

    const result = await Boat.aggregate(pipeline);

    if (!result || result.length === 0) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ACTIVITY_NOT_FOUND_OR_NOT_AVAILABLE"),
      });
    }

    const activity = result[0];

    // Additional validation for booking requirements
    if (!activity.owner || !activity.owner._id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("ACTIVITY_OWNER_NOT_FOUND"),
      });
    }

    // Check if user is trying to book their own activity
    if (activity.owner._id.toString() === userId.toString()) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_BOOK_OWN_ACTIVITY"),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("ACTIVITY_FETCH_SUCCESS"),
      data: activity,
    });
  } catch (error: any) {
    logger.error(`Error fetching activity for booking: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

export const updateActivity = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { id } = req.params;

    const activity = await Boat.findOne({
      _id: id,
      recordType: RecordType.ACTIVITY,
    });

    if (!activity) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ACTIVITY_NOT_FOUND"),
      });
    }

    // Check if user is the owner of the activity
    if (activity.ownerId.toString() !== userId.toString()) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("ACTIVITY_NOT_OWNER"),
      });
    }

    const updates = {
      ...req.body,
      availability: parseJSON(req.body.availability, activity.availability),
      status: req.body.status || activity.status,
      existingVideos: parseJSON(req.body.existingVideos, activity.attachments.videos),
      existingImages: parseJSON(req.body.existingImages, activity.attachments.images),
    };

    Object.keys(updates).forEach((key) => {
      if (updates[key]?.toString().trim() === "true") {
        updates[key] = true;
      } else if (updates[key]?.toString().trim() === "false") {
        updates[key] = false;
      }
    });

    const { error } = updateActivityValidatorBody.validate(updates);
    if (error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: error.message,
      });
    }

    // Validate affiliate code if provided and changed
    if (
      updates.affiliateCode !== undefined &&
      updates.affiliateCode !== activity.affiliateCode
    ) {
      if (updates.affiliateCode) {
        const affiliateValidation =
          await affiliateCodeService.validateAffiliateCode(
            updates.affiliateCode
          );
        if (!affiliateValidation.isValid) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__(affiliateValidation.messageKey),
          });
        }
        // Normalize the affiliate code
        updates.affiliateCode = affiliateCodeService.normalizeAffiliateCode(
          updates.affiliateCode
        );
      }
    }

    // Initialize attachments object if it doesn't exist
    if (!updates.attachments) {
      updates.attachments = {
        images: activity.attachments.images || [],
        videos: activity.attachments.videos || [],
        documents: activity.attachments.documents || [],
      };
    }

    console.log("Processing activity images...");
    console.log("Existing images from request:", updates.existingImages);
    console.log("Current activity images:", activity.attachments.images);
    console.log("New image files:", req.files?.images?.length || 0);

    // Process images - handle both new uploads and existing images
    let finalImageIds: mongoose.Types.ObjectId[] = [];

    // First, handle existing images if provided
    if (updates.existingImages && Array.isArray(updates.existingImages)) {
      const existingImageIds = updates.existingImages
        .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
        .map((id: string) => new mongoose.Types.ObjectId(id));
      finalImageIds = [...existingImageIds];
      console.log("Processed existing image IDs:", finalImageIds);
    }

    // Then, handle new image uploads if any
    if (req.files && req.files.images && req.files.images.length > 0) {
      console.log("Processing new image uploads...");
      const newImageIds = await Promise.all(
        req.files.images.map(async (file: any) => {
          const relativePath = getRelativePath(file.path);
          console.log("Creating new image file:", file.filename, "at path:", relativePath);

          const newFile = await File.create({
            name: file.filename,
            size: file.size,
            fileType: file.mimetype,
            ext: file.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.IMAGE,
            ownerId: userId,
          });
          console.log("Created new image file with ID:", newFile._id);
          return newFile._id;
        })
      );

      // Add new images to the final array
      finalImageIds = [...finalImageIds, ...newImageIds];
      console.log("Final image IDs after adding new uploads:", finalImageIds);
    }

    // Update attachments with final image array only if we have images to set or if existingImages was provided
    if (finalImageIds.length > 0 || (updates.existingImages && Array.isArray(updates.existingImages))) {
      updates.attachments.images = finalImageIds;
      console.log("Updated attachments.images to:", finalImageIds);
    } else {
      console.log("No image updates needed, keeping existing images");
    }

    console.log("Processing activity videos...");
    console.log("Existing videos from request:", updates.existingVideos);
    console.log("Current activity videos:", activity.attachments.videos);
    console.log("New video files:", req.files?.videos?.length || 0);

    // Process videos - handle both new uploads and existing videos
    let finalVideoIds: mongoose.Types.ObjectId[] = [];

    // First, handle existing videos if provided
    if (updates.existingVideos && Array.isArray(updates.existingVideos)) {
      const existingVideoIds = updates.existingVideos
        .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
        .map((id: string) => new mongoose.Types.ObjectId(id));
      finalVideoIds = [...existingVideoIds];
      console.log("Processed existing video IDs:", finalVideoIds);
    }

    // Then, handle new video uploads if any
    if (req.files && req.files.videos && req.files.videos.length > 0) {
      console.log("Processing new video uploads...");
      const newVideoIds = await Promise.all(
        req.files.videos.map(async (file: any) => {
          const relativePath = getRelativePath(file.path);
          console.log("Creating new video file:", file.filename, "at path:", relativePath);

          const newFile = await File.create({
            name: file.filename,
            size: file.size,
            fileType: file.mimetype,
            ext: file.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.VIDEO,
            ownerId: userId,
          });
          console.log("Created new video file with ID:", newFile._id);
          return newFile._id;
        })
      );

      // Add new videos to the final array
      finalVideoIds = [...finalVideoIds, ...newVideoIds];
      console.log("Final video IDs after adding new uploads:", finalVideoIds);
    }

    // Update attachments with final video array only if we have videos to set or if existingVideos was provided
    if (finalVideoIds.length > 0 || (updates.existingVideos && Array.isArray(updates.existingVideos))) {
      updates.attachments.videos = finalVideoIds;
      console.log("Updated attachments.videos to:", finalVideoIds);
    } else {
      console.log("No video updates needed, keeping existing videos");
    }

    // Get changes before update
    const changes = validateAndGetChanges(activity, updates);

    console.log("Final updates object before save:");
    console.log("- attachments.images:", updates.attachments?.images);
    console.log("- attachments.videos:", updates.attachments?.videos);

    const updatedActivity = await Boat.findByIdAndUpdate(id, updates, {
      new: true,
    }).populate([
      {
        path: "attachments.images",
        select: "location",
      },
      {
        path: "attachments.videos",
        select: "location",
      },
    ]);

    console.log("Activity updated successfully");
    console.log("Updated activity attachments:", updatedActivity?.attachments);

    // Log the update
    if (changes.length > 0) {
      await createActivityLog("boats", id, "UPDATE", changes, userId);
    }

    // Process image and video URLs if they exist
    const processedActivity: any = updatedActivity?.toObject();
    if (processedActivity?.attachments?.images?.length > 0) {
      processedActivity.attachments.images =
        processedActivity.attachments.images.map(
          (img: any) => ({
            id: img._id,
            link: `${global.config.FILE_BASE_URL}${img.location}`
          })
        );
    }
    if (processedActivity?.attachments?.videos?.length > 0) {
      processedActivity.attachments.videos =
        processedActivity.attachments.videos.map(
          (video: any) => ({
            id: video._id,
            link: `${global.config.FILE_BASE_URL}${video.location}`
          })
        );
    }

    res.json({
      status: true,
      message: res.__("ACTIVITY_UPDATED_SUCCESS"),
      data: processedActivity,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const deleteActivity = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;

    // Check if activity exists and is of type activity
    const activity = await Boat.findOne({
      _id: id,
      recordType: RecordType.ACTIVITY,
    });
    if (!activity) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ACTIVITY_NOT_FOUND"),
      });
    }

    // Check if user is the owner or admin
    if (
      activity.ownerId.toString() !== req.user._id.toString() &&
      req.user.role !== "admin"
    ) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("ACTIVITY_NOT_OWNER"),
      });
    }

    // Get changes before update
    const changes = validateAndGetChanges(activity, {
      status: BoatStatus.DELETED,
    });

    // Delete associated files (optional)
    const filesToDelete = [];
    if (activity.attachments?.images?.length > 0) {
      filesToDelete.push(...activity.attachments.images);
    }
    if (activity.attachments?.videos?.length > 0) {
      filesToDelete.push(...activity.attachments.videos);
    }
    if (filesToDelete.length > 0) {
      await File.deleteMany({ _id: { $in: filesToDelete } });
    }

    // Update activity status to deleted
    await Boat.findByIdAndUpdate(id, { status: BoatStatus.DELETED });

    // Log the deletion
    await createActivityLog("boats", id, "DELETE", changes, req.user._id);

    res.json({
      status: true,
      message: res.__("ACTIVITY_DELETED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Check wishlist status for an activity
 */
export const checkWishlistStatus = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { id } = req.params;

    // Validate activity ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("ACTIVITY_INVALID_ID"),
      });
    }

    // Check if the activity exists
    const activity = await Boat.findOne({
      _id: id,
      recordType: RecordType.ACTIVITY,
    });

    if (!activity) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ACTIVITY_NOT_FOUND"),
      });
    }

    // Check if in wishlist
    const isWishlisted = await Wishlist.exists({ userId, boatId: id });

    res.json({
      status: true,
      data: {
        isWishlisted: !!isWishlisted,
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Add images to an activity
 * @param req
 * @param res
 * @returns
 */
export const addActivityImages = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = req.user._id;
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("NO_FILES_UPLOADED"),
      });
    }

    // Find the activity and verify ownership
    const activity = await Boat.findOne({
      _id: id,
      ownerId: userId,
      recordType: RecordType.ACTIVITY,
      status: { $ne: BoatStatus.DELETED },
    });

    if (!activity) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ACTIVITY_NOT_FOUND"),
      });
    }

    // Process uploaded files
    const newFileIds: mongoose.Types.ObjectId[] = [];
    for (const file of files) {
      const relativePath = getRelativePath(file.path);
      const newFile = await File.create({
        name: file.filename,
        size: file.size,
        fileType: file.mimetype,
        ext: file.originalname.split(".").pop(),
        location: relativePath,
        type: FileType.IMAGE,
        ownerId: userId,
      });
      newFileIds.push(newFile._id as mongoose.Types.ObjectId);
    }

    // Add new images to activity
    activity.attachments.images.push(...newFileIds);
    await activity.save();

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("ACTIVITY_IMAGES_ADDED"),
      data: {
        addedImages: newFileIds.length,
        totalImages: activity.attachments.images.length,
        newImageIds: newFileIds,
      },
    });
  } catch (error: any) {
    logger.error(`Error adding activity images: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Add videos to an activity
 * @param req
 * @param res
 * @returns
 */
export const addActivityVideos = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = req.user._id;
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("NO_FILES_UPLOADED"),
      });
    }

    // Find the activity and verify ownership
    const activity = await Boat.findOne({
      _id: id,
      ownerId: userId,
      recordType: RecordType.ACTIVITY,
      status: { $ne: BoatStatus.DELETED },
    });

    if (!activity) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ACTIVITY_NOT_FOUND"),
      });
    }

    // Process uploaded files
    const newFileIds: mongoose.Types.ObjectId[] = [];
    for (const file of files) {
      const relativePath = getRelativePath(file.path);
      const newFile = await File.create({
        name: file.filename,
        size: file.size,
        fileType: file.mimetype,
        ext: file.originalname.split(".").pop(),
        location: relativePath,
        type: FileType.VIDEO,
        ownerId: userId,
      });
      newFileIds.push(newFile._id as mongoose.Types.ObjectId);
    }

    // Add new videos to activity
    activity.attachments.videos.push(...newFileIds);
    await activity.save();

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("ACTIVITY_VIDEOS_ADDED"),
      data: {
        addedVideos: newFileIds.length,
        totalVideos: activity.attachments.videos.length,
        newVideoIds: newFileIds,
      },
    });
  } catch (error: any) {
    logger.error(`Error adding activity videos: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Remove images from an activity
 * @param req
 * @param res
 * @returns
 */
export const removeActivityImages = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const { imageIds } = req.body;
    const userId = req.user._id;

    if (!imageIds || !Array.isArray(imageIds) || imageIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("IMAGE_IDS_REQUIRED"),
      });
    }

    // Find the activity and verify ownership
    const activity = await Boat.findOne({
      _id: id,
      ownerId: userId,
      recordType: RecordType.ACTIVITY,
      status: { $ne: BoatStatus.DELETED },
    });

    if (!activity) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ACTIVITY_NOT_FOUND"),
      });
    }

    // Validate that image IDs exist in activity's images
    const objectIdImageIds = imageIds.map((id: string) => new mongoose.Types.ObjectId(id));
    const validImageIds = objectIdImageIds.filter((removeId) =>
      activity.attachments.images.some((imageId) => removeId.equals(imageId))
    );

    if (validImageIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("INVALID_IMAGE_IDS"),
      });
    }

    // Remove images from activity
    activity.attachments.images = activity.attachments.images.filter(
      (imageId) => !validImageIds.some((removeId) => removeId.equals(imageId))
    );

    await activity.save();

    // Soft delete file records
    await File.updateMany(
      { _id: { $in: validImageIds } },
      { status: "deleted" }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("ACTIVITY_IMAGES_REMOVED"),
      data: {
        removedImages: validImageIds.length,
        totalImages: activity.attachments.images.length,
      },
    });
  } catch (error: any) {
    logger.error(`Error removing activity images: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

/**
 * Remove videos from an activity
 * @param req
 * @param res
 * @returns
 */
export const removeActivityVideos = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const { videoIds } = req.body;
    const userId = req.user._id;

    if (!videoIds || !Array.isArray(videoIds) || videoIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("VIDEO_IDS_REQUIRED"),
      });
    }

    // Find the activity and verify ownership
    const activity = await Boat.findOne({
      _id: id,
      ownerId: userId,
      recordType: RecordType.ACTIVITY,
      status: { $ne: BoatStatus.DELETED },
    });

    if (!activity) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ACTIVITY_NOT_FOUND"),
      });
    }

    // Validate that video IDs exist in activity's videos
    const objectIdVideoIds = videoIds.map((id: string) => new mongoose.Types.ObjectId(id));
    const validVideoIds = objectIdVideoIds.filter((removeId) =>
      activity.attachments.videos.some((videoId) => removeId.equals(videoId))
    );

    if (validVideoIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("INVALID_VIDEO_IDS"),
      });
    }

    // Remove videos from activity
    activity.attachments.videos = activity.attachments.videos.filter(
      (videoId) => !validVideoIds.some((removeId) => removeId.equals(videoId))
    );

    await activity.save();

    // Soft delete file records
    await File.updateMany(
      { _id: { $in: validVideoIds } },
      { status: "deleted" }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("ACTIVITY_VIDEOS_REMOVED"),
      data: {
        removedVideos: validVideoIds.length,
        totalVideos: activity.attachments.videos.length,
      },
    });
  } catch (error: any) {
    logger.error(`Error removing activity videos: ${error.message}`, {
      service: "boat-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};
