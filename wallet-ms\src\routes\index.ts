import express from "express";
import {
  getWalletBalance,
  getTransactionHistory,
  requestWithdrawal,
  getWithdrawalHistory,
} from "../controller/wallet.controller";
import { withdrawalValidator } from "../validator/wallet.validator";
import internalRoutes from "./internal.routes";

const router = express.Router();

// Public user routes
router.get("/balance", getWalletBalance);
router.get("/transactions", getTransactionHistory);
router.post("/withdraw", withdrawalValidator, requestWithdrawal);
router.get("/withdrawals", getWithdrawalHistory);

// Internal routes for service-to-service communication
router.use("/internal", internalRoutes);

export default router;
