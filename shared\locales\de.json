{"TOKEN_REQUIRED": "Authentifizierungstoken ist erforderlich.", "FAIL_TOKEN_EXPIRED": "Sitzung ist abgelaufen. bitte melden sie sich erneut an.", "USER_NOT_FOUND": "Benutzer nicht gefunden.", "ACCOUNT_INACTIVE": "Konto ist inaktiv oder gelöscht. bitte wenden sie sich an den support.", "SOMETHING_WENT_WRONG": "Etwas ist schief gelaufen. bitte versuchen sie es später noch einmal.", "ACCESS_DENIED": "Zugriff verweigert.", "INTERNAL_SERVER_ERROR": "Interner serverfehler aufgetreten.", "INVALID_ID": "Ungültige id angegeben.", "INVALID_STATUS": "Ungültiger status angegeben.", "INVALID_PAGINATION_PARAMS": "Ungültige paginierungsparameter.", "INVALID_FILTER_PARAMS": "Ungültige filterparameter.", "ACCOUNT_VERIFICATION_PENDING": "Konto ist ausstehend oder inaktiv. ein neuer otp wurde zur verifizierung gesendet.", "ACCOUNT_ALREADY_EXISTS": "Konto existiert bereits.", "ACCOUNT_VERIFICATION_SUCCESS": "Otp erfolgreich verifiziert.", "ACCOUNT_REGISTERED_SUCCESSFULLY": "Benutzer erfolgreich registriert.", "ACCOUNT_PENDING": "Kontoverifizierung steht aus.", "ACCOUNT_DELETED_SUCCESSFULLY": "Konto erfolgreich <PERSON>t.", "INVALID_CREDENTIALS": "Ungültige e-mail oder passwort.", "LOGIN_SUCCESS": "Anmeldung erfolgreich.", "LOGOUT_SUCCESS": "Abmeldung erfolgreich.", "INVALID_OTP": "Ungültiger otp. bitte versuchen sie es erneut.", "OTP_EXPIRED": "Otp ist abgelaufen. bitte fordern sie einen neuen otp an.", "OTP_SENT": "Otp erfolgreich gesendet. bitte überprüfen sie ihre e-mail.", "PASSWORD_RESET_SUCCESS": "Passwort erfolgreich zurückgesetzt.", "REFRESH_TOKEN_REQUIRED": "Aktualisierungstoken ist erforderlich.", "TOKEN_REFRESHED": "Token erfolgreich aktualisiert.", "REFRESH_TOKEN_EXPIRED": "Aktualisierungstoken ist abgelaufen.", "INVALID_REFRESH_TOKEN": "Ungültiger aktualisierungstoken.", "CURRENT_PASSWORD_INCORRECT": "Aktuelles passwort ist falsch.", "SUCCESS_CHANGE_PASSWORD": "Passwort erfolgreich geändert.", "PROFILE_UPDATED_SUCCESSFULLY": "Profil er<PERSON><PERSON>g<PERSON>ich aktualisiert.", "USER_ROLE_CHANGED_SUCCESSFULLY": "Benutzerrolle erfolgreich geändert.", "DEVICE_ID_UPDATED_SUCCESSFULLY": "Geräte-id erfolgreich aktualisiert.", "USER_PROFILE_FETCH_FAILED": "Benutzerprofilinformationen konnten nicht abgerufen werden.", "DOCUMENT_UPLOAD_REQUIRED": "Mindestens ein dokument muss hochgeladen werden.", "DOCUMENT_UPLOAD_SUCCESS": "Dokumente erfolgreich hochgeladen.", "DOCUMENT_NOT_FOUND": "Dokument nicht gefunden.", "DOCUMENT_DELETED_SUCCESS": "Dokument erfolgreich gelöscht.", "DOCUMENT_VERIFIED_SUCCESS": "Dokument erfolgreich verifiziert.", "INVALID_DOCUMENT_ID": "Ungültige dokument-id.", "DOCUMENT_USE_USER_API": "Bitte verwenden sie die benutzer-api für dokumentoperationen.", "NO_REWARDS_FOUND": "<PERSON>ine belohnungen für diesen benutzer gefunden.", "REWARDS_FETCH_SUCCESS": "Belohnungen erfolgreich abgerufen.", "BOAT_CREATED_SUCCESS": "Boot erfolgreich erstellt.", "BOAT_UPDATED_SUCCESS": "Boot erfolgreich aktualisiert.", "BOAT_DELETED_SUCCESS": "<PERSON>ot erfolg<PERSON>ich <PERSON>.", "BOAT_NOT_FOUND": "Boot nicht gefunden.", "BOAT_NOT_OWNER": "Sie sind nicht der besitzer dieses bootes.", "BOAT_INVALID_DOCUMENT_IDS": "Ungültige dokument-ids angegeben.", "BOAT_AVAILABILITY_SET": "Bootsverfügbarkeit erfolgreich festgelegt.", "BOAT_TYPES_FETCHED": "Bootstypen erfolgreich abgerufen.", "BOAT_LOCATIONS_FETCHED": "Bootsstandorte erfolgreich abgerufen.", "ACTIVITY_CREATED_SUCCESS": "Aktivität erfolgreich erstellt.", "ACTIVITY_UPDATED_SUCCESS": "Aktivität erfolgreich aktualisiert.", "ACTIVITY_DELETED_SUCCESS": "Aktivität erfolgreich gelöscht.", "ACTIVITY_NOT_FOUND": "Aktivität nicht gefunden.", "ACTIVITY_NOT_OWNER": "Sie sind nicht der besitzer dieser aktivität.", "ACTIVITY_INVALID_ID": "Ungültige aktivitäts-id.", "PLACE_CREATED_SUCCESS": "Ort erfolgreich erstellt.", "PLACE_UPDATED_SUCCESS": "Ort erfolgreich aktualisiert.", "PLACE_DELETED_SUCCESS": "Ort erfolgreich gelöscht.", "PLACE_NOT_FOUND": "Ort nicht gefunden.", "FACILITY_CREATED_SUCCESS": "Einrichtung erfolgreich erstellt.", "FACILITY_UPDATED_SUCCESS": "Einrichtung erfolgreich aktualisiert.", "FACILITY_DELETED_SUCCESS": "Einrichtung erfolgreich gelöscht.", "FACILITY_NOT_FOUND": "Einrichtung nicht gefunden.", "FACILITY_IMAGE_ADDED": "Einrichtungsbild erfolgreich hinzugefügt.", "WISHLIST_ADDED_SUCCESS": "Artikel erfolgreich zur wunschliste hinzugefügt.", "WISHLIST_REMOVED_SUCCESS": "Art<PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON><PERSON> von der wunschliste entfernt.", "WISHLIST_FETCH_SUCCESS": "Wunschliste erfolgreich abgerufen.", "WISHLIST_INVALID_BOAT_ID": "Ungültige artikel-id.", "WISHLIST_BOAT_ID_REQUIRED": "Artikel-id ist erforderlich.", "WISHLIST_ALREADY_IN_WISHLIST": "Dieser artikel ist bereits auf ihrer wunschliste.", "WISHLIST_NOT_IN_WISHLIST": "Dieser artikel ist nicht auf ihrer wunschliste.", "AFFILIATE_ALREADY_EXISTS": "Affiliate-antrag existiert bereits.", "AFFILIATE_CREATED_SUCCESS": "Affiliate-antrag erfolgreich erstellt.", "AFFILIATE_UPDATED_SUCCESS": "Affiliate erfolgreich aktualisiert.", "AFFILIATE_DELETED_SUCCESS": "Affiliate erfolg<PERSON>ich <PERSON>t.", "AFFILIATE_NOT_FOUND": "Affiliate nicht gefunden.", "AFFILIATE_FETCH_SUCCESS": "Affiliate-daten erfolgreich abgerufen.", "AFFILIATE_APPROVED_SUCCESS": "Affiliate erfolg<PERSON><PERSON> genehm<PERSON>t.", "AFFILIATE_REJECTED_SUCCESS": "Affiliate erfolg<PERSON>ich abgelehnt.", "AFFILIATE_ALREADY_APPROVED": "Affiliate ist bereits genehmigt.", "AFFILIATE_ALREADY_REJECTED": "Affiliate ist bereits abgelehnt.", "AFFILIATE_REJECTION_REASON_REQUIRED": "Ablehnungsgrund ist erforderlich.", "AFFILIATE_INVALID_STATUS": "Ungültiger affiliate-status.", "AFFILIATE_NOT_APPROVED": "Affiliate ist nicht genehmigt.", "AFFILIATE_FETCH_BOATS_SUCCESS": "Affiliate-boote erfolgreich abgerufen.", "AFFILIATE_NO_REJECTED_APPLICATION": "Kein abgelehnter affiliate-antrag gefunden.", "BOOKING_CREATED_SUCCESS": "Buchung erfolgreich erstellt.", "BOOKING_UPDATED_SUCCESS": "Buchung erfolgreich aktualisiert.", "BOOKING_CANCELLED_SUCCESS": "Buchung erfolgreich storniert.", "BOOKING_ACCEPTED_SUCCESS": "Buchung erfolgreich angenommen.", "BOOKING_REJECTED_SUCCESS": "Buchung erfolgreich abgelehnt.", "BOOKING_NOT_FOUND": "Buchung nicht gefunden.", "BOOKING_NOT_OWNER": "<PERSON>e sind nicht berechtigt, auf diese buchung zuzugreifen.", "BOOKING_FETCH_SUCCESS": "Buchungsdaten erfolgreich abgerufen.", "BOOKING_STATUS_UPDATED": "Buchungsstatus erfolgreich aktualisiert.", "BOOKING_NOT_READY_FOR_PAYMENT": "Diese buchung ist nicht bereit für die zahlungsabwicklung.", "UNAUTHORIZED_BOOKING_PAYMENT": "<PERSON>e sind nicht berechtigt, die zahlung für diese buchung zu tätigen.", "PAYMENT_SUCCESSFUL": "Zahlung erfolgreich verarbeitet.", "PAYMENT_FAILED": "Zahlungsverarbeitung fehlgeschlagen. bitte versuchen sie es erneut oder verwenden sie eine andere zahlungsmethode.", "PAYMENT_INVALID_METHOD": "Ungültige zahlungsmethode angegeben.", "PAYMENT_METHOD_NOT_SUPPORTED": "Die ausgewählte zahlungsmethode wird nicht unterstützt.", "PAYMENT_FETCH_SUCCESS": "Zahlungsdetails erfolgreich abgerufen.", "WALLET_NOT_FOUND": "Wallet für diesen benutzer nicht gefunden.", "WALLET_PAYMENT_FAILED_INSUFFICIENT_BALANCE": "Unzureichende mittel in der wallet, um diese zahlung abzuschließen.", "WALLET_CREATED_SUCCESS": "<PERSON>et erfolgreich erstellt.", "WALLET_UPDATED_SUCCESS": "Wallet erfolgreich aktualisiert.", "WALLET_FETCH_SUCCESS": "Wallet-daten erfolgreich abgerufen.", "CARD_NOT_FOUND": "Die ausgewählte zahlungskarte wurde nicht gefunden oder ist inaktiv.", "DEFAULT_CARD_NOT_FOUND": "Keine standard-zahlungskarte gefunden. bitte fügen sie eine karte hinzu oder wählen sie eine karte für die zahlung aus.", "CARD_PAYMENT_FAILED": "Kartenzahlung fehlgeschlagen. bitte überprüfen sie ihre kartendetails und versuchen sie es erneut.", "CARD_ADDED_SUCCESS": "<PERSON><PERSON> erfolgreich hinzugefügt.", "CARD_UPDATED_SUCCESS": "Karte erfolgreich aktualisiert.", "CARD_DELETED_SUCCESS": "Karte erfolgreich gelö<PERSON>t.", "CARD_FETCH_SUCCESS": "Kartendaten erfolgreich abgerufen.", "REVIEW_ADDED_SUCCESSFULLY": "Bewertung erfolgreich hinzugefügt.", "REVIEW_UPDATED_SUCCESS": "Bewertung erfolgreich aktualisiert.", "REVIEW_DELETED_SUCCESSFULLY": "Bewertung erfolgreich gelöscht.", "REVIEW_NOT_FOUND": "Bewertung nicht gefunden.", "REVIEW_ALREADY_EXISTS": "Sie haben diesen artikel bereits bewertet.", "REVIEW_FETCH_SUCCESS": "Bewertungen erfolgreich abgerufen.", "NOTIFICATION_LIST_SUCCESS": "Benachrichtigungen erfolgreich abgerufen.", "NOTIFICATION_NOT_FOUND": "Benachrichtigung nicht gefunden.", "NOTIFICATION_MARK_READ_SUCCESS": "Benachrichtigung als gelesen markiert.", "NOTIFICATION_MARK_ALL_READ_SUCCESS": "Alle benachrichtigungen als gelesen markiert.", "NOTIFICATION_ERROR_GET": "Fe<PERSON> beim abrufen von benachrichtigungen.", "NOTIFICATION_ERROR_MARK_READ": "Fehler beim markieren der benachrichtigung als gelesen.", "NOTIFICATION_ERROR_MARK_ALL_READ": "Fehler beim markieren aller benachrichtigungen als gelesen.", "FAQ_CREATED": "Faq erfolgreich erstellt.", "FAQ_UPDATED": "Faq erfolgreich aktualisiert.", "FAQ_DELETED": "Faq erfolgreich gelöscht.", "FAQ_NOT_FOUND": "Faq nicht gefunden.", "FAQ_FETCHED": "Faq erfolgreich abgerufen.", "FAQS_FETCHED": "Faqs erfolgreich abgerufen.", "NEWSLETTER_CREATED_SUCCESS": "Newsletter erfolgreich erstellt.", "NEWSLETTER_UPDATED_SUCCESS": "Newsletter erfolgreich aktualisiert.", "NEWSLETTER_DELETED_SUCCESS": "Newsletter erfolgreich gelöscht.", "NEWSLETTER_NOT_FOUND": "Newsletter nicht gefunden.", "NEWSLETTER_FETCH_SUCCESS": "Newsletter erfolgreich abgerufen.", "NEWSLETTER_FETCH_ONE_SUCCESS": "Newsletter erfolgreich abgerufen.", "NEWSLETTER_IMAGE_REQUIRED": "Newsletter-bild ist erforderlich.", "MAIL_FETCH_SUCCESS": "E-mail-daten erfolgreich abgerufen.", "MAIL_NOT_FOUND": "E-mail nicht gefunden.", "CHAT_HISTORY_FETCHED": "Chat-<PERSON><PERSON><PERSON><PERSON> erfolgreich abgerufen.", "CONVERSATIONS_FETCHED": "Unterhaltungen erfolgreich abgerufen.", "RECEIVER_ID_REQUIRED": "Empfänger-id ist erforderlich.", "CHANGELOG_FETCH_SUCCESS": "Änderungsprotokoll erfolgreich abgerufen.", "CHANGELOG_NOT_FOUND": "Änderungsprotokoll nicht gefunden.", "ABOUT_US_CREATED": "Über uns erfolgreich erstellt.", "ABOUT_US_UPDATED": "Über uns erfolgreich aktualisiert.", "ABOUT_US_DELETED": "Über uns erfolgreich gelöscht.", "ABOUT_US_NOT_FOUND": "Über uns nicht gefunden.", "ABOUT_US_VERSION_EXISTS": "Über uns version existiert bereits.", "CANNOT_DELETE_ACTIVE_ABOUT_US": "Aktives über uns kann nicht gelöscht werden.", "SOCIAL_MEDIA_CREATED": "Social media erfolgreich erstellt.", "SOCIAL_MEDIA_UPDATED": "Social media erfolgreich aktualisiert.", "SOCIAL_MEDIA_DELETED": "Social media erfolgreich gelöscht.", "SOCIAL_MEDIA_NOT_FOUND": "Social media nicht gefunden.", "SOCIAL_MEDIA_VERSION_EXISTS": "Social media version existiert bereits.", "CANNOT_DELETE_ACTIVE_SOCIAL_MEDIA": "Aktive social media kann nicht gelöscht werden.", "INVALID_CURRENCY": "Ungültige währung angegeben. bitte wählen sie eine gültige währung.", "BOAT_OWNER_NOT_FOUND": "Das konto des bootsbesitzers konnte nicht gefunden werden.", "AFFILIATE_CODE_NOT_FOUND": "Affiliate-code existiert nicht. bitte überprüfen sie den code und versuchen sie es erneut.", "AFFILIATE_CODE_NOT_ACTIVE": "Affiliate-code ist nicht aktiv. nur genehmigte affiliate-codes können verwendet werden.", "AFFILIATE_CODE_EXPIRED": "Affiliate-code ist abgelaufen und kann nicht mehr verwendet werden.", "AFFILIATE_CODE_VALID": "Affiliate-code ist gültig und aktiv.", "AFFILIATE_CODE_VALIDATION_ERROR": "Fehler bei der validierung des affiliate-codes. bitte versuchen sie es erneut.", "AFFILIATE_CODE_INVALID_FORMAT": "Affiliate-code-Format ist ungültig. code muss 6-20 alphanumerische zeichen haben.", "REFERRAL_NAME_NONE": "No referral name bereitgestellt.", "REFERRAL_NAME_NOT_FOUND": "Referral name nicht gefunden or not aktiv. bitte check the name and versuchen sie es erneut.", "REFERRAL_NAME_VALID": "Referral name is valid and aktiv.", "REFERRAL_NAME_VALIDATION_ERROR": "<PERSON><PERSON> aufgetreten while validating referral name. bitte versuchen sie es erneut."}