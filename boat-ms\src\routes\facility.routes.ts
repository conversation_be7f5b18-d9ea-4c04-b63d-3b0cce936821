import { Router } from "express";
import {
  createFacility,
  getFacility,
  getAllFacilities,
  updateFacility,
  deleteFacility,
  uploadImage,
  removeFacilityImages,
} from "../controller/facility.controller";
import {
  idParamValidator,
  uploadImageValidator,
} from "../validator/facility.validator";
import { uploadFiles } from "../../../shared/middleware/fileUpload.middleware";

const router = Router();

router.post("/", uploadFiles("facilities", "images"), createFacility);
router.get("/", getAllFacilities);
router.get("/:id", idParamValidator, getFacility);
router.put(
  "/:id",
  idParamValidator,
  uploadFiles("facilities", "images"),
  updateFacility
);
router.delete("/:id", idParamValidator, deleteFacility);
router.post(
  "/:id/images",
  idParamValidator,
  uploadImageValidator,
  uploadFiles("facilities", "images"),
  uploadImage
);
router.delete("/:id/images", idParamValidator, removeFacilityImages);

export default router;
