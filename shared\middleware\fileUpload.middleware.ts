import { Response, NextFunction } from "express";
import { upload<PERSON><PERSON> } from "../services/upload.service";
import path from "path";

/**
 * Helper function to extract path after uploads folder
 * @param filePath - The full file path
 * @returns Path after uploads/ folder
 */
export const getRelativePath = (filePath: string): string => {
  const parts = filePath.split(path.sep);
  const uploadsIndex = parts.findIndex((part) => part === "uploads");
  if (uploadsIndex !== -1 && uploadsIndex < parts.length - 1) {
    return parts.slice(uploadsIndex + 1).join("/");
  }
  return filePath; // Return original if we can't find uploads
};

/**
 * Middleware for handling file uploads to different folders based on entity type
 * @param entity - The entity type (e.g., 'boats', 'facilities', 'users', etc.)
 * @param fieldName - The field name in the form
 * @returns multer middleware - no limit on number of files
 */
export const uploadFiles = (entity: string, fieldName: string) => {
  return uploadMulter(entity).array(fieldName);
};

export const uploadSingeFiles = (entity: string, fieldName: string) => {
  console.log(entity , fieldName);

  return uploadMulter(entity).single(fieldName);
};

/**
 * Middleware for handling multiple file fields
 * @param entity - The entity type (e.g., 'boats', 'facilities', 'users', etc.)
 * @param fields - Array of field configurations { name }
 * @returns multer middleware
 */
export const uploadMultipleFields = (
  entity: string,
  fields: { name: string }[]
) => {
  return uploadMulter(entity).fields(fields);
};

/**
 * Middleware to validate file uploads exist
 */
export const validateFileUploads = (
  req: any,
  res: Response,
  next: NextFunction
) => {
  if (
    !req.file &&
    (!req.files ||
      (Array.isArray(req.files)
        ? req.files.length === 0
        : Object.keys(req.files).length === 0))
  ) {
    return res
      .status(400)
      .json({ status: false, message: "No files uploaded" });
  }
  next();
};
