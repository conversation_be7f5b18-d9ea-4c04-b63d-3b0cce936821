import { ChatEvents, MessageStatus } from "../constants/socket.constants";
import logger from "../../../shared/services/logger.service";
import {
  saveMessage,
  markMessageAsRead,
  markMessageAsDeleted,
  getPopulatedMessage,
  getSingleConversation,
} from "../services/chat.service";
import { messageValidator } from "../validator/chat.validator";
import mongoose from "mongoose";
import { NotificationService } from "../../../notification-ms/src/services/notification.service";
import { NOTIFICATION_TYPES } from "../../../notification-ms/src/constant/notificationConstant";

interface MessageData {
  receiverId: mongoose.Types.ObjectId;
  content?: string;
  messageType: string;
  mediaUrl?: string;
  userId: mongoose.Types.ObjectId;
}

interface MessageIdData {
  messageId: string;
  userId: mongoose.Types.ObjectId;
}

interface TypingData {
  receiverId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  isTyping: boolean
}

interface ConversationData {
  receiverId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
}

interface ChatRoomData {
  userId: mongoose.Types.ObjectId;
  otherUserId: mongoose.Types.ObjectId;
}

const connectedUsers: Record<string, string> = {};
const userChatRooms: Record<string, string> = {}; // Track which chat room each user is currently in

export const socketHandler = (io: any, socket: any) => {
  // Join a personal room for direct messages
  socket.on(ChatEvents.JOIN, (data: { userId: string }) => {
    socket.join(`user:${data.userId}`);
    console.log(`User connected: ${data.userId}`);
    logger.info(`User connected: ${data.userId}`, {
      service: "chat-ms",
      userId: data.userId,
    });
    connectedUsers[socket.id] = data.userId;
  });

  // Handle new private message
  socket.on(ChatEvents.SEND_MESSAGE, async (data: MessageData) => {
    const { userId, receiverId, content, messageType, mediaUrl } = data;
    try {
      // Validate incoming message data
      const { error } = messageValidator.validate(data);
      if (error) {
        return socket.emit(ChatEvents.ERROR, { message: error.message });
      }

      // Save message to database
      const message = await saveMessage({
        senderId: userId,
        receiverId,
        content,
        messageType: messageType as any,
        mediaUrl,
      });

      const isReceiverOnline = Object.values(connectedUsers).includes(receiverId.toString());
      const isReceiverInThisChat = userChatRooms[receiverId.toString()] === userId.toString();

      if (isReceiverOnline) {
        message.status = MessageStatus.DELIVERED
        await message.save()

        // Get populated message data for proper response
        const populatedMessage = await getPopulatedMessage((message._id as mongoose.Types.ObjectId).toString());

        // Always send the socket message if receiver is online
        io.to(`user:${receiverId}`).emit(ChatEvents.RECEIVE_MESSAGE, populatedMessage);

        // Send push notification only if receiver is NOT in this specific chat
        if (!isReceiverInThisChat) {
          await NotificationService.createAndSendNotification(
            receiverId.toString(),
            userId.toString(),
            NOTIFICATION_TYPES.NEW_MESSAGE,
            {
              message: message.content,
              messageId: message._id,
            }
          );
        }
      } else {
        // User is completely offline - send push notification
        await NotificationService.createAndSendNotification(
          receiverId.toString(),
          userId.toString(),
          NOTIFICATION_TYPES.NEW_MESSAGE,
          {
            message: message.content,
            messageId: message._id,
          }
        );
      }

      // Emit back to sender for confirmation
      socket.emit(ChatEvents.MESSAGE_SENT, {
        messageId: message._id,
        status: "sent",
      });
    } catch (error: any) {
      logger.error(`Error sending message: ${error.message}`, {
        service: "chat-ms",
        error,
        userId,
      });
      socket.emit(ChatEvents.ERROR, { message: "Failed to send message" });
    }
  });

  // Handle read receipts
  socket.on(ChatEvents.READ_MESSAGE, async (data: MessageIdData) => {
    const { messageId, userId } = data;
    try {
      const updatedMessage = await markMessageAsRead(messageId);

      if (updatedMessage) {
        io.to(`user:${updatedMessage.senderId}`).emit(ChatEvents.MESSAGE_READ, {
          messageId: updatedMessage._id,
        });
      }
    } catch (error: any) {
      logger.error(`Error marking message as read: ${error.message}`, {
        service: "chat-ms",
        error,
        userId,
      });
    }
  });

  // Handle message deletion
  socket.on(ChatEvents.DELETE_MESSAGE, async (data: MessageIdData) => {
    const { messageId, userId } = data;
    try {
      const deletedMessage = await markMessageAsDeleted(messageId, userId);

      if (deletedMessage) {
        // Notify the other user about message deletion
        io.to(`user:${deletedMessage.receiverId}`).emit(
          ChatEvents.MESSAGE_DELETED,
          {
            messageId: deletedMessage._id,
          }
        );

        // Confirm deletion to sender
        socket.emit(ChatEvents.MESSAGE_DELETED, {
          messageId: deletedMessage._id,
        });
      }
    } catch (error: any) {
      logger.error(`Error deleting message: ${error.message}`, {
        service: "chat-ms",
        error,
        userId,
      });
      socket.emit(ChatEvents.ERROR, { message: "Failed to delete message" });
    }
  });

  // Handle typing indicators
  socket.on(ChatEvents.TYPING, (data: TypingData) => {
    const { receiverId, userId, isTyping } = data;
    io.to(`user:${receiverId}`).emit(ChatEvents.USER_TYPING, { userId, isTyping });
  });

  // Handle list conversation request - returns single conversation object
  socket.on(ChatEvents.LIST_CONVERSATION, async (data: ConversationData) => {
    const { userId, receiverId } = data;
    try {
      // Get conversation between two specific users with same structure as getRecentChats
      const conversation = await getSingleConversation(userId.toString(), receiverId.toString());

      socket.emit(ChatEvents.CONVERSATION_CREATED, {
        success: true,
        data: conversation
      });
    } catch (error: any) {
      logger.error(`Error getting conversation: ${error.message}`, {
        service: "chat-ms",
        error,
        userId,
      });
      socket.emit(ChatEvents.ERROR, { message: "Failed to get conversation" });
    }
  });

  // Handle user entering a specific chat room
  socket.on(ChatEvents.ENTER_CHAT, (data: ChatRoomData) => {
    const { userId, otherUserId } = data;
    userChatRooms[userId.toString()] = otherUserId.toString();
    console.log(`User ${userId} entered chat with ${otherUserId}`);
    logger.info(`User ${userId} entered chat with ${otherUserId}`, {
      service: "chat-ms",
      userId: userId.toString(),
      otherUserId: otherUserId.toString(),
    });
  });

  // Handle user leaving a specific chat room
  socket.on(ChatEvents.LEAVE_CHAT, (data: { userId: mongoose.Types.ObjectId }) => {
    const { userId } = data;
    delete userChatRooms[userId.toString()];
    console.log(`User ${userId} left chat`);
    logger.info(`User ${userId} left chat`, {
      service: "chat-ms",
      userId: userId.toString(),
    });
  });

  // Handle user going offline
  socket.on("disconnect", () => {
    const userId = connectedUsers[socket.id];
    if (userId) {
      delete connectedUsers[socket.id];
      delete userChatRooms[userId]; // Clean up chat room tracking
      console.log(`User ${userId} disconnected`);
      logger.info(`User ${userId} disconnected`, {
        service: "chat-ms",
        userId,
      });
    }
    console.log("User disconnected");
  });
};
