{
	"info": {
		"_postman_id": "e18ef09c-ceea-4994-abbf-9878a519a930",
		"name": "Sea Escape API Copy",
		"description": "API collection for Sea Escape Backend Microservices",
		"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
		"_exporter_id": "40766612"
	},
	"item": [
		{
			"name": "Auth Service",
			"item": [
				{
					"name": "Register",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"12345678\",\n  \"currency\": \"USD\",\n  \"language\": \"en\",\n  \"interestedIn\": [\"Boat\", \"<PERSON><PERSON>kis\", \"Activity\"]\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/auth/register",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"auth",
								"register"
							]
						},
						"description": "Register a new user"
					},
					"response": []
				},
				{
					"name": "Login",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"var jsonData = JSON.parse(responseBody);",
									"if (jsonData.token) {",
									"    pm.environment.set(\"token\", jsonData.token);",
									"    console.log(\"Token saved to environment\");",
									"}"
								],
								"type": "text/javascript",
								"packages": {}
							}
						},
						{
							"listen": "prerequest",
							"script": {
								"exec": [
									""
								],
								"type": "text/javascript",
								"packages": {}
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"12345678\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/auth/login",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"auth",
								"login"
							]
						},
						"description": "Login user and get authentication token"
					},
					"response": []
				},
				{
					"name": "Forgot Password",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/auth/forgot-password",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"auth",
								"forgot-password"
							]
						},
						"description": "Send OTP for password reset"
					},
					"response": []
				},
				{
					"name": "Reset Password",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"newpassword123\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/auth/reset-password",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"auth",
								"reset-password"
							]
						},
						"description": "Reset password using OTP"
					},
					"response": []
				},
				{
					"name": "Verify OTP",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": 1234\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/auth/verify-otp",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"auth",
								"verify-otp"
							]
						},
						"description": "Verify OTP after registration"
					},
					"response": []
				},
				{
					"name": "Refresh Token",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"userId\": \"login user id\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/auth/refresh-token",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"auth",
								"refresh-token"
							]
						},
						"description": "Refresh access token using refresh token"
					},
					"response": []
				}
			]
		},
		{
			"name": "User Service",
			"item": [
				{
					"name": "Get Profile",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/user/profile",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"profile"
							]
						},
						"description": "Get current user profile"
					},
					"response": []
				},
				{
					"name": "Update Profile",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"body": {
							"mode": "formdata",
							"formdata": [
								{
									"key": "username",
									"value": "updated_username",
									"type": "text"
								},
								{
									"key": "avatar",
									"type": "file",
									"src": []
								},
								{
									"key": "currency",
									"value": "USD",
									"type": "text"
								},
								{
									"key": "language",
									"value": "en",
									"type": "text"
								}
							]
						},
						"url": {
							"raw": "{{baseUrl}}/v1/user/profile",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"profile"
							]
						},
						"description": "Update user profile with avatar"
					},
					"response": []
				},
				{
					"name": "Change Password",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"currentPassword\": \"password123\",\n  \"newPassword\": \"newpassword123\",\n  \"confirmPassword\": \"newpassword123\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/user/change-password",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"change-password"
							]
						},
						"description": "Change user password"
					},
					"response": []
				},
				{
					"name": "Switch User Role",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"role\": \"owner\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/user/switch-role",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"switch-role"
							]
						},
						"description": "Switch between user roles"
					},
					"response": []
				},
				{
					"name": "Get All Users",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/user/list",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"list"
							]
						},
						"description": "Get all users (admin only)"
					},
					"response": []
				},
				{
					"name": "Delete Account",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"deleteReason\": \"No longer needed\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/user/delete",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"delete"
							]
						},
						"description": "Delete current user account"
					},
					"response": []
				},
				{
					"name": "Upload Documents",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"body": {
							"mode": "formdata",
							"formdata": [
								{
									"key": "documents",
									"type": "file",
									"src": "postman-cloud:///1efd49fa-4d38-4970-a2f5-ac3589ca216c"
								}
							]
						},
						"url": {
							"raw": "{{baseUrl}}/v1/user/documents/upload",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"documents",
								"upload"
							]
						},
						"description": "Upload user documents"
					},
					"response": []
				},
				{
					"name": "verify document",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\r\n    \"status\":\"active\"\r\n}",
							"options": {
								"raw": {
									"language": "json"
								}
							}
						},
						"url": {
							"raw": "{{baseUrl}}/v1/user/documents/verify/6806187a5824163a2f98f80d",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"documents",
								"verify",
								"6806187a5824163a2f98f80d"
							]
						},
						"description": "Upload user documents"
					},
					"response": []
				},
				{
					"name": "Upload Chat Document",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"body": {
							"mode": "formdata",
							"formdata": [
								{
									"key": "media",
									"type": "file",
									"src": []
								}
							]
						},
						"url": {
							"raw": "{{baseUrl}}/v1/user/documents/chat-upload",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"documents",
								"chat-upload"
							]
						},
						"description": "Upload documents for chat"
					},
					"response": []
				},
				{
					"name": "Get User Documents",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/user/documents?page=1&limit=10",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"documents"
							],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								}
							]
						},
						"description": "Get current user's documents"
					},
					"response": []
				},
				{
					"name": "Get All Users Document By Admin",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/user/documents/all",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"documents",
								"all"
							]
						},
						"description": "Get current user's documents"
					},
					"response": []
				},
				{
					"name": "Delete Document",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/user/documents/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"documents",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Delete a user document"
					},
					"response": []
				},
				{
					"name": "Get User Rewards",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/user/rewards",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"rewards"
							]
						},
						"description": "Get the current user's reward details including points and welcome discounts"
					},
					"response": []
				},
				{
					"name": "Update Device ID",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"deviceToken\": \"fcm-device-token-xyz123\",\n  \"deviceType\": \"android\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/user/update-device-id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"update-device-id"
							]
						},
						"description": "Update device ID for push notifications"
					},
					"response": []
				},
				{
					"name": "Logout",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/user/logout",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"logout"
							]
						},
						"description": "Logout user and clear device token"
					},
					"response": []
				},
				{
					"name": "Get Rewards",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/user/rewards",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"user",
								"rewards"
							]
						},
						"description": "Get the current user's reward details including points and welcome discounts"
					},
					"response": []
				}
			]
		},
		{
			"name": "Boat Service",
			"item": [
				{
					"name": "Boat Facilities",
					"item": [
						{
							"name": "Add Activity",
							"request": {
								"method": "POST",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"body": {
									"mode": "formdata",
									"formdata": [
										{
											"key": "name",
											"value": "Scuba Diving",
											"type": "text"
										},
										{
											"key": "description",
											"value": "Exciting scuba diving activity",
											"type": "text"
										},
										{
											"key": "duration",
											"value": "2",
											"type": "text"
										},
										{
											"key": "location",
											"value": "Miami Beach",
											"type": "text"
										},
										{
											"key": "price",
											"value": "50",
											"type": "text"
										},
										{
											"key": "safetyInstructions",
											"value": "Always wear a life jacket",
											"type": "text"
										},
										{
											"key": "type",
											"value": "Water",
											"type": "text"
										},
										{
											"key": "availability",
											"value": "{\"start\": \"2023-08-01\", \"end\": \"2023-12-31\"}",
											"type": "text"
										},
										{
											"key": "status",
											"value": "published",
											"type": "text",
											"disabled": true
										},
										{
											"key": "images",
											"type": "file",
											"src": []
										}
									]
								},
								"url": {
									"raw": "{{baseUrl}}/v1/boat/activities",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"activities"
									]
								},
								"description": "Add a new boat activity"
							},
							"response": []
						},
						{
							"name": "Get All Activities",
							"request": {
								"method": "GET",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/boat/activities?page=1&limit=10",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"activities"
									],
									"query": [
										{
											"key": "page",
											"value": "1"
										},
										{
											"key": "limit",
											"value": "10"
										},
										{
											"key": "status",
											"value": "active",
											"disabled": true
										},
										{
											"key": "search",
											"value": "",
											"disabled": true
										}
									]
								},
								"description": "Get all boat activities with pagination"
							},
							"response": []
						},
						{
							"name": "Get Activity by ID",
							"request": {
								"method": "GET",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/boat/activities/:id",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"activities",
										":id"
									],
									"variable": [
										{
											"key": "id",
											"value": "60d21b4667d0d8992e610c85",
											"description": "Activity ID"
										}
									]
								},
								"description": "Get a specific activity by ID"
							},
							"response": []
						},
						{
							"name": "Update Activity",
							"request": {
								"method": "PUT",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"body": {
									"mode": "formdata",
									"formdata": [
										{
											"key": "name",
											"value": "Updated Activity Name",
											"type": "text"
										},
										{
											"key": "description",
											"value": "Updated activity description",
											"type": "text"
										},
										{
											"key": "duration",
											"value": "3",
											"type": "text"
										},
										{
											"key": "location",
											"value": "Updated Beach Location",
											"type": "text"
										},
										{
											"key": "price",
											"value": "75",
											"type": "text"
										},
										{
											"key": "safetyInstructions",
											"value": "Updated safety instructions",
											"type": "text"
										},
										{
											"key": "type",
											"value": "Water",
											"type": "text"
										},
										{
											"key": "availability",
											"value": "{\"start\": \"2023-09-01\", \"end\": \"2024-01-31\"}",
											"type": "text"
										},
										{
											"key": "status",
											"value": "published",
											"type": "text",
											"disabled": true
										},
										{
											"key": "images",
											"type": "file",
											"src": []
										}
									]
								},
								"url": {
									"raw": "{{baseUrl}}/v1/boat/activities/:id",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"activities",
										":id"
									],
									"variable": [
										{
											"key": "id",
											"value": "60d21b4667d0d8992e610c85",
											"description": "Activity ID"
										}
									]
								},
								"description": "Update an existing activity"
							},
							"response": []
						},
						{
							"name": "Delete Activity",
							"request": {
								"method": "DELETE",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/boat/activities/:id",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"activities",
										":id"
									],
									"variable": [
										{
											"key": "id",
											"value": "60d21b4667d0d8992e610c85",
											"description": "Activity ID"
										}
									]
								},
								"description": "Delete an existing activity"
							},
							"response": []
						},
						{
							"name": "Check Wishlist Status",
							"request": {
								"method": "GET",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/boat/activities/wishlist-status/:id",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"activities",
										"wishlist-status",
										":id"
									],
									"variable": [
										{
											"key": "id",
											"value": "60d21b4667d0d8992e610c85",
											"description": "Activity ID"
										}
									]
								},
								"description": "Check if an activity is in user's wishlist"
							},
							"response": []
						},
						{
							"name": "Create Facility",
							"request": {
								"method": "POST",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"body": {
									"mode": "formdata",
									"formdata": [
										{
											"key": "name",
											"value": "WiFi",
											"type": "text"
										},
										{
											"key": "price",
											"value": "25",
											"type": "text"
										},
										{
											"key": "status",
											"value": "active",
											"type": "text",
											"disabled": true
										},
										{
											"key": "images",
											"type": "file",
											"src": []
										}
									]
								},
								"url": {
									"raw": "{{baseUrl}}/v1/boat/facilities",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"facilities"
									]
								},
								"description": "Create a new boat facility"
							},
							"response": []
						},
						{
							"name": "Get All Facilities",
							"request": {
								"method": "GET",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/boat/facilities?page=1&limit=10",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"facilities"
									],
									"query": [
										{
											"key": "page",
											"value": "1"
										},
										{
											"key": "limit",
											"value": "10"
										},
										{
											"key": "status",
											"value": "active",
											"disabled": true
										},
										{
											"key": "name",
											"value": "",
											"disabled": true
										}
									]
								},
								"description": "Get all boat facilities with pagination"
							},
							"response": []
						},
						{
							"name": "Get Facility by ID",
							"request": {
								"method": "GET",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/boat/facilities/:id",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"facilities",
										":id"
									],
									"variable": [
										{
											"key": "id",
											"value": "60d21b4667d0d8992e610c85",
											"description": "Facility ID"
										}
									]
								},
								"description": "Get a specific facility by ID"
							},
							"response": []
						},
						{
							"name": "Update Facility",
							"request": {
								"method": "PUT",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"body": {
									"mode": "formdata",
									"formdata": [
										{
											"key": "name",
											"value": "Updated Facility Name",
											"type": "text"
										},
										{
											"key": "price",
											"value": "35",
											"type": "text"
										},
										{
											"key": "status",
											"value": "active",
											"type": "text",
											"disabled": true
										},
										{
											"key": "images",
											"type": "file",
											"src": []
										}
									]
								},
								"url": {
									"raw": "{{baseUrl}}/v1/boat/facilities/:id",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"facilities",
										":id"
									],
									"variable": [
										{
											"key": "id",
											"value": "60d21b4667d0d8992e610c85"
										}
									]
								},
								"description": "Update an existing facility"
							},
							"response": []
						},
						{
							"name": "Delete Facility",
							"request": {
								"method": "DELETE",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/boat/facilities/:id",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"facilities",
										":id"
									],
									"variable": [
										{
											"key": "id",
											"value": "60d21b4667d0d8992e610c85"
										}
									]
								},
								"description": "Delete a facility (soft delete)"
							},
							"response": []
						},
						{
							"name": "Upload Facility Image",
							"request": {
								"method": "POST",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}"
									}
								],
								"body": {
									"mode": "formdata",
									"formdata": [
										{
											"key": "images",
											"type": "file",
											"src": []
										}
									]
								},
								"url": {
									"raw": "{{baseUrl}}/v1/boat/facilities/:id/images",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"boat",
										"facilities",
										":id",
										"images"
									],
									"variable": [
										{
											"key": "id",
											"value": "60d21b4667d0d8992e610c85"
										}
									]
								},
								"description": "Upload an image to an existing facility"
							},
							"response": []
						}
					]
				},
				{
					"name": "Create Boat",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"body": {
							"mode": "formdata",
							"formdata": [
								{
									"key": "name",
									"value": "Sample Boat",
									"type": "text"
								},
								{
									"key": "description",
									"value": "This is a sample boat description",
									"type": "text"
								},
								{
									"key": "type",
									"value": "Yacht",
									"type": "text"
								},
								{
									"key": "location",
									"value": "Sample Location",
									"type": "text"
								},
								{
									"key": "guestsCapacity",
									"value": "10",
									"type": "text"
								},
								{
									"key": "cabins",
									"value": "2",
									"type": "text"
								},
								{
									"key": "baths",
									"value": "1",
									"type": "text"
								},
								{
									"key": "pricePerDay",
									"value": "500",
									"type": "text"
								},
								{
									"key": "fullDayWithPatron",
									"value": "600",
									"type": "text"
								},
								{
									"key": "fullDayWithoutPatron",
									"value": "500",
									"type": "text"
								},
								{
									"key": "halfDayWithPatron",
									"value": "350",
									"type": "text"
								},
								{
									"key": "affiliateCode",
									"value": "AFFILIATE123",
									"type": "text"
								},
								{
									"key": "halfDayWithoutPatron",
									"value": "250",
									"type": "text"
								},
								{
									"key": "language",
									"value": "[\"English\", \"Spanish\"]",
									"type": "text"
								},
								{
									"key": "fuel",
									"value": "true",
									"type": "text"
								},
								{
									"key": "patron",
									"value": "true",
									"type": "text"
								},
								{
									"key": "license",
									"value": "true",
									"type": "text"
								},
								{
									"key": "schedule",
									"value": "[{\"day\": \"Monday\", \"duration\": \"Full Day\", \"startTime\": \"09:00 AM\", \"endTime\": \"05:00 PM\"}]",
									"type": "text"
								},
								{
									"key": "checkInNotes",
									"value": "Please arrive 15 minutes early",
									"type": "text"
								},
								{
									"key": "checkOutNotes",
									"value": "Please ensure all personal items are removed",
									"type": "text"
								},
								{
									"key": "availability",
									"value": "{\"start\": \"2023-08-01\", \"end\": \"2023-12-31\"}",
									"type": "text"
								},
								{
									"key": "status",
									"value": "published",
									"type": "text"
								},
								{
									"key": "images",
									"type": "file",
									"src": [
										"postman-cloud:///1effd757-8e06-4eb0-bca2-94edb93f649c",
										"postman-cloud:///1effd757-19be-4da0-9ddb-1e06d99e833e"
									]
								},
								{
									"key": "videos",
									"type": "file",
									"src": "postman-cloud:///1eff5978-ca36-4500-b730-e52f7e86e5fa"
								},
								{
									"key": "facilities",
									"value": '[{"name": "WiFi", "price": 25, "images": ["facility1.jpg", "facility2.jpg"]}, {"name": "Catering", "price": 120, "images": ["catering1.jpg"]}]',
									"type": "text"
								},
								{
									"key": "recommendedPlaces",
									"value": '[{"name": "Beach", "description": "Nice beach", "images": ["beach1.jpg"]}, {"name": "Island", "description": "Beautiful island", "images": ["island1.jpg", "island2.jpg"]}]',
									"type": "text"
								},
								{
									"key": "documents",
									"value": "[]",
									"type": "text"
								},
								{
									"key": "lat",
									"value": "123",
									"type": "text"
								},
								{
									"key": "lng",
									"value": "456",
									"type": "text"
								}
							]
						},
						"url": {
							"raw": "{{baseUrl}}/v1/boat",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"boat"
							]
						},
						"description": "Create a new boat"
					},
					"response": []
				},
				{
					"name": "Get All Boats",
					"request": {
						"auth": {
							"type": "bearer",
							"bearer": [
								{
									"key": "token",
									"value": "{{token}}",
									"type": "string"
								}
							]
						},
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/boat?page=1&limit=10",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"boat"
							],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								},
								{
									"key": "search",
									"value": "",
									"disabled": true
								},
								{
									"key": "type",
									"value": "",
									"disabled": true
								},
								{
									"key": "status",
									"value": "",
									"disabled": true
								}
							]
						},
						"description": "Get all boats with pagination"
					},
					"response": []
				},
				{
					"name": "Get Boat Types",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/boat/types",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"boat",
								"types"
							]
						},
						"description": "Get all available boat types"
					},
					"response": []
				},
				{
					"name": "Get Boat By ID",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/boat/68060bf2c11940d4a137e866",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"boat",
								"68060bf2c11940d4a137e866"
							]
						},
						"description": "Get boat details by ID"
					},
					"response": []
				},
				{
					"name": "Update Boat",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"body": {
							"mode": "formdata",
							"formdata": [
								{
									"key": "name",
									"value": "Updated Boat Name",
									"type": "text"
								},
								{
									"key": "description",
									"value": "Updated boat description",
									"type": "text"
								},
								{
									"key": "type",
									"value": "Yacht",
									"type": "text"
								},
								{
									"key": "location",
									"value": "Updated Location",
									"type": "text"
								},
								{
									"key": "guestsCapacity",
									"value": "12",
									"type": "text"
								},
								{
									"key": "pricePerDay",
									"value": "600",
									"type": "text"
								},
								{
									"key": "fullDayWithPatron",
									"value": "700",
									"type": "text"
								},
								{
									"key": "fullDayWithoutPatron",
									"value": "600",
									"type": "text"
								},
								{
									"key": "language",
									"value": "[\"English\", \"Spanish\"]",
									"type": "text"
								},
								{
									"key": "fuel",
									"value": "true",
									"type": "text"
								},
								{
									"key": "affiliateCode",
									"value": "UPDATED_AFFILIATE123",
									"type": "text"
								},
								{
									"key": "status",
									"value": "published",
									"type": "text"
								},
								{
									"key": "schedule",
									"value": "[{\"day\": \"Monday\", \"duration\": \"Full Day\", \"startTime\": \"09:00 AM\", \"endTime\": \"05:00 PM\"}]",
									"type": "text"
								},
								{
									"key": "availability",
									"value": "{\"start\": \"2023-08-01\", \"end\": \"2023-12-31\"}",
									"type": "text"
								},
								{
									"key": "images",
									"type": "file",
									"src": [],
									"disabled": true
								},
								{
									"key": "videos",
									"type": "file",
									"src": [],
									"disabled": true
								}
							]
						},
						"url": {
							"raw": "{{baseUrl}}/v1/boat/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"boat",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Update boat details"
					},
					"response": []
				},
				{
					"name": "Delete Boat",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/boat/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"boat",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Delete a boat (soft delete)"
					},
					"response": []
				},
				{
					"name": "Set Boat Availability",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"start\": \"2023-08-01T00:00:00Z\",\n  \"end\": \"2023-12-31T23:59:59Z\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/boat/:id/availability",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"boat",
								":id",
								"availability"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Set boat availability for specific dates"
					},
					"response": []
				},
				{
					"name": "Upload Boat Attachment",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"body": {
							"mode": "formdata",
							"formdata": [
								{
									"key": "file",
									"type": "file",
									"src": []
								}
							]
						},
						"url": {
							"raw": "{{baseUrl}}/v1/boat/:id/attachment/:type",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"boat",
								":id",
								"attachment",
								":type"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								},
								{
									"key": "type",
									"value": "image"
								}
							]
						},
						"description": "Upload image, video, or document attachment for a boat (type can be 'image', 'video')"
					},
					"response": []
				}
			]
		},
		{
			"name": "Booking Service",
			"item": [
				{
					"name": "Create Booking",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"boatId\": \"60d21b4667d0d8992e610c85\",\n  \"startDate\": \"2023-09-15T10:00:00Z\",\n  \"endDate\": \"2023-09-15T14:00:00Z\",\n  \"location\": \"Marina Bay\",\n  \"duration\": \"HALF_DAY\",\n  \"patronType\": \"FullDayWithPatron\",\n  \"extraFacilities\": [\n    {\n      \"facilityId\": \"60d21b4667d0d8992e610c87\",\n      \"quantity\": 2\n    }\n  ],\n  \"referralName\": null\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/booking",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"booking"
							]
						},
						"description": "Create a new booking"
					},
					"response": []
				},
				{
					"name": "Get My Bookings",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/booking/me?page=1&limit=10",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"booking",
								"me"
							],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								},
								{
									"key": "status",
									"value": "",
									"disabled": true
								}
							]
						},
						"description": "Get bookings for current user"
					},
					"response": []
				},
				{
					"name": "Get Booking By ID",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/booking/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"booking",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Get booking details by ID"
					},
					"response": []
				},
				{
					"name": "Modify Booking",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"startDate\": \"2023-09-16T11:00:00Z\",\n  \"endDate\": \"2023-09-16T15:00:00Z\",\n  \"location\": \"Updated Marina Bay\",\n  \"duration\": \"HALF_DAY\",\n  \"patronType\": \"FullDayWithPatron\",\n  \"extraFacilities\": [\n    {\n      \"facilityId\": \"60d21b4667d0d8992e610c87\",\n      \"quantity\": 3\n    }\n  ]\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/booking/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"booking",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Modify an existing booking"
					},
					"response": []
				},
				{
					"name": "Accept Booking",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/booking/:id/accept",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"booking",
								":id",
								"accept"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Accept a booking (boat owner only)"
					},
					"response": []
				},
				{
					"name": "Update Booking Status",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"status\": \"ReadyForPayment\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/booking/update-status/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"booking",
								"update-status",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "601a2fc95af25b0014e76fb3",
									"description": "Booking ID"
								}
							]
						},
						"description": "Update the status of a booking (for payment integration)"
					},
					"response": []
				},
				{
					"name": "Get Booking Status",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/booking/status/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"booking",
								"status",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "601a2fc95af25b0014e76fb3",
									"description": "Booking ID"
								}
							]
						},
						"description": "Get the current status of a booking"
					},
					"response": []
				},
				{
					"name": "Reject Booking",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"reason\": \"Boat is under maintenance\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/booking/:id/reject",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"booking",
								":id",
								"reject"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Reject a booking (boat owner only)"
					},
					"response": []
				}
			]
		},
		{
			"name": "Card Service",
			"item": [
				{
					"name": "Create Card",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"cardNumber\": \"****************\",\n  \"expiryMonth\": \"12\",\n  \"expiryYear\": \"2025\",\n  \"cvv\": \"123\",\n  \"cardholderName\": \"John Doe\",\n  \"isDefault\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/card",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"card"
							]
						},
						"description": "Add a new payment card"
					},
					"response": []
				},
				{
					"name": "Get My Cards",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/card?status=active",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"card"
							],
							"query": [
								{
									"key": "status",
									"value": "active"
								}
							]
						},
						"description": "Get all cards for current user"
					},
					"response": []
				},
				{
					"name": "Get Card By ID",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/card/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"card",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Get card details by ID"
					},
					"response": []
				},
				{
					"name": "Update Card",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"cardholderName\": \"Jane Doe\",\n  \"isDefault\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/card/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"card",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Update card details"
					},
					"response": []
				},
				{
					"name": "Delete Card",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/card/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"card",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Delete a card"
					},
					"response": []
				}
			]
		},
		{
			"name": "Payment Service",
			"item": [
				{
					"name": "Generate Client Token",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/payment/client-token",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"payment",
								"client-token"
							]
						},
						"description": "Generate client token for Braintree"
					},
					"response": []
				},
				{
					"name": "Process Payment",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"bookingId\": \"60d21b4667d0d8992e610c85\",\n  \"paymentMethod\": \"CARD\",\n  \"cardId\": \"60d21b4667d0d8992e610c85\",\n  \"paymentMethodNonce\": \"fake-valid-nonce\",\n  \"deviceData\": \"browser-data\",\n  \"usePoints\": 0,\n  \"useWelcomeDiscount\": false\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/payment",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"payment"
							]
						},
						"description": "Process payment for a booking"
					},
					"response": []
				},
				{
					"name": "Get Payment Details",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/payment/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"payment",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Get payment details by ID"
					},
					"response": []
				},
				{
					"name": "Get Payment History",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/payment?page=1&limit=10&type=all",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"payment"
							],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								},
								{
									"key": "type",
									"value": "all"
								}
							]
						},
						"description": "Get payment history (type can be 'all', 'sent', or 'received')"
					},
					"response": []
				}
			]
		},
		{
			"name": "Wallet Service",
			"item": [
				{
					"name": "Public APIs",
					"item": [
						{
							"name": "Get Wallet Balance",
							"request": {
								"method": "GET",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}",
										"type": "text"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/wallet/balance",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"wallet",
										"balance"
									]
								},
								"description": "Retrieves the current balance of the user's wallet"
							},
							"response": [
								{
									"name": "Success Response",
									"originalRequest": {
										"method": "GET",
										"header": [
											{
												"key": "Authorization",
												"value": "Bearer {{jwt_token}}",
												"type": "text"
											}
										],
										"url": {
											"raw": "{{base_url}}/balance",
											"host": [
												"{{base_url}}"
											],
											"path": [
												"balance"
											]
										}
									},
									"status": "OK",
									"code": 200,
									"_postman_previewlanguage": "json",
									"header": [
										{
											"key": "Content-Type",
											"value": "application/json"
										}
									],
									"cookie": [],
									"body": "{\n  \"success\": true,\n  \"message\": \"Wallet balance fetched successfully\",\n  \"data\": {\n    \"balance\": 1500.00\n  }\n}"
								}
							]
						},
						{
							"name": "Get Transaction History",
							"request": {
								"method": "GET",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}",
										"type": "text"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/wallet/transactions?page=1&limit=10&type=receive",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"wallet",
										"transactions"
									],
									"query": [
										{
											"key": "page",
											"value": "1"
										},
										{
											"key": "limit",
											"value": "10"
										},
										{
											"key": "type",
											"value": "receive"
										}
									]
								},
								"description": "Retrieves the transaction history for the authenticated user"
							},
							"response": [
								{
									"name": "Success Response",
									"originalRequest": {
										"method": "GET",
										"header": [
											{
												"key": "Authorization",
												"value": "Bearer {{jwt_token}}",
												"type": "text"
											}
										],
										"url": {
											"raw": "{{base_url}}/transactions?page=1&limit=10",
											"host": [
												"{{base_url}}"
											],
											"path": [
												"transactions"
											],
											"query": [
												{
													"key": "page",
													"value": "1"
												},
												{
													"key": "limit",
													"value": "10"
												}
											]
										}
									},
									"status": "OK",
									"code": 200,
									"_postman_previewlanguage": "json",
									"header": [
										{
											"key": "Content-Type",
											"value": "application/json"
										}
									],
									"cookie": [],
									"body": "{\n  \"success\": true,\n  \"message\": \"Transaction history fetched successfully\",\n  \"data\": {\n    \"transactions\": [\n      {\n        \"_id\": \"60d21b4667d0d8992e610c85\",\n        \"userId\": \"60d21b4667d0d8992e610c86\",\n        \"amount\": 500.00,\n        \"type\": \"credit\",\n        \"status\": \"completed\",\n        \"source\": \"booking\",\n        \"sourceId\": \"60d21b4667d0d8992e610c87\",\n        \"bookingId\": \"60d21b4667d0d8992e610c88\",\n        \"description\": \"Payment for booking #12345\",\n        \"paymentMethod\": \"credit_card\",\n        \"reference\": \"txn_12345\",\n        \"currency\": \"USD\",\n        \"createdAt\": \"2023-06-20T10:00:00Z\",\n        \"updatedAt\": \"2023-06-20T10:00:00Z\"\n      },\n      {\n        \"_id\": \"60d21b4667d0d8992e610c89\",\n        \"userId\": \"60d21b4667d0d8992e610c86\",\n        \"amount\": 200.00,\n        \"type\": \"debit\",\n        \"status\": \"completed\",\n        \"source\": \"withdrawal\",\n        \"sourceId\": \"60d21b4667d0d8992e610c90\",\n        \"description\": \"Withdrawal to Bank of America account ****1234\",\n        \"currency\": \"USD\",\n        \"createdAt\": \"2023-06-18T14:30:00Z\",\n        \"updatedAt\": \"2023-06-18T14:30:00Z\"\n      }\n    ],\n    \"pagination\": {\n      \"total\": 15,\n      \"page\": 1,\n      \"limit\": 10,\n      \"pages\": 2\n    }\n  }\n}"
								}
							]
						},
						{
							"name": "Request Withdrawal",
							"request": {
								"method": "POST",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}",
										"type": "text"
									},
									{
										"key": "Content-Type",
										"value": "application/json",
										"type": "text"
									}
								],
								"body": {
									"mode": "raw",
									"raw": "{\n  \"amount\": 100.00,\n  \"bankHolderName\": \"John Doe\",\n  \"bankName\": \"Bank of America\",\n  \"accountNumber\": \"*********0\"\n}"
								},
								"url": {
									"raw": "{{baseUrl}}/v1/wallet/withdraw",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"wallet",
										"withdraw"
									]
								},
								"description": "Requests a withdrawal from the user's wallet to their bank account"
							},
							"response": [
								{
									"name": "Success Response",
									"originalRequest": {
										"method": "POST",
										"header": [
											{
												"key": "Authorization",
												"value": "Bearer {{jwt_token}}",
												"type": "text"
											},
											{
												"key": "Content-Type",
												"value": "application/json",
												"type": "text"
											}
										],
										"body": {
											"mode": "raw",
											"raw": "{\n  \"amount\": 100.00,\n  \"bankHolderName\": \"John Doe\",\n  \"bankName\": \"Bank of America\",\n  \"accountNumber\": \"*********0\",\n  \"routingNumber\": \"*********\"\n}"
										},
										"url": {
											"raw": "{{base_url}}/withdraw",
											"host": [
												"{{base_url}}"
											],
											"path": [
												"withdraw"
											]
										}
									},
									"status": "Created",
									"code": 201,
									"_postman_previewlanguage": "json",
									"header": [
										{
											"key": "Content-Type",
											"value": "application/json"
										}
									],
									"cookie": [],
									"body": "{\n  \"success\": true,\n  \"message\": \"Withdrawal request submitted successfully\",\n  \"data\": {\n    \"_id\": \"60d21b4667d0d8992e610c91\",\n    \"userId\": \"60d21b4667d0d8992e610c86\",\n    \"amount\": 100.00,\n    \"bankHolderName\": \"John Doe\",\n    \"bankName\": \"Bank of America\",\n    \"accountNumber\": \"*********0\",\n    \"routingNumber\": \"*********\",\n    \"currency\": \"USD\",\n    \"status\": \"completed\",\n    \"transactionId\": \"60d21b4667d0d8992e610c92\",\n    \"externalReferenceId\": \"wth-sandbox-*************-123\",\n    \"createdAt\": \"2023-06-22T09:15:00Z\",\n    \"updatedAt\": \"2023-06-22T09:15:00Z\"\n  }\n}"
								},
								{
									"name": "Insufficient Balance Response",
									"originalRequest": {
										"method": "POST",
										"header": [
											{
												"key": "Authorization",
												"value": "Bearer {{jwt_token}}",
												"type": "text"
											},
											{
												"key": "Content-Type",
												"value": "application/json",
												"type": "text"
											}
										],
										"body": {
											"mode": "raw",
											"raw": "{\n  \"amount\": 5000.00,\n  \"bankHolderName\": \"John Doe\",\n  \"bankName\": \"Bank of America\",\n  \"accountNumber\": \"*********0\",\n  \"routingNumber\": \"*********\"\n}"
										},
										"url": {
											"raw": "{{base_url}}/withdraw",
											"host": [
												"{{base_url}}"
											],
											"path": [
												"withdraw"
											]
										}
									},
									"status": "Bad Request",
									"code": 400,
									"_postman_previewlanguage": "json",
									"header": [
										{
											"key": "Content-Type",
											"value": "application/json"
										}
									],
									"cookie": [],
									"body": "{\n  \"success\": false,\n  \"message\": \"Insufficient wallet balance\",\n  \"error\": \"Your current balance is not enough for this withdrawal\"\n}"
								}
							]
						},
						{
							"name": "Get Withdrawal History",
							"request": {
								"method": "GET",
								"header": [
									{
										"key": "Authorization",
										"value": "Bearer {{token}}",
										"type": "text"
									}
								],
								"url": {
									"raw": "{{baseUrl}}/v1/wallet/withdrawals?page=1&limit=10",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"wallet",
										"withdrawals"
									],
									"query": [
										{
											"key": "page",
											"value": "1"
										},
										{
											"key": "limit",
											"value": "10"
										}
									]
								},
								"description": "Retrieves the withdrawal history for the authenticated user"
							},
							"response": [
								{
									"name": "Success Response",
									"originalRequest": {
										"method": "GET",
										"header": [
											{
												"key": "Authorization",
												"value": "Bearer {{jwt_token}}",
												"type": "text"
											}
										],
										"url": {
											"raw": "{{base_url}}/withdrawals?page=1&limit=10",
											"host": [
												"{{base_url}}"
											],
											"path": [
												"withdrawals"
											],
											"query": [
												{
													"key": "page",
													"value": "1"
												},
												{
													"key": "limit",
													"value": "10"
												}
											]
										}
									},
									"status": "OK",
									"code": 200,
									"_postman_previewlanguage": "json",
									"header": [
										{
											"key": "Content-Type",
											"value": "application/json"
										}
									],
									"cookie": [],
									"body": "{\n  \"success\": true,\n  \"message\": \"Withdrawal history fetched successfully\",\n  \"data\": {\n    \"withdrawals\": [\n      {\n        \"_id\": \"60d21b4667d0d8992e610c91\",\n        \"userId\": \"60d21b4667d0d8992e610c86\",\n        \"amount\": 100.00,\n        \"bankHolderName\": \"John Doe\",\n        \"bankName\": \"Bank of America\",\n        \"accountNumber\": \"*********0\",\n        \"routingNumber\": \"*********\",\n        \"currency\": \"USD\",\n        \"status\": \"completed\",\n        \"transactionId\": \"60d21b4667d0d8992e610c92\",\n        \"externalReferenceId\": \"wth-sandbox-*************-123\",\n        \"createdAt\": \"2023-06-22T09:15:00Z\",\n        \"updatedAt\": \"2023-06-22T09:15:00Z\"\n      },\n      {\n        \"_id\": \"60d21b4667d0d8992e610c93\",\n        \"userId\": \"60d21b4667d0d8992e610c86\",\n        \"amount\": 200.00,\n        \"bankHolderName\": \"John Doe\",\n        \"bankName\": \"Chase Bank\",\n        \"accountNumber\": \"**********\",\n        \"routingNumber\": \"*********\",\n        \"currency\": \"USD\",\n        \"status\": \"processing\",\n        \"transactionId\": \"60d21b4667d0d8992e610c94\",\n        \"createdAt\": \"2023-06-20T14:30:00Z\",\n        \"updatedAt\": \"2023-06-20T14:30:00Z\"\n      }\n    ],\n    \"pagination\": {\n      \"total\": 5,\n      \"page\": 1,\n      \"limit\": 10,\n      \"pages\": 1\n    }\n  }\n}"
								}
							]
						}
					],
					"description": "APIs available to authenticated users"
				},
				{
					"name": "Internal APIs",
					"item": [
						{
							"name": "Credit Wallet",
							"request": {
								"auth": {
									"type": "bearer",
									"bearer": [
										{
											"key": "token",
											"value": "{{token}}",
											"type": "string"
										}
									]
								},
								"method": "POST",
								"header": [
									{
										"key": "Content-Type",
										"value": "application/json",
										"type": "text"
									}
								],
								"body": {
									"mode": "raw",
									"raw": "{\n  \"userId\": \"60d21b4667d0d8992e610c86\",\n  \"amount\": 500.00,\n  \"source\": \"booking\",\n  \"sourceId\": \"60d21b4667d0d8992e610c87\",\n  \"bookingId\": \"60d21b4667d0d8992e610c88\",\n  \"description\": \"Payment for booking #12345\",\n  \"paymentMethod\": \"credit_card\",\n  \"reference\": \"txn_12345\"\n}"
								},
								"url": {
									"raw": "{{baseUrl}}/v1/wallet/internal/credit",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"wallet",
										"internal",
										"credit"
									]
								},
								"description": "Credits a user's wallet (internal service-to-service API)"
							},
							"response": [
								{
									"name": "Success Response",
									"originalRequest": {
										"method": "POST",
										"header": [
											{
												"key": "Content-Type",
												"value": "application/json",
												"type": "text"
											}
										],
										"body": {
											"mode": "raw",
											"raw": "{\n  \"userId\": \"60d21b4667d0d8992e610c86\",\n  \"amount\": 500.00,\n  \"source\": \"booking\",\n  \"sourceId\": \"60d21b4667d0d8992e610c87\",\n  \"bookingId\": \"60d21b4667d0d8992e610c88\",\n  \"description\": \"Payment for booking #12345\",\n  \"paymentMethod\": \"credit_card\",\n  \"reference\": \"txn_12345\"\n}"
										},
										"url": {
											"raw": "{{base_url}}/internal/credit",
											"host": [
												"{{base_url}}"
											],
											"path": [
												"internal",
												"credit"
											]
										}
									},
									"status": "OK",
									"code": 200,
									"_postman_previewlanguage": "json",
									"header": [
										{
											"key": "Content-Type",
											"value": "application/json"
										}
									],
									"cookie": [],
									"body": "{\n  \"success\": true,\n  \"message\": \"Wallet credited successfully\",\n  \"data\": {\n    \"wallet\": {\n      \"_id\": \"60d21b4667d0d8992e610c95\",\n      \"userId\": \"60d21b4667d0d8992e610c86\",\n      \"balance\": 2000.00,\n      \"currency\": \"USD\",\n      \"status\": \"active\",\n      \"createdAt\": \"2023-06-01T10:00:00Z\",\n      \"updatedAt\": \"2023-06-22T15:30:00Z\"\n    },\n    \"transaction\": {\n      \"_id\": \"60d21b4667d0d8992e610c96\",\n      \"userId\": \"60d21b4667d0d8992e610c86\",\n      \"amount\": 500.00,\n      \"type\": \"credit\",\n      \"status\": \"completed\",\n      \"source\": \"booking\",\n      \"sourceId\": \"60d21b4667d0d8992e610c87\",\n      \"bookingId\": \"60d21b4667d0d8992e610c88\",\n      \"description\": \"Payment for booking #12345\",\n      \"paymentMethod\": \"credit_card\",\n      \"reference\": \"txn_12345\",\n      \"createdAt\": \"2023-06-22T15:30:00Z\",\n      \"updatedAt\": \"2023-06-22T15:30:00Z\"\n    }\n  }\n}"
								}
							]
						},
						{
							"name": "Debit Wallet",
							"request": {
								"method": "POST",
								"header": [
									{
										"key": "Content-Type",
										"value": "application/json",
										"type": "text"
									}
								],
								"body": {
									"mode": "raw",
									"raw": "{\n  \"userId\": \"60d21b4667d0d8992e610c86\",\n  \"amount\": 300.00,\n  \"source\": \"refund\",\n  \"sourceId\": \"60d21b4667d0d8992e610c97\",\n  \"bookingId\": \"60d21b4667d0d8992e610c98\",\n  \"description\": \"Refund for cancelled booking #54321\",\n  \"reference\": \"ref_54321\"\n}"
								},
								"url": {
									"raw": "{{baseUrl}}/v1/wallet/internal/debit",
									"host": [
										"{{baseUrl}}"
									],
									"path": [
										"v1",
										"wallet",
										"internal",
										"debit"
									]
								},
								"description": "Debits a user's wallet (internal service-to-service API)"
							},
							"response": [
								{
									"name": "Success Response",
									"originalRequest": {
										"method": "POST",
										"header": [
											{
												"key": "Content-Type",
												"value": "application/json",
												"type": "text"
											}
										],
										"body": {
											"mode": "raw",
											"raw": "{\n  \"userId\": \"60d21b4667d0d8992e610c86\",\n  \"amount\": 300.00,\n  \"source\": \"refund\",\n  \"sourceId\": \"60d21b4667d0d8992e610c97\",\n  \"bookingId\": \"60d21b4667d0d8992e610c98\",\n  \"description\": \"Refund for cancelled booking #54321\",\n  \"reference\": \"ref_54321\"\n}"
										},
										"url": {
											"raw": "{{base_url}}/internal/debit",
											"host": [
												"{{base_url}}"
											],
											"path": [
												"internal",
												"debit"
											]
										}
									},
									"status": "OK",
									"code": 200,
									"_postman_previewlanguage": "json",
									"header": [
										{
											"key": "Content-Type",
											"value": "application/json"
										}
									],
									"cookie": [],
									"body": "{\n  \"success\": true,\n  \"message\": \"Wallet debited successfully\",\n  \"data\": {\n    \"wallet\": {\n      \"_id\": \"60d21b4667d0d8992e610c95\",\n      \"userId\": \"60d21b4667d0d8992e610c86\",\n      \"balance\": 1700.00,\n      \"currency\": \"USD\",\n      \"status\": \"active\",\n      \"createdAt\": \"2023-06-01T10:00:00Z\",\n      \"updatedAt\": \"2023-06-22T16:00:00Z\"\n    },\n    \"transaction\": {\n      \"_id\": \"60d21b4667d0d8992e610c99\",\n      \"userId\": \"60d21b4667d0d8992e610c86\",\n      \"amount\": 300.00,\n      \"type\": \"debit\",\n      \"status\": \"completed\",\n      \"source\": \"refund\",\n      \"sourceId\": \"60d21b4667d0d8992e610c97\",\n      \"bookingId\": \"60d21b4667d0d8992e610c98\",\n      \"description\": \"Refund for cancelled booking #54321\",\n      \"reference\": \"ref_54321\",\n      \"createdAt\": \"2023-06-22T16:00:00Z\",\n      \"updatedAt\": \"2023-06-22T16:00:00Z\"\n    }\n  }\n}"
								},
								{
									"name": "Insufficient Balance Response",
									"originalRequest": {
										"method": "POST",
										"header": [
											{
												"key": "Content-Type",
												"value": "application/json",
												"type": "text"
											}
										],
										"body": {
											"mode": "raw",
											"raw": "{\n  \"userId\": \"60d21b4667d0d8992e610c86\",\n  \"amount\": 5000.00,\n  \"source\": \"refund\",\n  \"sourceId\": \"60d21b4667d0d8992e610c97\",\n  \"bookingId\": \"60d21b4667d0d8992e610c98\",\n  \"description\": \"Refund for cancelled booking #54321\",\n  \"reference\": \"ref_54321\"\n}"
										},
										"url": {
											"raw": "{{base_url}}/internal/debit",
											"host": [
												"{{base_url}}"
											],
											"path": [
												"internal",
												"debit"
											]
										}
									},
									"status": "Bad Request",
									"code": 400,
									"_postman_previewlanguage": "json",
									"header": [
										{
											"key": "Content-Type",
											"value": "application/json"
										}
									],
									"cookie": [],
									"body": "{\n  \"success\": false,\n  \"message\": \"Failed to debit wallet\",\n  \"error\": \"Insufficient wallet balance\"\n}"
								}
							]
						}
					],
					"description": "Internal APIs for service-to-service communication"
				}
			]
		},
		{
			"name": "Wishlist Service",
			"item": [
				{
					"name": "Add to Wishlist",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"boatId\": \"60d21b4667d0d8992e610c85\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/wishlist",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"wishlist"
							]
						},
						"description": "Add a boat to wishlist"
					},
					"response": []
				},
				{
					"name": "Get Wishlist",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/wishlist",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"wishlist"
							]
						},
						"description": "Get user's wishlist"
					},
					"response": []
				},
				{
					"name": "Remove from Wishlist",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/wishlist/:boatId",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"wishlist",
								":boatId"
							],
							"variable": [
								{
									"key": "boatId",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Remove a boat from wishlist"
					},
					"response": []
				},
				{
					"name": "Check Wishlist Status",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/wishlist/status/:boatId",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"wishlist",
								"status",
								":boatId"
							],
							"variable": [
								{
									"key": "boatId",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Check if a boat is in user's wishlist"
					},
					"response": []
				}
			]
		},
		{
			"name": "Reviews Service",
			"item": [
				{
					"name": "Add Review",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"boatId\": \"60d21b4667d0d8992e610c85\",\n  \"rating\": 5,\n  \"comment\": \"Great experience!\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/reviews",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"reviews"
							]
						},
						"description": "Add a review for a boat"
					},
					"response": []
				},
				{
					"name": "Get Reviews",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/reviews?boatId=60d21b4667d0d8992e610c85&page=1&limit=10",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"reviews"
							],
							"query": [
								{
									"key": "boatId",
									"value": "60d21b4667d0d8992e610c85"
								},
								{
									"key": "userId",
									"value": "",
									"disabled": true
								},
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								}
							]
						},
						"description": "Get reviews for a boat"
					},
					"response": []
				},
				{
					"name": "Delete Review",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/reviews/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"reviews",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Delete a review"
					},
					"response": []
				}
			]
		},
		{
			"name": "Affiliate Service",
			"item": [
				{
					"name": "Get My Affiliate Status",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/me",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"me"
							]
						},
						"description": "Get current user's affiliate status"
					},
					"response": []
				},
				{
					"name": "Get My Affiliated Boats",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/me/boats",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"me",
								"boats"
							]
						},
						"description": "Get boats affiliated with current user's affiliate code"
					},
					"response": []
				},
				{
					"name": "Register Affiliate",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"name\": \"Ocean Adventures\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNo\": \"+*********0\",\n  \"accountHolderName\": \"John Smith\",\n  \"accountNumber\": \"*********0\",\n  \"bankName\": \"Ocean Bank\",\n  \"routingNumber\": \"*********\",\n  \"paypalEmail\": \"<EMAIL>\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/register",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"register"
							]
						},
						"description": "Register as an affiliate"
					},
					"response": []
				},
				{
					"name": "Get All Affiliates (Admin)",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/admin",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"admin"
							]
						},
						"description": "Get all affiliates (Admin only)"
					},
					"response": []
				},
				{
					"name": "Get Affiliates By Status (Admin)",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/admin/status/:status",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"admin",
								"status",
								":status"
							],
							"variable": [
								{
									"key": "status",
									"value": "pending",
									"description": "Status can be pending, approved, rejected"
								}
							]
						},
						"description": "Get affiliates by status (Admin only)"
					},
					"response": []
				},
				{
					"name": "Get Affiliate By ID (Admin)",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/admin/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"admin",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Get affiliate by ID (Admin only)"
					},
					"response": []
				},
				{
					"name": "Update Affiliate (Admin)",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"businessName\": \"Updated Ocean Adventures\",\n  \"businessEmail\": \"<EMAIL>\",\n  \"businessPhone\": \"+*********0\",\n  \"businessDescription\": \"Updated description\",\n  \"businessAddress\": \"456 Ocean Drive\",\n  \"businessCity\": \"Miami Beach\",\n  \"businessState\": \"FL\",\n  \"businessZip\": \"33140\",\n  \"businessCountry\": \"USA\",\n  \"businessWebsite\": \"https://updated-oceanadventures.com\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/admin/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"admin",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Update affiliate (Admin only)"
					},
					"response": []
				},
				{
					"name": "Delete Affiliate (Admin)",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/admin/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"admin",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Delete affiliate (Admin only)"
					},
					"response": []
				},
				{
					"name": "Approve Affiliate (Admin)",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/admin/:id/approve",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"admin",
								":id",
								"approve"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Approve affiliate application (Admin only)"
					},
					"response": []
				},
				{
					"name": "Reject Affiliate (Admin)",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"reason\": \"Business information could not be verified\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/admin/:id/reject",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"admin",
								":id",
								"reject"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Reject affiliate application (Admin only)"
					},
					"response": []
				},
				{
					"name": "Reapply Affiliate",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"name\": \"Ocean Adventures\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNo\": \"+*********0\",\n  \"accountHolderName\": \"John Smith\",\n  \"accountNumber\": \"*********0\",\n  \"bankName\": \"Ocean Bank\",\n  \"routingNumber\": \"*********\",\n  \"paypalEmail\": \"<EMAIL>\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/affiliate/reapply",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"affiliate",
								"reapply"
							]
						},
						"description": "Reapply for affiliate status after rejection"
					},
					"response": []
				}
			]
		},
		{
			"name": "Notification Service",
			"item": [
				{
					"name": "Get Notifications",
					"request": {
						"auth": {
							"type": "bearer",
							"bearer": [
								{
									"key": "token",
									"value": "{{token}}",
									"type": "string"
								}
							]
						},
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/notifications/list?page=1&limit=10",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"notifications",
								"list"
							],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								}
							]
						},
						"description": "Get user notifications with pagination"
					},
					"response": []
				},
				{
					"name": "Mark Notification as Read",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/notifications/:notificationId/read",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"notifications",
								":notificationId",
								"read"
							],
							"variable": [
								{
									"key": "notificationId",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Mark a specific notification as read"
					},
					"response": []
				},
				{
					"name": "Mark All Notifications as Read",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/notifications/read-all",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"notifications",
								"read-all"
							]
						},
						"description": "Mark all notifications as read"
					},
					"response": []
				}
			]
		},
		{
			"name": "About Us Service",
			"item": [
				{
					"name": "Get Active About Us",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/about-us",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"about-us"
							]
						},
						"description": "Get the active About Us page"
					},
					"response": []
				},
				{
					"name": "Get About Us by Version",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/about-us/version/:version",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"about-us",
								"version",
								":version"
							],
							"variable": [
								{
									"key": "version",
									"value": "1.0"
								}
							]
						},
						"description": "Get About Us by specific version"
					},
					"response": []
				},
				{
					"name": "Create About Us (Admin)",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"version\": \"1.0\",\n  \"title\": \"About Sea Escape\",\n  \"content\": \"Sea Escape is a premier boat rental platform...\",\n  \"isActive\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/about-us",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"about-us"
							]
						},
						"description": "Create a new About Us page (Admin only)"
					},
					"response": []
				},
				{
					"name": "Get All About Us (Admin)",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/about-us/all",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"about-us",
								"all"
							]
						},
						"description": "Get all About Us pages (Admin only)"
					},
					"response": []
				},
				{
					"name": "Update About Us (Admin)",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"title\": \"Updated About Sea Escape\",\n  \"content\": \"Sea Escape is the premier boat rental platform...\",\n  \"isActive\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/about-us/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"about-us",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Update an existing About Us page (Admin only)"
					},
					"response": []
				},
				{
					"name": "Delete About Us (Admin)",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/about-us/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"about-us",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Delete an About Us page (Admin only)"
					},
					"response": []
				}
			]
		},
		{
			"name": "Privacy Policy Service",
			"item": [
				{
					"name": "Get Active Privacy Policy",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/v1/privacy-policy",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"privacy-policy"
							]
						},
						"description": "Get the active Privacy Policy"
					},
					"response": []
				},
				{
					"name": "Get Privacy Policy by Version",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/v1/privacy-policy/version/:version",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"privacy-policy",
								"version",
								":version"
							],
							"variable": [
								{
									"key": "version",
									"value": "1.0"
								}
							]
						},
						"description": "Get Privacy Policy by specific version"
					},
					"response": []
				},
				{
					"name": "Create Privacy Policy (Admin)",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"version\": \"1.0\",\n  \"title\": \"Privacy Policy\",\n  \"sections\": [\n    {\n      \"heading\": \"Introduction\",\n      \"content\": \"This Privacy Policy describes how Sea Escape collects and processes your personal information.\"\n    },\n    {\n      \"heading\": \"Information We Collect\",\n      \"content\": \"We collect information that you provide directly to us.\"\n    }\n  ],\n  \"isActive\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/privacy-policy",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"privacy-policy"
							]
						},
						"description": "Create a new Privacy Policy (Admin only)"
					},
					"response": []
				},
				{
					"name": "Get All Privacy Policies (Admin)",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/privacy-policy/all",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"privacy-policy",
								"all"
							]
						},
						"description": "Get all Privacy Policies (Admin only)"
					},
					"response": []
				},
				{
					"name": "Update Privacy Policy (Admin)",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"title\": \"Updated Privacy Policy\",\n  \"sections\": [\n    {\n      \"heading\": \"Updated Introduction\",\n      \"content\": \"This Privacy Policy describes how Sea Escape collects and processes your personal information.\"\n    },\n    {\n      \"heading\": \"Information We Collect\",\n      \"content\": \"We collect information that you provide directly to us.\"\n    }\n  ],\n  \"isActive\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/privacy-policy/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"privacy-policy",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Update an existing Privacy Policy (Admin only)"
					},
					"response": []
				},
				{
					"name": "Delete Privacy Policy (Admin)",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/privacy-policy/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"privacy-policy",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Delete a Privacy Policy (Admin only)"
					},
					"response": []
				}
			]
		},
		{
			"name": "Terms and Conditions Service",
			"item": [
				{
					"name": "Get Active Terms and Conditions",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/v1/terms-condition",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"terms-condition"
							]
						},
						"description": "Get the active Terms and Conditions"
					},
					"response": []
				},
				{
					"name": "Get Terms and Conditions by Version",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/v1/terms-condition/version/:version",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"terms-condition",
								"version",
								":version"
							],
							"variable": [
								{
									"key": "version",
									"value": "1.0"
								}
							]
						},
						"description": "Get Terms and Conditions by specific version"
					},
					"response": []
				},
				{
					"name": "Create Terms and Conditions (Admin)",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"version\": \"1.0\",\n  \"title\": \"Terms and Conditions\",\n  \"sections\": [\n    {\n      \"heading\": \"Acceptance of Terms\",\n      \"content\": \"By accessing or using Sea Escape, you agree to be bound by these Terms and Conditions.\"\n    },\n    {\n      \"heading\": \"User Accounts\",\n      \"content\": \"To use certain features of our service, you must register for an account.\"\n    }\n  ],\n  \"isActive\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/terms-condition",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"terms-condition"
							]
						},
						"description": "Create new Terms and Conditions (Admin only)"
					},
					"response": []
				},
				{
					"name": "Get All Terms and Conditions (Admin)",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/terms-condition/all",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"terms-condition",
								"all"
							]
						},
						"description": "Get all Terms and Conditions (Admin only)"
					},
					"response": []
				},
				{
					"name": "Update Terms and Conditions (Admin)",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"title\": \"Updated Terms and Conditions\",\n  \"sections\": [\n    {\n      \"heading\": \"Updated Acceptance of Terms\",\n      \"content\": \"By accessing or using Sea Escape, you agree to be bound by these Terms and Conditions.\"\n    },\n    {\n      \"heading\": \"User Accounts\",\n      \"content\": \"To use certain features of our service, you must register for an account.\"\n    }\n  ],\n  \"isActive\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/terms-condition/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"terms-condition",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Update existing Terms and Conditions (Admin only)"
					},
					"response": []
				},
				{
					"name": "Delete Terms and Conditions (Admin)",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/terms-condition/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"terms-condition",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Delete Terms and Conditions (Admin only)"
					},
					"response": []
				}
			]
		},
		{
			"name": "Social Media Service",
			"item": [
				{
					"name": "Get Active Social Media Profile",
					"request": {
						"auth": {
							"type": "bearer",
							"bearer": [
								{
									"key": "token",
									"value": "{{token}}",
									"type": "string"
								}
							]
						},
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/v1/social-media",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"social-media"
							]
						},
						"description": "Get the active Social Media Profile"
					},
					"response": []
				},
				{
					"name": "Get Social Media Profile by Version",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/v1/social-media/version/:version",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"social-media",
								"version",
								":version"
							],
							"variable": [
								{
									"key": "version",
									"value": "1.0"
								}
							]
						},
						"description": "Get Social Media Profile by specific version"
					},
					"response": []
				},
				{
					"name": "Create Social Media Profile (Admin)",
					"request": {
						"auth": {
							"type": "bearer",
							"bearer": [
								{
									"key": "token",
									"value": "{{token}}",
									"type": "string"
								}
							]
						},
						"method": "POST",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"version\": \"1.0\",\n  \"platforms\": [\n    {\n      \"platform\": \"Twitter\",\n      \"username\": \"@seaescape\",\n      \"url\": \"https://twitter.com/seaescape\",\n      \"icon\": \"twitter-icon\",\n      \"isActive\": true\n    },\n    {\n      \"platform\": \"Instagram\",\n      \"username\": \"@seaescape\",\n      \"url\": \"https://instagram.com/seaescape\",\n      \"icon\": \"instagram-icon\",\n      \"isActive\": true\n    }\n  ],\n  \"isActive\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/social-media",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"social-media"
							]
						},
						"description": "Create a new Social Media Profile (Admin only)"
					},
					"response": []
				},
				{
					"name": "Get All Social Media Profiles (Admin)",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/social-media/all",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"social-media",
								"all"
							]
						},
						"description": "Get all Social Media Profiles (Admin only)"
					},
					"response": []
				},
				{
					"name": "Update Social Media Profile (Admin)",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							},
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"platforms\": [\n    {\n      \"platform\": \"Twitter\",\n      \"username\": \"@seaescape_updated\",\n      \"url\": \"https://twitter.com/seaescape_updated\",\n      \"icon\": \"twitter-icon\",\n      \"isActive\": true\n    },\n    {\n      \"platform\": \"Facebook\",\n      \"username\": \"Sea Escape\",\n      \"url\": \"https://facebook.com/seaescape\",\n      \"icon\": \"facebook-icon\",\n      \"isActive\": true\n    }\n  ],\n  \"isActive\": true\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/social-media/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"social-media",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Update an existing Social Media Profile (Admin only)"
					},
					"response": []
				},
				{
					"name": "Delete Social Media Profile (Admin)",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/social-media/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"social-media",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Delete a Social Media Profile (Admin only)"
					},
					"response": []
				}
			]
		},
		{
			"name": "Mail Service",
			"item": [
				{
					"name": "Get Mails",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/mails?page=1&limit=10",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"mails"
							],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								},
								{
									"key": "mailStatus",
									"value": "sent",
									"disabled": true
								},
								{
									"key": "mailFrom",
									"value": "",
									"disabled": true
								},
								{
									"key": "mailTo",
									"value": "",
									"disabled": true
								},
								{
									"key": "mailSubject",
									"value": "",
									"disabled": true
								},
								{
									"key": "createdBy",
									"value": "",
									"disabled": true
								},
								{
									"key": "startDate",
									"value": "",
									"disabled": true
								},
								{
									"key": "endDate",
									"value": "",
									"disabled": true
								}
							]
						},
						"description": "Get mail history with pagination"
					},
					"response": []
				},
				{
					"name": "Get Mail by ID",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/mails/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"mails",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Get specific mail by ID"
					},
					"response": []
				}
			]
		},
		{
			"name": "Changelog Service",
			"item": [
				{
					"name": "Get Activity Logs",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/changelog?page=1&limit=10",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"changelog"
							],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								}
							]
						},
						"description": "Get activity logs with pagination"
					},
					"response": []
				},
				{
					"name": "Get Activity Log by ID",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/changelog/:id",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"changelog",
								":id"
							],
							"variable": [
								{
									"key": "id",
									"value": "60d21b4667d0d8992e610c85"
								}
							]
						},
						"description": "Get specific activity log by ID"
					},
					"response": []
				}
			]
		},
		{
			"name": "Newsletter Management",
			"item": [
				{
					"name": "Subscribe to Newsletter",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json",
								"type": "text"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\",\n  \"firstName\": \"John\",\n  \"lastName\": \"Doe\",\n  \"preferences\": [\"promotions\", \"news\", \"events\"]\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/newsletter/subscribe",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"newsletter",
								"subscribe"
							]
						},
						"description": "Subscribe to the newsletter"
					},
					"response": []
				},
				{
					"name": "Unsubscribe from Newsletter",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json",
								"type": "text"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/newsletter/unsubscribe",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"newsletter",
								"unsubscribe"
							]
						},
						"description": "Unsubscribe from the newsletter"
					},
					"response": []
				},
				{
					"name": "Update Newsletter Preferences",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json",
								"type": "text"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"email\": \"<EMAIL>\",\n  \"preferences\": [\"promotions\", \"events\"]\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/newsletter/preferences",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"newsletter",
								"preferences"
							]
						},
						"description": "Update newsletter preferences"
					},
					"response": []
				},
				{
					"name": "Get Newsletter Subscribers",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "Bearer {{token}}",
								"type": "text"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/v1/newsletter/subscribers?page=1&limit=10",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"newsletter",
								"subscribers"
							],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								}
							]
						},
						"description": "Get all newsletter subscribers (admin only)"
					},
					"response": []
				},
				{
					"name": "Send Newsletter",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json",
								"type": "text"
							},
							{
								"key": "Authorization",
								"value": "Bearer {{token}}",
								"type": "text"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"subject\": \"Summer Boat Deals\",\n  \"content\": \"<h1>Summer Boat Deals</h1><p>Check out our latest summer boat rental deals!</p>\",\n  \"targetPreferences\": [\"promotions\"],\n  \"scheduledAt\": \"2023-06-15T10:00:00Z\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/v1/newsletter/send",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"v1",
								"newsletter",
								"send"
							]
						},
						"description": "Send a newsletter to subscribers (admin only)"
					},
					"response": []
				}
			],
			"description": "Endpoints for managing newsletters"
		}
	],
	"variable": [
		{
			"key": "baseUrl",
			"value": "http://localhost:3000",
			"type": "string"
		},
		{
			"key": "token",
			"value": "",
			"type": "string"
		}
	]
}