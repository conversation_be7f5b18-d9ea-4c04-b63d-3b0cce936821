import mongoose, { Schema, Document } from "mongoose";

export interface IAffiliate extends Document {
  userId: mongoose.Schema.Types.ObjectId;
  name: string;
  email: string;
  phoneNo: string;
  accountHolderName: string;
  accountNumber: string;
  bankName: string;
  routingNumber: string;
  paypalEmail?: string;
  affiliateCode: string;
  rejectionReason?: string;
  expireDate: Date;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum AffiliateStatus {
  Pending = "pending",
  Approved = "approved",
  Rejected = "rejected",
  Deleted = "deleted",
}

const affiliateSchema = new Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    phoneNo: {
      type: String,
      required: true,
      trim: true,
    },
    accountHolderName: {
      type: String,
      required: true,
      trim: true,
    },
    accountNumber: {
      type: String,
      required: true,
      trim: true,
    },
    bankName: {
      type: String,
      required: true,
      trim: true,
    },
    routingNumber: {
      type: String,
      required: true,
      trim: true,
    },
    paypalEmail: {
      type: String,
      trim: true,
    },
    affiliateCode: {
      type: String,
      unique: true,
    },
    rejectionReason: {
      type: String,
      required: false,
    },
    expireDate: {
      type: Date,
      required: false,
    },
    status: {
      type: String,
      enum: [...Object.values(AffiliateStatus)],
      default: AffiliateStatus.Pending,
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.model<IAffiliate>("Affiliates", affiliateSchema);
