import { Joi, Segments } from "celebrate";

export const createOrUpdateCommissionRateSchema = {
  [Segments.BODY]: Joi.object().keys({
    adminFee: Joi.number()
      .required()
      .min(0)
      .max(100)
      .message("Admin fee must be between 0 and 100 percent"),
    affiliateRate: Joi.number()
      .required()
      .min(0)
      .max(100)
      .message("Affiliate rate must be between 0 and 100 percent"),
  }),
};
