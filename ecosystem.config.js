module.exports = {
  apps: [
    // Global PM2 settings
    // These settings will be applied to all microservices unless overridden
    // See: https://pm2.keymetrics.io/docs/usage/application-declaration/
    {
      name: "api",
      script: "ts-runner.js",
      args: "./api/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "512M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=512 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "30%",
      env: {
        NODE_ENV: "dev",
      },
      env_test: {
        NODE_ENV: "test"
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "auth-ms",
      script: "ts-runner.js",
      args: "./auth-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "256M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=256 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "20%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "user-ms",
      script: "ts-runner.js",
      args: "./user-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "256M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=256 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "20%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "boat-ms",
      script: "ts-runner.js",
      args: "./boat-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "256M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=256 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "20%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "booking-ms",
      script: "ts-runner.js",
      args: "./booking-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "256M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=256 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "20%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "wishlist-ms",
      script: "ts-runner.js",
      args: "./wishlist-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "200M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=200 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "15%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "affiliate-ms",
      script: "ts-runner.js",
      args: "./affiliate-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "200M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=200 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "15%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "notification-ms",
      script: "ts-runner.js",
      args: "./notification-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "200M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=200 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "15%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "newsletter-ms",
      script: "ts-runner.js",
      args: "./newsletter-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "200M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=200 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "15%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "faq-ms",
      script: "ts-runner.js",
      args: "./faq-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "150M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=150 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "10%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "card-ms",
      script: "ts-runner.js",
      args: "./card-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "200M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=200 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "15%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "contact-us-ms",
      script: "ts-runner.js",
      args: "./contact-us-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "150M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=150 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "10%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "privacy-policy-ms",
      script: "ts-runner.js",
      args: "./privacy-policy-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "150M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=150 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "10%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "terms-condition-ms",
      script: "ts-runner.js",
      args: "./terms-condition-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "150M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=150 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "10%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "about-us-ms",
      script: "ts-runner.js",
      args: "./about-us-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "150M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=150 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "10%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "social-media-ms",
      script: "ts-runner.js",
      args: "./social-media-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "150M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=150 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "10%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "payment-ms",
      script: "ts-runner.js",
      args: "./payment-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "256M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=256 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "20%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "wallet-ms",
      script: "ts-runner.js",
      args: "./wallet-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "256M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=256 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "20%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "reviews-ms",
      script: "ts-runner.js",
      args: "./reviews-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "200M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=200 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "15%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "changelogs-ms",
      script: "ts-runner.js",
      args: "./changelogs-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "150M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=150 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "10%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "mail-ms",
      script: "ts-runner.js",
      args: "./mail-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "200M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=200 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "15%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
    {
      name: "chat-ms",
      script: "ts-runner.js",
      args: "./chat-ms/index.ts",
      interpreter: "node",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "256M",
      exec_mode: "cluster",
      exp_backoff_restart_delay: 100,
      kill_timeout: 5000,
      restart_delay: 3000,
      max_restarts: 10,
      node_args: "--max-old-space-size=256 --expose-gc",
      cron_restart: "0 */12 * * *",
      listen_timeout: 8000,
      cpu: "20%",
      env: {
        NODE_ENV: "dev",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
  ],
};
