import { StatusCodes } from "http-status-codes";
import logger from "../../../shared/services/logger.service";
import Facility, { FacilityStatus } from "../models/Facility";
import File, { FileType } from "../../../shared/models/Files";
import { UserRole } from "../../../user-ms/src/models/User";
import {
  facilityCreateBodyValidator,
  facilityUpdateBodyValidator,
} from "../validator/facility.validator";
import { getRelativePath } from "../../../shared/middleware/fileUpload.middleware";
import mongoose from "mongoose";

export const createFacility = async (req: any, res: any): Promise<any> => {
  try {
    const validationResult = facilityCreateBodyValidator.validate(req.body);
    if (validationResult.error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: validationResult.error.details[0].message,
      });
    }

    // Create file records and get file IDs
    const fileIds = [];
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        // Store only the path after uploads/
        const relativePath = getRelativePath(file.path);

        const newFile = await File.create({
          name: file.filename,
          size: file.size,
          fileType: file.mimetype,
          ext: file.originalname.split(".").pop(),
          location: relativePath,
          type: FileType.IMAGE,
          ownerId: req.user._id,
        });
        fileIds.push(newFile._id);
      }
    }

    const facility = await Facility.create({
      ...req.body,
      ownerId: req.user._id,
      images: fileIds,
    });

    // Populate file references
    await facility.populate("images");

    // Process image URLs if they exist
    const processedFacility: any = facility.toObject();
    if (processedFacility.images?.length > 0) {
      processedFacility.images = processedFacility.images.map(
        (img: any) => `${global.config.FILE_BASE_URL}${img.location}`,
      );
    }

    res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("FACILITY_CREATED_SUCCESS"),
      data: processedFacility,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getFacility = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const facility = await Facility.findById(id).populate("images");

    if (!facility) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("FACILITY_NOT_FOUND"),
      });
    }

    // Process image URLs if they exist
    const processedFacility: any = facility.toObject();
    if (processedFacility.images?.length > 0) {
      processedFacility.images = processedFacility.images.map(
        (img: any) => ({
          id: img._id,
          link: `${global.config.FILE_BASE_URL}${img.location}`
        })
      );
    }

    res.json({
      status: true,
      data: processedFacility,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getAllFacilities = async (req: any, res: any): Promise<any> => {
  try {
    const { page = 1, limit = 10, ...filters } = req.query;
    const skip = (page - 1) * limit;

    const query: any = {};

    // Add filters if provided
    if (filters.status) query.status = filters.status;
    if (filters.name) query.name = { $regex: filters.name, $options: "i" };

    if (req.user.role != UserRole.Admin) {
      query.ownerId = req.user._id;
      query.status = { $ne: FacilityStatus.DELETED };
    }

    const facilities = await Facility.find(query)
      .populate("images")
      .skip(skip)
      .limit(Number(limit))
      .sort({ createdAt: -1 });

    const totalFacilities = await Facility.countDocuments(query);

    // Process facilities data
    const processedFacilities = facilities.map((facility) => {
      const facilityObj: any = facility.toObject();
      if (facilityObj.images?.length > 0) {
        facilityObj.images = facilityObj.images.map(
          (img: any) => ({
            id: img._id,
            link: `${global.config.FILE_BASE_URL}${img.location}`
          })
        );
      }
      return facilityObj;
    });

    res.json({
      status: true,
      data: {
        facilities: processedFacilities,
        totalFacilities,
        currentPage: Number(page),
        totalPages: Math.ceil(totalFacilities / Number(limit)),
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const updateFacility = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const validationResult = facilityUpdateBodyValidator.validate(req.body);
    if (validationResult.error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: validationResult.error.details[0].message,
      });
    }

    // Handle images - support both existing and new uploads
    let finalImageIds: mongoose.Types.ObjectId[] = [];

    // First, handle existing images if provided
    if (req.body.existingImages) {
      try {
        const existingImagesArray = JSON.parse(req.body.existingImages || "[]");
        if (Array.isArray(existingImagesArray) && existingImagesArray.length > 0) {
          const existingImageIds = existingImagesArray
            .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
            .map((id: string) => new mongoose.Types.ObjectId(id));
          finalImageIds = [...existingImageIds];
        }
      } catch (error) {
        console.error("Error parsing existingImages:", error);
      }
    }

    // Then, handle new image uploads if any
    if (req.files && req.files.length > 0) {
      const newImageIds: mongoose.Types.ObjectId[] = [];
      for (const file of req.files) {
        // Store only the path after uploads/
        const relativePath = getRelativePath(file.path);

        const newFile = await File.create({
          name: file.filename,
          size: file.size,
          fileType: file.mimetype,
          ext: file.originalname.split(".").pop(),
          location: relativePath,
          type: FileType.IMAGE,
          ownerId: req.user._id,
        });
        newImageIds.push(newFile._id as mongoose.Types.ObjectId);
      }

      // Add new images to the final array
      finalImageIds = [...finalImageIds, ...newImageIds];
    }

    // Update images only if we have images to set or if existingImages was provided
    if (finalImageIds.length > 0 || req.body.existingImages) {
      updates.images = finalImageIds;
    }

    const facility = await Facility.findByIdAndUpdate(id, updates, {
      new: true,
    }).populate("images");

    if (!facility) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("FACILITY_NOT_FOUND"),
      });
    }

    // Process image URLs if they exist
    const processedFacility: any = facility.toObject();
    if (processedFacility.images?.length > 0) {
      processedFacility.images = processedFacility.images.map(
        (img: any) => ({
          id: img._id,
          link: `${global.config.FILE_BASE_URL}${img.location}`
        })
      );
    }

    res.json({
      status: true,
      message: res.__("FACILITY_UPDATED_SUCCESS"),
      data: processedFacility,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const deleteFacility = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;

    // Get facility to find associated files
    const facility = await Facility.findById(id);
    if (!facility) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("FACILITY_NOT_FOUND"),
      });
    }

    // Delete associated files (optional)
    if (facility.images && facility.images.length > 0) {
      await File.deleteMany({ _id: { $in: facility.images } });
    }

    // Update facility status to deleted
    await Facility.findByIdAndUpdate(id, { status: FacilityStatus.DELETED });

    res.json({
      status: true,
      message: res.__("FACILITY_DELETED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const uploadImage = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const file = req.file;

    if (!file) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "No file provided",
      });
    }

    const facility: any = await Facility.findById(id);
    if (!facility) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("FACILITY_NOT_FOUND"),
      });
    }

    // Store only the path after uploads/
    const relativePath = getRelativePath(file.path);

    // Create file record
    const newFile = await File.create({
      name: file.filename,
      size: file.size,
      fileType: file.mimetype,
      ext: file.originalname.split(".").pop(),
      location: relativePath,
      type: FileType.IMAGE,
      ownerId: req.user._id,
    });

    // Add file ID to facility images
    facility.images.push(newFile._id);
    await facility.save();

    // Populate the updated facility with file details
    await facility.populate("images");

    // Process image URLs
    const processedFacility = facility.toObject();
    if (processedFacility.images?.length > 0) {
      processedFacility.images = processedFacility.images.map(
        (img: any) => ({
          id: img._id,
          link: `${global.config.FILE_BASE_URL}${img.location}`
        })
      );
    }

    res.json({
      status: true,
      message: res.__("FACILITY_IMAGE_ADDED"),
      data: processedFacility,
    });
  } catch (error) {
    logger.error(error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Remove images from a facility
 * @param req
 * @param res
 * @returns
 */
export const removeFacilityImages = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const { imageIds } = req.body;
    const userId = req.user._id;

    if (!imageIds || !Array.isArray(imageIds) || imageIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("IMAGE_IDS_REQUIRED"),
      });
    }

    // Find the facility and verify ownership
    const facility = await Facility.findOne({
      _id: id,
      ownerId: userId,
      status: { $ne: FacilityStatus.DELETED },
    });

    if (!facility) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("FACILITY_NOT_FOUND"),
      });
    }

    // Validate that image IDs exist in facility's images
    const objectIdImageIds = imageIds.map((id: string) => new mongoose.Types.ObjectId(id));
    const validImageIds = objectIdImageIds.filter((removeId) =>
      facility.images.some((imageId) => removeId.equals(imageId))
    );

    if (validImageIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: res.__("INVALID_IMAGE_IDS"),
      });
    }

    // Remove images from facility
    facility.images = facility.images.filter(
      (imageId) => !validImageIds.some((removeId) => removeId.equals(imageId))
    );

    await facility.save();

    // Optionally delete file records (soft delete by updating status)
    await File.updateMany(
      { _id: { $in: validImageIds } },
      { status: "deleted" }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("FACILITY_IMAGES_REMOVED"),
      data: {
        removedImages: validImageIds.length,
        totalImages: facility.images.length,
      },
    });
  } catch (error: any) {
    logger.error(`Error removing facility images: ${error.message}`, {
      service: "facility-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};
