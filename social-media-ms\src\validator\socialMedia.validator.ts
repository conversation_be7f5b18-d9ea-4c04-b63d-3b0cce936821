import { Joi, Segments, celebrate } from "celebrate";

export const validateSocialMediaProfile = celebrate({
  [Segments.BODY]: Joi.object().keys({
    version: Joi.string().trim().required().min(1).max(20).messages({
      "string.empty": "SOCIAL_MEDIA_VERSION_REQUIRED",
      "string.min": "SOCIAL_MEDIA_VERSION_LENGTH",
      "string.max": "SOCIAL_MEDIA_VERSION_LENGTH",
      "any.required": "SOCIAL_MEDIA_VERSION_REQUIRED",
    }),
    platforms: Joi.array()
      .items(
        Joi.object({
          platform: Joi.string()
            .trim()
            .required()
            .valid("Twitter", "Instagram", "Facebook", "Snapchat", "Other")
            .messages({
              "string.empty": "SOCIAL_MEDIA_PLATFORM_REQUIRED",
              "any.only": "SOCIAL_MEDIA_PLATFORM_INVALID",
              "any.required": "SOCIAL_MEDIA_PLATFORM_REQUIRED",
            }),
          username: Joi.string().trim().required().min(1).max(100).messages({
            "string.empty": "SOCIAL_MEDIA_USERNAME_REQUIRED",
            "string.min": "SOCIAL_MEDIA_USERNAME_LENGTH",
            "string.max": "SOCIAL_MEDIA_USERNAME_LENGTH",
            "any.required": "SOCIAL_MEDIA_USERNAME_REQUIRED",
          }),
          url: Joi.string().trim().uri().allow("").messages({
            "string.uri": "SOCIAL_MEDIA_URL_INVALID",
          }),
          icon: Joi.string().trim().allow(""),
          isActive: Joi.boolean().default(true),
        }),
      )
      .min(1)
      .messages({
        "array.min": "SOCIAL_MEDIA_PLATFORMS_REQUIRED",
        "array.base": "SOCIAL_MEDIA_PLATFORMS_REQUIRED",
      }),
    isActive: Joi.boolean().default(false),
  }),
});

export const validateSocialMediaProfileUpdate = celebrate({
  [Segments.BODY]: Joi.object()
    .keys({
      platforms: Joi.array()
        .items(
          Joi.object({
            platform: Joi.string()
              .trim()
              .valid("Twitter", "Instagram", "Facebook", "Snapchat", "Other")
              .messages({
                "string.empty": "SOCIAL_MEDIA_PLATFORM_REQUIRED",
                "any.only": "SOCIAL_MEDIA_PLATFORM_INVALID",
              }),
            username: Joi.string().trim().min(1).max(100).messages({
              "string.empty": "SOCIAL_MEDIA_USERNAME_REQUIRED",
              "string.min": "SOCIAL_MEDIA_USERNAME_LENGTH",
              "string.max": "SOCIAL_MEDIA_USERNAME_LENGTH",
            }),
            url: Joi.string().trim().uri().allow("").messages({
              "string.uri": "SOCIAL_MEDIA_URL_INVALID",
            }),
            icon: Joi.string().trim().allow(""),
            isActive: Joi.boolean(),
          }),
        )
        .min(1)
        .messages({
          "array.min": "SOCIAL_MEDIA_PLATFORMS_REQUIRED",
          "array.base": "SOCIAL_MEDIA_PLATFORMS_REQUIRED",
        }),
      isActive: Joi.boolean(),
    })
    .min(1),
});

export const validateIdParam = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string()
      .required()
      .regex(/^[0-9a-fA-F]{24}$/)
      .messages({
        "string.pattern.base": "SOCIAL_MEDIA_INVALID_ID",
        "any.required": "SOCIAL_MEDIA_ID_REQUIRED",
      }),
  }),
});

export const validateVersionParam = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    version: Joi.string().required().messages({
      "any.required": "SOCIAL_MEDIA_VERSION_REQUIRED",
    }),
  }),
});
