import { celebrate, Joi, Segments } from "celebrate";
import { UserCurrency, UserLanguage } from "../../../user-ms/src/models/User";

const validateRegister = celebrate({
  [Segments.BODY]: Joi.object().keys({
    username: Joi.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    currency: Joi.string()
      .valid(...Object.values(UserCurrency))
      .required(),
    language: Joi.string()
      .valid(...Object.values(UserLanguage))
      .required(),
    interestedIn: Joi.array().items(Joi.string()).allow(null).optional(),
    otp: Joi.number().required(),
  }),
});

const validateLogin = celebrate({
  [Segments.BODY]: Joi.object().keys({
    email: Joi.string().email().required(),
    password: Joi.string().required(),
  }),
});

const validateForgetPassword = celebrate({
  [Segments.BODY]: Joi.object().keys({
    email: Joi.string().email().required(),
  }),
});

const validateResetPassword = celebrate({
  [Segments.BODY]: Joi.object().keys({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
  }),
});

export default {
  validateRegister,
  validateLogin,
  validateForgetPassword,
  validateResetPassword,
};
