# App Version Check Middleware

## Overview

The App Version Check Middleware provides automatic version checking for mobile applications, supporting both Android (integer versioning) and iOS (semantic versioning). It sets appropriate response headers to inform clients about update requirements.

## Features

- ✅ **Android Support**: Integer-based version checking (1, 2, 3, etc.)
- ✅ **iOS Support**: Semantic version checking (1.0.0, 1.1.0, etc.)
- ✅ **Three Update States**: OK, Upgrade, Upgrade-Required
- ✅ **Configurable**: Environment-specific version settings
- ✅ **Non-blocking**: Continues request processing on errors
- ✅ **Comprehensive Logging**: Detailed logging for monitoring

## Configuration

### Config File Setup (`shared/config.json`)

Add the following version configuration to each environment:

```json
{
  "dev": {
    "MIN_APP_VERSION": 1,
    "MAX_APP_VERSION": 1,
    "MIN_IOS_VERSION": "1.0.0",
    "MAX_IOS_VERSION": "1.0.0"
  },
  "test": {
    "MIN_APP_VERSION": 1,
    "MAX_APP_VERSION": 1,
    "MIN_IOS_VERSION": "1.0.0",
    "MAX_IOS_VERSION": "1.0.0"
  },
  "production": {
    "MIN_APP_VERSION": 1,
    "MAX_APP_VERSION": 2,
    "MIN_IOS_VERSION": "1.0.0",
    "MAX_IOS_VERSION": "1.1.0"
  }
}
```

### Configuration Parameters

- **MIN_APP_VERSION**: Minimum required Android app version (integer)
- **MAX_APP_VERSION**: Latest/current Android app version (integer)
- **MIN_IOS_VERSION**: Minimum required iOS app version (semantic)
- **MAX_IOS_VERSION**: Latest/current iOS app version (semantic)

## Usage

### 1. Import and Apply Middleware

```typescript
import { checkAppVersion } from "../shared/middleware/appVersionCheck.middleware";

// Apply middleware before authentication but after basic setup
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(responseFormatter);

// Add app version check middleware
app.use(checkAppVersion);

// Then apply authentication and routes
app.use("/", authenticateJWT, routes);
```

### 2. Client Request Headers

Clients must send these headers with their requests:

```http
App-Version: 1           # Android: integer version
Device-Type: android     # Platform identifier

# OR

App-Version: 1.0.0       # iOS: semantic version
Device-Type: ios         # Platform identifier
```

### 3. Server Response Headers

The middleware sets the following response header:

```http
App-Version: OK                 # Client is up to date
App-Version: Upgrade            # Update available but not required
App-Version: Upgrade-Required   # Update required to continue
```

## Version Logic

### Android (Integer Versioning)

```
Client Version < MIN_APP_VERSION  → "Upgrade-Required"
Client Version < MAX_APP_VERSION  → "Upgrade"
Client Version >= MAX_APP_VERSION → "OK"
```

### iOS (Semantic Versioning)

```
Client Version < MIN_IOS_VERSION  → "Upgrade-Required"
Client Version < MAX_IOS_VERSION  → "Upgrade"
Client Version >= MAX_IOS_VERSION → "OK"
```

## Examples

### Example 1: Android Version Check

**Configuration:**
```json
{
  "MIN_APP_VERSION": 2,
  "MAX_APP_VERSION": 4
}
```

**Client Requests:**
```http
App-Version: 1, Device-Type: android → Response: App-Version: Upgrade-Required
App-Version: 3, Device-Type: android → Response: App-Version: Upgrade
App-Version: 4, Device-Type: android → Response: App-Version: OK
```

### Example 2: iOS Version Check

**Configuration:**
```json
{
  "MIN_IOS_VERSION": "1.0.0",
  "MAX_IOS_VERSION": "1.2.0"
}
```

**Client Requests:**
```http
App-Version: 0.9.0, Device-Type: ios → Response: App-Version: Upgrade-Required
App-Version: 1.1.0, Device-Type: ios → Response: App-Version: Upgrade
App-Version: 1.2.0, Device-Type: ios → Response: App-Version: OK
```

## Client Implementation

### Android Example (Kotlin)

```kotlin
// Add headers to your API requests
val request = Request.Builder()
    .url("https://api.example.com/endpoint")
    .addHeader("App-Version", BuildConfig.VERSION_CODE.toString())
    .addHeader("Device-Type", "android")
    .build()

// Check response header
val appVersionStatus = response.header("App-Version")
when (appVersionStatus) {
    "Upgrade-Required" -> showForceUpdateDialog()
    "Upgrade" -> showOptionalUpdateDialog()
    "OK" -> continueNormally()
}
```

### iOS Example (Swift)

```swift
// Add headers to your API requests
var request = URLRequest(url: url)
request.setValue(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String, 
                forHTTPHeaderField: "App-Version")
request.setValue("ios", forHTTPHeaderField: "Device-Type")

// Check response header
if let appVersionStatus = response.value(forHTTPHeaderField: "App-Version") {
    switch appVersionStatus {
    case "Upgrade-Required":
        showForceUpdateDialog()
    case "Upgrade":
        showOptionalUpdateDialog()
    case "OK":
        continueNormally()
    default:
        break
    }
}
```

## Error Handling

The middleware is designed to be non-blocking:

- ✅ **Missing Headers**: Skips version check, continues processing
- ✅ **Invalid Versions**: Logs warning, continues processing
- ✅ **Unknown Device Types**: Logs warning, continues processing
- ✅ **Configuration Errors**: Logs error, continues processing

## Logging

The middleware provides comprehensive logging:

```typescript
// Debug logs for successful checks
logger.debug('Android version check completed', {
  clientVersion: 3,
  minVersion: 2,
  maxVersion: 4,
  status: 'Upgrade'
});

// Warning logs for invalid data
logger.warn('Invalid Android app version format', {
  appVersion: 'invalid',
  deviceType: 'android'
});

// Error logs for exceptions
logger.error('Error in app version check middleware', {
  error: 'Configuration missing'
});
```

## Integration Checklist

- [ ] Add version configuration to `shared/config.json` for all environments
- [ ] Import and apply middleware in your microservice
- [ ] Update client apps to send required headers
- [ ] Implement client-side update dialog handling
- [ ] Test with different version scenarios
- [ ] Monitor logs for version check activity

## Best Practices

1. **Apply Early**: Add middleware before authentication but after basic setup
2. **Monitor Usage**: Track version distribution through logs
3. **Gradual Rollout**: Update MAX versions gradually
4. **Clear Communication**: Provide clear update messages to users
5. **Fallback Handling**: Ensure app works even if headers are missing
