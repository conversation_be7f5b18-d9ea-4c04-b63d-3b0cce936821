import Jo<PERSON> from "joi";
import { celebrate, Segments } from "celebrate";
import { FIleStatus } from "../../../shared/models/Files";

export const idParamValidator = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.string().required(),
  }),
});

export const verifyDocumentValidator = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.string().required(),
  }),
  [Segments.BODY]: Joi.object({
    status: Joi.string()
      .valid(FIleStatus.ACTIVE, FIleStatus.REJECTED)
      .required(),
  }),
});
