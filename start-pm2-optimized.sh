#!/bin/bash

# Set default environment to dev if not provided
ENV=${1:-dev}

echo "Starting Sea Escape Microservices with PM2 (Optimized) in $ENV environment..."
echo

# Stop and delete any existing PM2 processes to avoid conflicts
pm2 delete all

echo
echo "Starting microservices in stages to prevent memory overload..."
echo

# Start API service first to ensure proper port allocation
echo "Starting API gateway..."
pm2 start ecosystem.config.js --only api --env $ENV
sleep 10

# Start critical services first
echo "Starting critical services (auth, user, payment)..."
pm2 start ecosystem.config.js --only auth-ms,user-ms,payment-ms --env $ENV
sleep 8

# Start core business services
echo "Starting core business services (boat, booking, wallet)..."
pm2 start ecosystem.config.js --only boat-ms,booking-ms,wallet-ms --env $ENV
sleep 8

# Start secondary services
echo "Starting secondary services (wishlist, reviews, card)..."
pm2 start ecosystem.config.js --only wishlist-ms,reviews-ms,card-ms --env $ENV
sleep 8

# Start communication services
echo "Starting communication services (notification, mail, chat)..."
pm2 start ecosystem.config.js --only notification-ms,mail-ms,chat-ms --env $ENV
sleep 8

# Start content services
echo "Starting content services (newsletter, faq, affiliate)..."
pm2 start ecosystem.config.js --only newsletter-ms,faq-ms,affiliate-ms --env $ENV
sleep 8

# Start static content services
echo "Starting static content services (about-us, privacy-policy, terms-condition, contact-us)..."
pm2 start ecosystem.config.js --only about-us-ms,privacy-policy-ms,terms-condition-ms,contact-us-ms --env $ENV
sleep 8

# Start remaining services
echo "Starting remaining services (social-media, changelogs)..."
pm2 start ecosystem.config.js --only social-media-ms,changelogs-ms --env $ENV

echo
echo "All microservices started successfully with PM2!"
echo
echo "Resource optimization tips:"
echo "- Use 'pm2 monit' to monitor resource usage in real-time"
echo "- Use 'pm2 reload [service-name]' to restart services with zero downtime"
echo "- Use 'pm2 scale [service-name] [number]' to adjust instances if needed"
echo "- Services are configured to restart automatically at 12-hour intervals"
echo
echo "Commands:"
echo "- 'npm run pm2:logs' to view logs"
echo "- 'npm run pm2:status' to check status"
echo "- 'npm run pm2:monitor' to monitor in real-time"
echo