import { StatusCodes } from "http-status-codes";
import Card, { CardStatus } from "../models/Card";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";
import logger from "../../../shared/services/logger.service";
import StripeService from "../services/stripe.service";
import Booking, { BookingStatus } from "../../../booking-ms/src/models/Booking";
import mongoose from "mongoose";

// Create a new card
export const createCard = async (req: any, res: any) => {
  try {
    const {
      cardToken,        // Card token from client
      cardNumber,       // OPTIONAL: For direct card processing
      expiryMonth,
      expiryYear,
      cvv,
      cardholderName,
      isDefault,
    } = req.body;
    const ownerId = req.user._id;

    // Get or create user's Stripe customer ID
    let stripeCustomerId = req.user.stripeCustomerId;

    if (!stripeCustomerId) {
      // Create Stripe customer if doesn't exist
      const customerResult = await StripeService.createCustomer({
        email: req.user.email,
        name: req.user.username || req.user.email,
        metadata: {
          userId: ownerId.toString(),
        },
      });

      if (!customerResult.success) {
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: req.__("STRIPE_CUSTOMER_CREATION_FAILED"),
        });
      }

      stripeCustomerId = customerResult.customer!.id;

      // Update user with Stripe customer ID
      const User = require("../../../user-ms/src/models/User").default;
      await User.findByIdAndUpdate(ownerId, { stripeCustomerId });
    }

    let sourceResult;

    // Method 1: Use card token to create source (preferred method)
    if (cardToken) {
      sourceResult = await StripeService.createSourceFromToken(cardToken, stripeCustomerId);
    }
    // Method 2: Direct card processing (fallback)
    else if (cardNumber && expiryMonth && expiryYear && cvv) {
      const validationResult = await StripeService.validateCard({
        number: cardNumber,
        expiry: `${expiryMonth}/${expiryYear.toString().slice(-2)}`,
        cvv,
        name: cardholderName,
        email: req.user.email,
      });

      if (!validationResult.isValid) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: validationResult.error || req.__("CARD_VALIDATION_FAILED"),
        });
      }

      sourceResult = {
        isValid: true,
        sourceId: validationResult.paymentMethodId,
        lastFourDigits: validationResult.lastFourDigits,
        cardType: validationResult.cardType,
      };
    }
    else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("CARD_TOKEN_OR_DETAILS_REQUIRED"),
      });
    }

    if (!sourceResult.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: sourceResult.error || req.__("CARD_SOURCE_CREATION_FAILED"),
      });
    }

    // Create card record
    const card = new Card({
      ownerId,
      cardType: sourceResult.cardType,
      lastFourDigits: sourceResult.lastFourDigits,
      expiryMonth: expiryMonth || 0,
      expiryYear: expiryYear || 0,
      cardholderName: cardholderName || "Card Holder",
      isDefault: isDefault || false,
      status: CardStatus.ACTIVE,
      stripeSourceId: sourceResult.sourceId!,
      stripeCustomerId: stripeCustomerId,
    });

    await card.save();

    // Log the creation
    const changes = validateAndGetChanges({}, card.toJSON());
    await createActivityLog("cards", card._id, "CREATE", changes, ownerId);

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: req.__("CARD_CREATED_SUCCESS"),
      data: card,
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get all cards for a user
export const getMyCards = async (req: any, res: any) => {
  try {
    const ownerId = req.user._id;
    const { status, page = 1, limit = 10 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const query: any = { ownerId };

    // Add status filter if provided
    if (status) {
      query.status = status;
    } else {
      // By default, don't show deleted cards
      query.status = { $ne: CardStatus.DELETED };
    }

    const cards = await Card.find(query)
      .sort({ isDefault: -1, createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));

    const totalCount = await Card.countDocuments(query);
    const totalPages = Math.ceil(totalCount / Number(limit));

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("CARDS_FETCH_SUCCESS"),
      data: {
        cards,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount,
          totalPages,
        },
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get card by ID
export const getCardById = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const ownerId = req.user._id;

    const card = await Card.findOne({
      _id: id,
      ownerId,
      status: { $ne: CardStatus.DELETED },
    });

    if (!card) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("CARD_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("CARD_FETCH_SUCCESS"),
      data: card,
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Update card
export const updateCard = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const ownerId = req.user._id;
    const updateData = req.body;

    const card = await Card.findOne({
      _id: id,
      ownerId,
      status: { $ne: CardStatus.DELETED },
    });

    if (!card) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("CARD_NOT_FOUND"),
      });
    }

    // Get changes before update
    const changes = validateAndGetChanges(card.toJSON(), updateData);

    // Update card
    const updatedCard = await Card.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true },
    );

    // Log the changes if any
    if (changes.length > 0) {
      await createActivityLog("cards", id, "UPDATE", changes, ownerId);
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("CARD_UPDATED_SUCCESS"),
      data: updatedCard,
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Delete card
export const deleteCard = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const ownerId = req.user._id;

    const card = await Card.findOne({
      _id: id,
      ownerId,
      status: { $ne: CardStatus.DELETED },
    });

    if (!card) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("CARD_NOT_FOUND"),
      });
    }

    // Delete source from Stripe
    try {
      await StripeService.deleteSource(card.stripeCustomerId, card.stripeSourceId);
    } catch (stripeError) {
      logger.warn("Failed to delete source from Stripe", {
        service: "card-ms",
        cardId: id,
        stripeSourceId: card.stripeSourceId,
        stripeCustomerId: card.stripeCustomerId,
        error: stripeError,
      });
      // Continue with soft delete even if Stripe deletion fails
    }

    // Get changes before update
    const changes = validateAndGetChanges(card.toJSON(), {
      status: CardStatus.DELETED,
    });

    // Soft delete the card
    await Card.findByIdAndUpdate(id, {
      status: CardStatus.DELETED,
      isDefault: false,
    });

    // Log the deletion
    await createActivityLog("cards", id, "DELETE", changes, ownerId);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("CARD_DELETED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Create payment intent for booking
export const createPaymentIntent = async (req: any, res: any) => {
  try {
    const { bookingId, amount, currency = 'usd', description, metadata } = req.body;
    const userId = req.user._id;

    // Validate booking exists and belongs to user
    const booking = await Booking.findOne({
      _id: bookingId,
      userId: userId,
      status: { $in: [BookingStatus.Pending, BookingStatus.ReadyForPayment] }
    }).populate('boatId', 'name ownerId');

    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    // Check if payment intent already exists for this booking
    if (booking.paymentIntentId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("PAYMENT_INTENT_ALREADY_EXISTS"),
        data: {
          paymentIntentId: booking.paymentIntentId,
          clientSecret: booking.paymentIntentClientSecret,
          stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
        }
      });
    }

    // Get or create user's Stripe customer ID
    let stripeCustomerId = req.user.stripeCustomerId;

    if (!stripeCustomerId) {
      const customerResult = await StripeService.createCustomer({
        email: req.user.email,
        name: req.user.name || req.user.username || req.user.email,
        metadata: {
          userId: userId.toString(),
        },
      });

      if (!customerResult.success) {
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: req.__("STRIPE_CUSTOMER_CREATION_FAILED"),
        });
      }

      stripeCustomerId = customerResult.customer!.id;

      // Update user with Stripe customer ID
      const User = require("../../../user-ms/src/models/User").default;
      await User.findByIdAndUpdate(userId, { stripeCustomerId });
    }

    // Prepare metadata for payment intent
    const paymentMetadata = {
      bookingId: bookingId,
      userId: userId.toString(),
      boatId: booking.boatId._id.toString(),
      boatOwnerId: booking.boatId.ownerId.toString(),
      amount: amount.toString(),
      currency: currency,
      ...metadata
    };

    // Create payment intent with hold capture method
    const paymentIntentResult = await StripeService.createPaymentIntent(
      amount,
      currency,
      {
        customerId: stripeCustomerId,
        description: description || `Sea Escape booking payment for ${booking.boatId.name}`,
        metadata: paymentMetadata,
        captureMethod: 'manual', // Hold the payment
      }
    );

    if (!paymentIntentResult.success) {
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: req.__("PAYMENT_INTENT_CREATION_FAILED"),
        error: paymentIntentResult.error,
      });
    }

    // Update booking with payment intent details
    await Booking.findByIdAndUpdate(bookingId, {
      paymentIntentId: paymentIntentResult.paymentIntent!.id,
      paymentIntentStatus: paymentIntentResult.paymentIntent!.status,
      paymentIntentClientSecret: paymentIntentResult.clientSecret,
      status: BookingStatus.ReadyForPayment,
    });

    // Log the payment intent creation
    const changes = validateAndGetChanges({}, {
      paymentIntentId: paymentIntentResult.paymentIntent!.id,
      paymentIntentStatus: paymentIntentResult.paymentIntent!.status,
      status: BookingStatus.ReadyForPayment,
    });
    await createActivityLog("bookings", bookingId, "UPDATE", changes, userId);

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: req.__("PAYMENT_INTENT_CREATED_SUCCESS"),
      data: {
        paymentIntentId: paymentIntentResult.paymentIntent!.id,
        clientSecret: paymentIntentResult.clientSecret,
        stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
        amount: amount,
        currency: currency,
        status: paymentIntentResult.paymentIntent!.status,
      },
    });
  } catch (error) {
    logger.error("Error creating payment intent", {
      service: "card-ms",
      error: error,
      userId: req.user._id,
      bookingId: req.body.bookingId,
    });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
