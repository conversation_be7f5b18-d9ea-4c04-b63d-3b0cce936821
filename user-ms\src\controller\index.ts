import User, { User<PERSON><PERSON>, UserStatus, IUser } from "../models/User";
import File, { FIleStatus, FileType } from "../../../shared/models/Files";
import { StatusCodes } from "http-status-codes";
import { comparePassword, encrypt } from "../../../shared/helper/utils";
import logger from "../../../shared/services/logger.service";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";
import { updateProfileValidator } from "../validator/user.validator";
import fs from "fs";
import path from "path";
import { getRelativePath } from "../../../shared/middleware/fileUpload.middleware";
import Reward, { RewardStatus } from "../../../shared/models/Reward";
import { formatUserData } from "../../../auth-ms/src/helper/utils";
import Boat, { RecordType, BoatStatus } from "../../../boat-ms/src/models/Boat";

// Create an extended interface for the response
interface UserResponse extends Partial<IUser> {
  avatarUrl?: string;
  boatsCount?: number;
}

export const getProfile = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId)
      .select("-password -otp -otpExpireTime -__v")
      .populate("avatar");

    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("USER_NOT_FOUND"),
      });
    }

    const currentYear = new Date().getFullYear();
    const expiryDate = new Date(currentYear, 11, 31, 23, 59, 59);
    
    let reward = await Reward.findOne({ userId: user?._id });
    if (!reward) {
      reward = await Reward.create({
        userId: user?._id,
        points: 0,
        pointsExpiryDate: expiryDate,
        welcomeDiscounts: [{ used: false }, { used: false }], // Two welcome discounts
        createdBy: user?._id,
        updatedBy: user?._id
      });
    }

    // Transform avatar URL if exists
    const userData: UserResponse = user.toObject();
    if (userData.avatar && typeof userData.avatar === "object") {
      userData.avatarUrl = `${global.config.FILE_BASE_URL}${(userData.avatar as any).location}`;
    }
    userData.avatar = (userData.avatar as any)?._id;

    res.json({
      status: true,
      data: userData,
    });
  } catch (error) {
    console.error(error);
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const updateProfile = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const file = req.file;

    // Manual validation directly in the controller
    const { username, currency, language } = req.body;
    const bodyValidate = {
      username,
      currency,
      language
    }

    // Validate the body directly instead of using celebrate middleware
    const validationResult = updateProfileValidator.validate(bodyValidate);
    if (validationResult.error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: validationResult.error.details[0].message,
      });
    }

    const user = await User.findById(userId)
      .select("-password -otp -otpExpireTime -__v")
      .populate("avatar");

    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("USER_NOT_FOUND"),
      });
    }

    const updatedFields: any = {};
    const oldAvatarId = user.avatar ? (user.avatar as any)._id : null;

    // Handle avatar file upload
    if (file) {
      const relativePath = getRelativePath(file.path);

      // Create new file record
      const newFile = await File.create({
        name: file.filename,
        size: file.size,
        fileType: file.mimetype,
        ext: file.originalname.split(".").pop(),
        location: relativePath,
        type: FileType.IMAGE,
        ownerId: userId,
      });
      updatedFields.avatar = newFile._id;
    }

    updatedFields.username = username || user.username;
    updatedFields.currency = currency || user.currency;
    updatedFields.language = language || user.language;

    const changes = validateAndGetChanges(user, updatedFields);
    if (changes.length > 0) {
      await createActivityLog(
        "users",
        user._id,
        "UPDATE",
        changes,
        req.user._id,
      );
    }

    // Update user
    await user.updateOne(updatedFields);

    // Delete old avatar file and record if a new one was uploaded
    if (file && oldAvatarId) {
      // Get old avatar record
      const oldAvatar = await File.findById(oldAvatarId);
      if (oldAvatar) {
        // Delete file from filesystem
        const filePath = path.resolve(
          __dirname,
          "../../../shared/uploads",
          oldAvatar.location,
        );
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        // Delete file record
        await File.findByIdAndUpdate(oldAvatarId, {
          status: FIleStatus.DELETED,
        });
      }
    }

    // Get updated user with populated avatar
    const updatedUser = await User.findById(userId)
      .select("-password -otp -otpExpireTime -__v")
      .populate("avatar");

    if (!updatedUser) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("USER_NOT_FOUND"),
      });
    }

    // Transform avatar URL
    const userData: UserResponse = updatedUser.toObject();
    if (userData.avatar && typeof userData.avatar === "object") {
      userData.avatarUrl = `${global.config.FILE_BASE_URL}${(userData.avatar as any).location}`;
    }
    userData.avatar = (userData.avatar as any)?._id;

    res.json({
      status: true,
      message: res.__("PROFILE_UPDATED_SUCCESSFULLY"),
      data: await formatUserData(updatedUser),
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getAllUsers = async (req: any, res: any) => {
  try {
    if (req.user.role !== UserRole.Admin) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("ACCESS_DENIED"),
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || "";

    const searchQuery = {
      $or: [
        { username: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
      ],
    };

    const totalUsers = await User.countDocuments(searchQuery);
    const totalPages = Math.ceil(totalUsers / limit);

    const users = await User.find(searchQuery)
      .select("-password -otp -otpExpireTime -__v")
      .populate("avatar")
      .skip((page - 1) * limit)
      .limit(limit);

    // Get boat counts for all users
    const userIds = users.map(user => user._id);
    const boatCounts = await Boat.aggregate([
      {
        $match: {
          ownerId: { $in: userIds },
          status: { $ne: BoatStatus.DELETED }
        }
      },
      {
        $group: {
          _id: "$ownerId",
          boatsCount: { $sum: 1 }
        }
      }
    ]);

    // Create a map for quick lookup
    const boatCountMap = new Map();
    boatCounts.forEach(item => {
      boatCountMap.set(item._id.toString(), item.boatsCount);
    });

    // Transform avatar URLs and add boatsCount
    const transformedUsers = users.map((user) => {
      const userData: UserResponse = user.toObject();
      if (userData.avatar && typeof userData.avatar === "object") {
        userData.avatarUrl = `${global.config.FILE_BASE_URL}${(userData.avatar as any).location}`;
      }
      // Add boatsCount for each user
      userData.boatsCount = boatCountMap.get((user._id as any).toString()) || 0;
      return userData;
    });

    res.json({
      status: true,
      data: {
        users: transformedUsers,
        pagination: {
          page,
          size: limit,
          totalCount: totalUsers,
          totalPages,
        },
      },
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const changePassword = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const { currentPassword, newPassword } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("USER_NOT_FOUND"),
      });
    }

    const isMatch = await comparePassword(currentPassword, user.password);
    if (!isMatch) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CURRENT_PASSWORD_INCORRECT"),
      });
    }

    user.password = await encrypt(newPassword);
    await user.save();

    res.json({
      status: true,
      message: res.__("SUCCESS_CHANGE_PASSWORD"),
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const changeUserRole = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const { role } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("USER_NOT_FOUND"),
      });
    }

    if (role === UserRole.Admin) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("ACCESS_DENIED"),
      });
    }

    user.currentRole = role;
    await user.save();
    const changes = validateAndGetChanges(user, { currentRole: role });
    await createActivityLog("users", user._id, "UPDATE", changes, req.user._id);

    res.json({
      status: true,
      message: res.__("USER_ROLE_CHANGED_SUCCESSFULLY"),
    });
  } catch (err) {
    logger.error(err);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: err,
    });
  }
};

export const deleteAccount = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const { deleteReason } = req.body;
    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("USER_NOT_FOUND"),
      });
    }

    user.status = UserStatus.Deleted;
    user.deleteReason = deleteReason;

    await Reward.findOneAndUpdate({ userId }, { 
      points: 0,
      pointsExpiryDate: null,
      welcomeDiscounts: [],
      createdBy: userId,
      updatedBy: userId,
      status: RewardStatus.DELETED
    });

    await user.save();
    const changes = validateAndGetChanges(user, { status: UserStatus.Deleted });
    await createActivityLog("users", user._id, "UPDATE", changes, req.user._id);

    res.json({
      status: true,
      message: res.__("ACCOUNT_DELETED_SUCCESSFULLY"),
    });
  } catch (err) {
    logger.error(err);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: err,
    });
  }
};

export const updateDeviceId = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const { deviceToken, deviceType } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("USER_NOT_FOUND"),
      });
    }

    const changes = validateAndGetChanges(user, { deviceToken, deviceType });
    await createActivityLog("users", user._id, "UPDATE", changes, req.user._id);

    user.deviceToken = deviceToken;
    user.deviceType = deviceType;
    await user.save();

    res.json({
      status: true,
      message: res.__("DEVICE_ID_UPDATED_SUCCESSFULLY"),
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const logOut = async (req: any, res: any) => {
  try {
    res.clearCookie("refreshToken");

    const user = await User.findById(req.user._id);

    if(!user){
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("USER_NOT_FOUND"),
      });
    }

    user.deviceToken = "";
    await user.save();

    res.json({
      status: true,
      message: res.__("LOGOUT_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
}