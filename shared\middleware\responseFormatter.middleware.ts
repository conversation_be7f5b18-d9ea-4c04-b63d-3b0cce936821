import { Request, Response, NextFunction } from "express";

/**
 * Response Formatter Middleware
 * Standardizes API responses across all microservices
 */
export const responseFormatter = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.log("REQUEST FROM", req.url, " :: ", req.method, " :: BODY PAYLOAD ::", req.body ,"\n\n")
  // Store the original res.json method
  const originalJson = res.json;
  const originalSend = res.send;

  // Current API version - can be fetched from config or package.json
  const apiVersion = "1.0.0";

  // Override the res.json method
  res.json = function (body: any): Response {
    // Only format if not already formatted
    if (body && !body?.hasOwnProperty("version")) {
      // Check if this is an error response
      const isSuccess = res.statusCode < 400;

      const bodyData = body?.data;
      let iBody = {
        ...body,
      };
      delete iBody.status;
      delete iBody.message;
      delete iBody.data;
      // Format the response
      const formattedResponse = {
        version: apiVersion,
        statusCode: res.statusCode,
        isSuccess: isSuccess,
        data: isSuccess
          ? Array.isArray(bodyData) ? bodyData : { ...iBody, ...bodyData }
          : iBody
            ? { ...iBody, ...bodyData }
            : null,
        message: body.message || (isSuccess ? "Success" : "Error"),
      };

      // For error responses, move error details to data
      if (!isSuccess && typeof body === "object") {
        // Remove message from data to avoid duplication
        const { message, status, ...restBody } = body;
        if (Object.keys(restBody).length > 0) {
          formattedResponse.data = restBody;
        }
      }

      return originalJson.call(this, formattedResponse);
    }

    return originalJson.call(this, body);
  };

  // Override the res.send method to handle non-json responses
  res.send = function (body: any): Response {
    // Only intercept JSON responses, let others pass through
    if (body && typeof body === "object") {
      return res.json(body);
    }
    return originalSend.call(this, body);
  };

  next();
};

/**
 * Explicit response formatter function that can be used directly in controllers
 * This is helpful when you need more control over the response format
 */
export const formatResponse = (
  data: any = null,
  message: string = "Success",
  statusCode: number = 200,
  version: string = "1.0.0"
) => {
  return {
    version,
    statusCode,
    isSuccess: statusCode < 400,
    data,
    message,
  };
};

export default responseFormatter;
