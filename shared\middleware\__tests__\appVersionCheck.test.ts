/**
 * App Version Check Middleware Tests
 * 
 * Test file demonstrating the middleware functionality
 * Run with: npm test or jest
 */

import { Request, Response, NextFunction } from 'express';
import { checkAppVersion } from '../appVersionCheck.middleware';

// Mock global config
const mockConfig = {
  MIN_APP_VERSION: "1.0.0",
  MAX_APP_VERSION: "1.0.2",
  MIN_IOS_VERSION: "1.0.0",
  MAX_IOS_VERSION: "1.0.2"
};

// Mock logger to prevent console output during tests
jest.mock('../../services/logger.service', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

describe('App Version Check Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction;

  beforeEach(() => {
    // Set up global config
    (global as any).config = mockConfig;

    // Reset mocks
    mockRequest = {
      headers: {}
    };
    
    mockResponse = {
      setHeader: jest.fn()
    };
    
    nextFunction = jest.fn();
  });

  describe('Android Version Checks', () => {
    beforeEach(() => {
      mockRequest.headers = {
        'device-type': 'android'
      };
    });

    test('should return Upgrade-Required for version below minimum', () => {
      mockRequest.headers!['app-version'] = '0.9.0';

      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'Upgrade-Required');
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should return Upgrade for version below maximum', () => {
      mockRequest.headers!['app-version'] = '1.0.1';

      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'Upgrade');
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should return OK for current version', () => {
      mockRequest.headers!['app-version'] = '1.0.2';

      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'OK');
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should return OK for version above maximum', () => {
      mockRequest.headers!['app-version'] = '1.0.3';

      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'OK');
      expect(nextFunction).toHaveBeenCalled();
    });
  });

  describe('iOS Version Checks', () => {
    beforeEach(() => {
      mockRequest.headers = {
        'device-type': 'ios'
      };
    });

    test('should return Upgrade-Required for version below minimum', () => {
      mockRequest.headers!['app-version'] = '0.9.0';
      
      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);
      
      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'Upgrade-Required');
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should return Upgrade for version below maximum', () => {
      mockRequest.headers!['app-version'] = '1.0.1';

      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'Upgrade');
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should return OK for current version', () => {
      mockRequest.headers!['app-version'] = '1.0.2';

      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'OK');
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should return OK for version above maximum', () => {
      mockRequest.headers!['app-version'] = '1.0.3';

      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'OK');
      expect(nextFunction).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    test('should skip check when no headers provided', () => {
      mockRequest.headers = {};
      
      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);
      
      expect(mockResponse.setHeader).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should skip check when app-version header missing', () => {
      mockRequest.headers = {
        'device-type': 'android'
      };
      
      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);
      
      expect(mockResponse.setHeader).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should skip check when device-type header missing', () => {
      mockRequest.headers = {
        'app-version': '1'
      };
      
      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);
      
      expect(mockResponse.setHeader).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should handle invalid Android version format', () => {
      mockRequest.headers = {
        'app-version': 'invalid',
        'device-type': 'android'
      };
      
      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);
      
      expect(mockResponse.setHeader).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should handle invalid iOS version format', () => {
      mockRequest.headers = {
        'app-version': 'invalid',
        'device-type': 'ios'
      };
      
      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);
      
      expect(mockResponse.setHeader).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should handle unknown device type', () => {
      mockRequest.headers = {
        'app-version': '1.0.0',
        'device-type': 'unknown'
      };
      
      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);
      
      expect(mockResponse.setHeader).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalled();
    });
  });

  describe('Case Insensitive Device Type', () => {
    test('should handle uppercase Android device type', () => {
      mockRequest.headers = {
        'app-version': '1.0.2',
        'device-type': 'ANDROID'
      };

      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'OK');
      expect(nextFunction).toHaveBeenCalled();
    });

    test('should handle mixed case iOS device type', () => {
      mockRequest.headers = {
        'app-version': '1.0.2',
        'device-type': 'iOS'
      };

      checkAppVersion(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('App-Version', 'OK');
      expect(nextFunction).toHaveBeenCalled();
    });
  });
});

/**
 * Example Usage in Real Application
 */
export const exampleUsage = {
  // Example client request headers
  androidRequest: {
    'App-Version': '3',
    'Device-Type': 'android',
    'User-Agent': 'SeaEscape/3.0 (Android 12; SM-G991B)'
  },
  
  iosRequest: {
    'App-Version': '1.1.0',
    'Device-Type': 'ios',
    'User-Agent': 'SeaEscape/1.1.0 (iPhone; iOS 15.0; Scale/3.00)'
  },
  
  // Example server responses
  responses: {
    upgradeRequired: { 'App-Version': 'Upgrade-Required' },
    upgrade: { 'App-Version': 'Upgrade' },
    ok: { 'App-Version': 'OK' }
  }
};
