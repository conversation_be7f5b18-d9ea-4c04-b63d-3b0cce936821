import { celebrate, Joi, Segments } from "celebrate";
import mongoose from "mongoose";

// Custom validator for MongoDB ObjectId
const objectIdValidator = (value: string, helpers: any) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error("any.invalid");
  }
  return value;
};

// Create affiliate validator
export const createAffiliateValidator = celebrate({
  [Segments.BODY]: Joi.object({
    name: Joi.string().required().messages({
      "any.required": "VALIDATION_NAME_REQUIRED",
      "string.empty": "VALIDATION_NAME_REQUIRED",
    }),
    email: Joi.string().email().required().messages({
      "any.required": "VALIDATION_EMAIL_REQUIRED",
      "string.empty": "VALIDATION_EMAIL_REQUIRED",
      "string.email": "VALIDATION_EMAIL_INVALID",
    }),
    phoneNo: Joi.string().required().messages({
      "any.required": "VALIDATION_PHONE_REQUIRED",
      "string.empty": "VALIDATION_PHONE_REQUIRED",
    }),
    accountHolderName: Joi.string().required().messages({
      "any.required": "VALIDATION_ACCOUNT_HOLDER_REQUIRED",
      "string.empty": "VALIDATION_ACCOUNT_HOLDER_REQUIRED",
    }),
    accountNumber: Joi.string().required().messages({
      "any.required": "VALIDATION_ACCOUNT_NUMBER_REQUIRED",
      "string.empty": "VALIDATION_ACCOUNT_NUMBER_REQUIRED",
    }),
    bankName: Joi.string().required().messages({
      "any.required": "VALIDATION_BANK_NAME_REQUIRED",
      "string.empty": "VALIDATION_BANK_NAME_REQUIRED",
    }),
    routingNumber: Joi.string().required().messages({
      "any.required": "VALIDATION_ROUTING_NUMBER_REQUIRED",
      "string.empty": "VALIDATION_ROUTING_NUMBER_REQUIRED",
    }),
    paypalEmail: Joi.string().email().optional().allow("").messages({
      "string.email": "VALIDATION_PAYPAL_EMAIL_INVALID",
    }),
  }),
});

// Update affiliate validator
export const updateAffiliateValidator = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.string().custom(objectIdValidator).required().messages({
      "any.required": "AFFILIATE.INVALID_ID",
      "any.invalid": "AFFILIATE.INVALID_ID",
    }),
  }),
  [Segments.BODY]: Joi.object({
    name: Joi.string().optional().messages({
      "string.empty": "VALIDATION_NAME_REQUIRED",
    }),
    email: Joi.string().email().optional().messages({
      "string.empty": "VALIDATION_EMAIL_REQUIRED",
      "string.email": "VALIDATION_EMAIL_INVALID",
    }),
    phoneNo: Joi.string().optional().messages({
      "string.empty": "VALIDATION_PHONE_REQUIRED",
    }),
    accountHolderName: Joi.string().optional().messages({
      "string.empty": "VALIDATION_ACCOUNT_HOLDER_REQUIRED",
    }),
    accountNumber: Joi.string().optional().messages({
      "string.empty": "VALIDATION_ACCOUNT_NUMBER_REQUIRED",
    }),
    bankName: Joi.string().optional().messages({
      "string.empty": "VALIDATION_BANK_NAME_REQUIRED",
    }),
    routingNumber: Joi.string().optional().messages({
      "string.empty": "VALIDATION_ROUTING_NUMBER_REQUIRED",
    }),
    paypalEmail: Joi.string().email().optional().allow("").messages({
      "string.email": "VALIDATION_PAYPAL_EMAIL_INVALID",
    }),
  }),
});

// Get affiliate by ID validator
export const getAffiliateValidator = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.string().custom(objectIdValidator).required().messages({
      "any.required": "AFFILIATE_INVALID_ID",
      "any.invalid": "AFFILIATE_INVALID_ID",
    }),
  }),
});

// Delete affiliate validator
export const deleteAffiliateValidator = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.string().custom(objectIdValidator).required().messages({
      "any.required": "AFFILIATE_INVALID_ID",
      "any.invalid": "AFFILIATE_INVALID_ID",
    }),
  }),
});

// Approve affiliate validator
export const approveAffiliateValidator = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.string().custom(objectIdValidator).required().messages({
      "any.required": "AFFILIATE_INVALID_ID",
      "any.invalid": "AFFILIATE_INVALID_ID",
    }),
  }),
});

// Reject affiliate validator
export const rejectAffiliateValidator = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.string().custom(objectIdValidator).required().messages({
      "any.required": "AFFILIATE_INVALID_ID",
      "any.invalid": "AFFILIATE_INVALID_ID",
    }),
  }),
  [Segments.BODY]: Joi.object({
    reason: Joi.string().required().messages({
      "any.required": "VALIDATION_REASON_REQUIRED",
      "string.empty": "VALIDATION_REASON_REQUIRED",
    }),
  }),
});

// Get affiliate boat details validator
export const getAffiliateBoatDetailsValidator = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.string().custom(objectIdValidator).required().messages({
      "any.required": "AFFILIATE_INVALID_ID",
      "any.invalid": "AFFILIATE_INVALID_ID",
    }),
    boatId: Joi.string().custom(objectIdValidator).required().messages({
      "any.required": "BOAT_INVALID_ID",
      "any.invalid": "BOAT_INVALID_ID",
    }),
  }),
});
