import { Types } from "mongoose";
import File, { FileType } from "../../../shared/models/Files";
import { getRelativePath } from "../../../shared/middleware/fileUpload.middleware";

export const saveUploadedImage = async (
  file: any,
  userId: string,
): Promise<Types.ObjectId> => {
  const relativePath = getRelativePath(file.path);

  const newFile = (await File.create({
    name: file.filename,
    size: file.size,
    fileType: file.mimetype,
    ext: file.originalname.split(".").pop(),
    location: relativePath,
    type: FileType.IMAGE,
    ownerId: userId,
  })) as any;

  return newFile._id;
};
