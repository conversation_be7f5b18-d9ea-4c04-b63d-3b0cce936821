import { StatusCodes } from "http-status-codes";
import <PERSON><PERSON> from "joi";
import mongoose from "mongoose";

// Validator for boatId parameter
export const idParamValidator = (req: any, res: any, next: any) => {
  const schema = Joi.object({
    boatId: Joi.string()
      .required()
      .custom((value, helpers) => {
        if (!mongoose.Types.ObjectId.isValid(value)) {
          return helpers.error("any.invalid");
        }
        return value;
      })
      .messages({
        "any.required": "WISHLIST_BOAT_ID_REQUIRED",
        "any.invalid": "WISHLIST_INVALID_BOAT_ID",
      }),
  });

  const { error } = schema.validate({ boatId: req.params.boatId });
  if (error) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: res.__(error.message),
    });
  }

  return next();
};

// Validator for add to wishlist body
export const addToWishlistValidator = (req: any, res: any, next: any) => {
  const schema = Joi.object({
    boatId: Joi.string()
      .required()
      .custom((value, helpers) => {
        if (!mongoose.Types.ObjectId.isValid(value)) {
          return helpers.error("any.invalid");
        }
        return value;
      })
      .messages({
        "any.required": "WISHLIST_BOAT_ID_REQUIRED",
        "any.invalid": "WISHLIST_INVALID_BOAT_ID",
      }),
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: res.__(error.message),
    });
  }

  return next();
};
