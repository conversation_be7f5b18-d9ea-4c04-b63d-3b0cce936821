const fs = require('fs');
const path = require('path');

// All microservices to validate
const MICROSERVICES = [
  'auth-ms',
  'user-ms',
  'boat-ms',
  'booking-ms',
  'wishlist-ms',
  'affiliate-ms',
  'notification-ms',
  'newsletter-ms',
  'faq-ms',
  'card-ms',
  'contact-us-ms',
  'privacy-policy-ms',
  'terms-condition-ms',
  'about-us-ms',
  'social-media-ms',
  'payment-ms',
  'wallet-ms',
  'reviews-ms',
  'chat-ms',
  'changelogs-ms',
  'mail-ms'
];

const LOCALES = ['en', 'es', 'de'];

function validateTranslationCompleteness() {
  console.log('🔍 VALIDATING TRANSLATION COMPLETENESS ACROSS ALL MICROSERVICES\n');
  
  let totalIssues = 0;
  let totalServices = 0;
  let completeServices = 0;
  
  // Include shared locales
  const allServices = ['shared', ...MICROSERVICES];
  
  allServices.forEach(service => {
    console.log(`📁 Validating ${service}...`);
    totalServices++;
    
    const localeFiles = {};
    const allKeys = new Set();
    let serviceHasIssues = false;
    
    // Read all locale files for this service
    LOCALES.forEach(locale => {
      const filePath = service === 'shared' 
        ? path.join(__dirname, 'shared', 'locales', `${locale}.json`)
        : path.join(__dirname, service, 'src', 'locales', `${locale}.json`);
      
      if (fs.existsSync(filePath)) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          const messages = JSON.parse(content);
          localeFiles[locale] = messages;
          
          // Collect all unique keys
          Object.keys(messages).forEach(key => allKeys.add(key));
          
          console.log(`  ✅ ${locale}.json: ${Object.keys(messages).length} messages`);
        } catch (error) {
          console.log(`  ❌ Error reading ${filePath}: ${error.message}`);
          serviceHasIssues = true;
          totalIssues++;
        }
      } else {
        console.log(`  ❌ ${locale}.json not found`);
        serviceHasIssues = true;
        totalIssues++;
      }
    });
    
    // Check for missing translations
    const missingTranslations = {};
    let serviceMissingCount = 0;
    
    LOCALES.forEach(locale => {
      missingTranslations[locale] = [];
      allKeys.forEach(key => {
        if (!localeFiles[locale] || !localeFiles[locale][key]) {
          missingTranslations[locale].push(key);
          serviceMissingCount++;
        }
      });
    });
    
    // Report missing translations
    LOCALES.forEach(locale => {
      if (missingTranslations[locale].length > 0) {
        serviceHasIssues = true;
        totalIssues += missingTranslations[locale].length;
        console.log(`  ❌ ${locale}.json missing ${missingTranslations[locale].length} translations:`);
        missingTranslations[locale].forEach(key => {
          console.log(`    - ${key}`);
        });
      }
    });
    
    // Check for consistency (all locales should have same number of keys)
    const keyCounts = LOCALES.map(locale => 
      localeFiles[locale] ? Object.keys(localeFiles[locale]).length : 0
    );
    
    const maxKeys = Math.max(...keyCounts);
    const minKeys = Math.min(...keyCounts);
    
    if (maxKeys !== minKeys) {
      serviceHasIssues = true;
      console.log(`  ⚠️  Inconsistent key counts: EN=${keyCounts[0]}, ES=${keyCounts[1]}, DE=${keyCounts[2]}`);
    }
    
    if (!serviceHasIssues) {
      completeServices++;
      console.log(`  ✅ ${service} - Perfect! All translations complete and consistent`);
    }
    
    console.log(''); // Empty line for readability
  });
  
  return {
    totalServices,
    completeServices,
    totalIssues
  };
}

function generateValidationReport(results) {
  console.log('📊 VALIDATION SUMMARY');
  console.log('====================');
  console.log(`Total microservices checked: ${results.totalServices}`);
  console.log(`Services with complete translations: ${results.completeServices}`);
  console.log(`Services with issues: ${results.totalServices - results.completeServices}`);
  console.log(`Total issues found: ${results.totalIssues}`);
  
  const completionRate = (results.completeServices / results.totalServices * 100).toFixed(1);
  console.log(`\n🎯 Translation Completion Rate: ${completionRate}%`);
  
  if (results.totalIssues === 0) {
    console.log('\n🎉 PERFECT! All translations are complete and consistent!');
    console.log('✅ No missing translations found');
    console.log('✅ All JSON files are valid');
    console.log('✅ All microservices have consistent translation coverage');
    console.log('\n🚀 Your localization system is production-ready!');
  } else {
    console.log(`\n⚠️  Found ${results.totalIssues} issues that need attention.`);
    console.log('Please review the detailed output above for specific problems.');
  }
}

// Main execution
try {
  const results = validateTranslationCompleteness();
  generateValidationReport(results);
} catch (error) {
  console.error('❌ Validation Error:', error.message);
  process.exit(1);
}
