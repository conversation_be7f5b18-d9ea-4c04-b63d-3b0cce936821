import express from "express";
import cors from "cors";
import * as config from "../shared/config.json";
import i18n from "./src/services/i18n.service";
import routes from "./src/routes/index.routes";
import connectDB from "../shared/db";
import { authenticateJWT } from "../shared/middleware/auth";
import logger from "../shared/services/logger.service";
import HandleErrorMessage from "../shared/middleware/validator";
import responseFormatter from "../shared/middleware/responseFormatter.middleware";
import { checkAppVersion } from "../shared/middleware/appVersionCheck.middleware";
const app = express();

require("dotenv").config();

const environment = process.env.NODE_ENV! || "dev";
const envConfig: any = JSON.parse(JSON.stringify(config))[environment];

try {
  connectDB();
  logger.info("Database connected successfully", {
    service: "social-media-ms",
  });
} catch (error: any) {
  logger.error(`Database connection failed: ${error.message}`, {
    service: "social-media-ms",
    error,
  });
  process.exit(1);
}

global.config = envConfig;

// Enable CORS for all origins
app.use(cors({ origin: "*" }));

app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(i18n.init);

// Apply response formatter middleware
app.use(responseFormatter);

// Apply app version check middleware (before authentication)
app.use(checkAppVersion);

// Regular API routes - require JWT authentication
app.use("/", authenticateJWT, routes);

// Celebrate error handling middleware
app.use(HandleErrorMessage);

app.listen(envConfig.services["social-media-ms"].PORT, () => {
  logger.info(
    `Social Media microservice is running on port ${envConfig.services["social-media-ms"].PORT}`,
    {
      service: "social-media-ms",
      port: envConfig.services["social-media-ms"].PORT,
    },
  );
});

export default app;
