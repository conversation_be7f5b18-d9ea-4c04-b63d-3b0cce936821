{"SOMETHING_WENT_WRONG": "Something went wrong", "PRIVACY_POLICY_VERSION_EXISTS": "Privacy policy with this version already exists", "PRIVACY_POLICY_CREATED_SUCCESS": "Privacy policy created successfully", "PRIVACY_POLICY_FETCH_SUCCESS": "Privacy policy retrieved successfully", "PRIVACY_POLICY_FETCH_ALL_SUCCESS": "All privacy policies retrieved successfully", "PRIVACY_POLICY_UPDATED_SUCCESS": "Privacy policy updated successfully", "PRIVACY_POLICY_DELETED_SUCCESS": "Privacy policy deleted successfully", "PRIVACY_POLICY_NOT_FOUND": "Privacy policy not found", "PRIVACY_POLICY_VERSION_REQUIRED": "Version is required", "PRIVACY_POLICY_VERSION_LENGTH": "Version must be between 1 and 20 characters", "PRIVACY_POLICY_TITLE_REQUIRED": "Title is required", "PRIVACY_POLICY_TITLE_LENGTH": "Title must be between 5 and 100 characters", "PRIVACY_POLICY_SECTIONS_REQUIRED": "At least one section is required", "PRIVACY_POLICY_SECTION_TITLE_REQUIRED": "Section title is required", "PRIVACY_POLICY_SECTION_TITLE_LENGTH": "Section title must be between 5 and 100 characters", "PRIVACY_POLICY_SECTION_CONTENT_REQUIRED": "Section content is required", "PRIVACY_POLICY_SECTION_CONTENT_LENGTH": "Section content must be at least 10 characters", "PRIVACY_POLICY_INVALID_ID": "Invalid privacy policy ID", "PRIVACY_POLICY_ID_REQUIRED": "Privacy policy ID is required", "FORBIDDEN": "You are not authorized to access this resource", "FAIL_TOKEN_EXPIRED": "Session is expired. Please log in again", "TOKEN_REQUIRED": "Authentication token is required", "USER_NOT_FOUND": "User not found", "ACCOUNT_INACTIVE": "Account is inactive or deleted. Please contact support for assistance", "ACCESS_DENIED": "Access denied. You don't have permission to perform this action", "INTERNAL_SERVER_ERROR": "An internal server error occurred. Please try again later", "INVALID_ID": "Invalid ID format provided", "INVALID_PAGINATION_PARAMS": "Invalid pagination parameters", "INVALID_FILTER_PARAMS": "Invalid filter parameters"}