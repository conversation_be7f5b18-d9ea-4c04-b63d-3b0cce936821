import mongoose, { Schema, Document } from "mongoose";

export enum ReviewStatus {
  ACTIVE = "Active",
  INACTIVE = "Inactive",
  DELETED = "Deleted",
}

export interface IReview extends Document {
  boatId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  rating: number;
  comment: string;
  status: ReviewStatus;
  createdAt: Date;
  updatedAt: Date;
}

const reviewSchema = new Schema(
  {
    boatId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Boat",
    },
    userId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Users",
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
    },
    comment: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(ReviewStatus),
      default: ReviewStatus.ACTIVE,
    },
  },
  {
    timestamps: true,
  },
);

export default mongoose.model<IReview>("Reviews", reviewSchema);
