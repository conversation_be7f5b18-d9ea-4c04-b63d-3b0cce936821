#!/bin/bash

# Set default environment to dev if not provided
ENV=${1:-dev}

echo "Starting Sea Escape Microservices with PM2 in $ENV environment..."
echo

# Stop and delete any existing PM2 processes to avoid conflicts
pm2 delete all

echo
echo "Starting microservices..."
echo

# Start API service first to ensure proper port allocation
pm2 start ecosystem.config.js --only api --env $ENV
sleep 2

# Start all other microservices
pm2 start ecosystem.config.js --only auth-ms,user-ms,boat-ms,booking-ms,wishlist-ms,affiliate-ms,notification-ms,newsletter-ms,faq-ms,card-ms,contact-us-ms,privacy-policy-ms,terms-condition-ms,about-us-ms,social-media-ms,payment-ms,wallet-ms,reviews-ms --env $ENV

echo
echo "All microservices started successfully with PM2!"
echo "Use 'npm run pm2:logs' to view logs"
echo "Use 'npm run pm2:status' to check status"
echo "Use 'npm run pm2:monitor' to monitor in real-time"
echo


