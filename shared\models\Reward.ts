import mongoose, { Schema, Document } from "mongoose";

export enum RewardStatus {
  ACTIVE = "active",
  DELETED = "deleted"
}

export interface IReward extends Document {
  userId: mongoose.Schema.Types.ObjectId;
  points: number;
  pointsHistory: {
    points: number;
    type: string;
    bookingId?: mongoose.Schema.Types.ObjectId;
    reviewId?: mongoose.Schema.Types.ObjectId;
    description: string;
    createdAt: Date;
  }[];
  welcomeDiscounts: {
    used: boolean;
    bookingId?: mongoose.Schema.Types.ObjectId;
    usedAt?: Date;
  }[];
  pointsExpiryDate: Date;
  createdBy: mongoose.Schema.Types.ObjectId;
  updatedBy: mongoose.Schema.Types.ObjectId;
  status: RewardStatus;
}

export enum RewardType {
  EARNED = "earned",
  REDEEMED = "redeemed",
  EXPIRED = "expired"
}

const rewardSchema = new Schema(
  {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', required: true, unique: true },
    points: { type: Number, default: 0 },
    pointsHistory: [{
      points: { type: Number, required: true },
      type: { 
        type: String,
        enum: Object.values(RewardType),
        required: true 
      },
      bookingId: { type: Schema.Types.ObjectId, ref: 'Bookings' },
      reviewId: { type: Schema.Types.ObjectId, ref: 'Reviews' },
      description: { type: String, required: true },
      createdAt: { type: Date, default: Date.now }
    }],
    welcomeDiscounts: [{
      used: { type: Boolean, default: false },
      bookingId: { type: Schema.Types.ObjectId, ref: 'Bookings' },
      usedAt: { type: Date }
    }],
    pointsExpiryDate: { 
      type: Date, 
      default: () => {
        const currentYear = new Date().getFullYear();
        return new Date(currentYear, 11, 31, 23, 59, 59); // December 31st of current year
      }
    },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', required: true, unique: true },
    updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', required: true, unique: true },
    status: { type: String, enum: Object.values(RewardStatus), default: RewardStatus.ACTIVE }
  },
  {
    timestamps: true,
  }
);

// Index for faster queries
rewardSchema.index({ userId: 1 });
rewardSchema.index({ pointsExpiryDate: 1 });

export default mongoose.model<IReward>("Rewards", rewardSchema); 