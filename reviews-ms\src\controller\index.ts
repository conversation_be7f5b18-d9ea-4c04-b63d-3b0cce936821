import Review, { ReviewStatus } from "../models/Review";
import { StatusCodes } from "http-status-codes";
import logger from "../../../shared/services/logger.service";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";
import mongoose from "mongoose";
import { addPoints } from "../../../shared/services/reward.service";
import { RewardType } from "../../../shared/models/Reward";

// Add a new review
export const addReview = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { boatId, rating, comment } = req.body;

    // Check if user has already reviewed this boat
    const existingReview = await Review.findOne({ userId, boatId });
    if (existingReview) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("REVIEW_ALREADY_EXISTS"),
      });
    }

    // Create new review
    const review = await Review.create({
      boatId,
      userId,
      rating,
      comment,
    });

    const changes = validateAndGetChanges({}, JSON.parse(JSON.stringify(review)));
    // Create activity log
    await createActivityLog("reviews", review._id, "CREATE", changes, userId);

    // Add reward points
    await addPoints(
      userId,
      5, // 5 points for leaving a review
      RewardType.EARNED,
      "Points earned for leaving a review",
      undefined,
      review?._id?.toString() || ""
    );

    res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("REVIEW_ADDED_SUCCESSFULLY"),
      data: review,
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get reviews with optional filtering
export const getReviews = async (req: any, res: any): Promise<any> => {
  try {
    const { boatId, userId } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const query: any = {
      status: ReviewStatus.ACTIVE,
    };

    // Add filters if provided
    if (boatId) {
      query.boatId = new mongoose.Types.ObjectId(boatId);
    }
    if (userId) {
      query.userId = new mongoose.Types.ObjectId(userId);
    }

    // Count total reviews matching the query
    const totalReviews = await Review.countDocuments(query);
    const totalPages = Math.ceil(totalReviews / limit);

    // Get reviews with pagination
    const reviews = await Review.find(query)
      .skip((page - 1) * limit)
      .limit(limit)
      .populate("userId", "username avatar")
      .sort({ createdAt: -1 });

    res.json({
      status: true,
      data: {
        reviews,
        pagination: {
          page,
          size: limit,
          totalCount: totalReviews,
          totalPages: totalPages,
        },
      },
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Delete a review
export const deleteReview = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { id } = req.params;

    // Find the review
    const review = await Review.findById(id);

    if (!review) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("REVIEW_NOT_FOUND"),
      });
    }

    // Check if the user is the owner of the review
    if (review.userId.toString() !== userId.toString()) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("ACCESS_DENIED"),
      });
    }

    const reviewData = { ...review };
    // Delete the review
    await Review.findByIdAndUpdate(id, { status: ReviewStatus.DELETED });

    const changes = validateAndGetChanges(reviewData, {
      ...review,
      status: ReviewStatus.DELETED,
    });
    // Create activity log
    await createActivityLog("reviews", review._id, "DELETE", changes, userId);

    res.json({
      status: true,
      message: res.__("REVIEW_DELETED_SUCCESSFULLY"),
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
