import express from "express";
import cors from "cors";
import * as config from "../shared/config.json";
import * as proxy from "http-proxy-middleware";
import connectDB from "../shared/db";
import i18n from "../shared/services/i18n.service";
import path from "path";
import swaggerUi from "swagger-ui-express";
import { Server } from "socket.io";
import { socketHandler } from "../chat-ms/src/socket";
import http from "http";

require("dotenv").config();

const app = express();

const router = express.Router();

connectDB();

const environment = process.env.NODE_ENV! || "dev";

const envConfig: any = JSON.parse(JSON.stringify(config))[environment];

global.config = envConfig;

// Enable CORS for all origins
app.use(cors({ origin: "*" }));

app.use(i18n.init);

// Load Swagger documentation with custom CSS and JS for dynamic buttons
const swaggerDocument = require("./swagger.json");
const swaggerOptions = {
  customCss: `
    .dynamic-add-btn {
      background: #27ae60;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 5px;
      cursor: pointer;
      margin: 5px;
      font-size: 12px;
      font-weight: bold;
    }
    .dynamic-add-btn:hover {
      background: #219a52;
      transform: scale(1.05);
    }
    .dynamic-remove-btn {
      background: #e74c3c;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 3px;
      cursor: pointer;
      margin-left: 10px;
      font-size: 11px;
    }
    .dynamic-remove-btn:hover {
      background: #c0392b;
    }
    .dynamic-field-group {
      border: 1px solid #e1e5ea;
      border-radius: 4px;
      padding: 10px;
      margin: 5px 0;
      background: #f7f7f7;
      position: relative;
    }
    .dynamic-field-header {
      font-weight: bold;
      margin-bottom: 8px;
      color: #3b4151;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .dynamic-buttons-container {
      text-align: center;
      margin: 10px 0;
      padding: 10px;
      background: #f0f0f0;
      border-radius: 4px;
    }
  `,
  customJs: `
    let facilityCount = 2; // Start from 2 since we have [0] and [1] by default
    let placeCount = 2; // Start from 2 since we have [0] and [1] by default

    // Wait for Swagger UI to fully load and monitor for changes
    function initializeButtons() {
      // Use MutationObserver to watch for DOM changes
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            // Check if we're in the boat endpoint and try-it mode is active
            const boatEndpoint = document.querySelector('[data-path="/boat"][data-method="post"]');
            if (boatEndpoint && boatEndpoint.querySelector('.try-out__btn[disabled]')) {
              // Try-it mode is active, add buttons
              setTimeout(() => {
                addFacilityButtons();
                addPlaceButtons();
              }, 100);
            }
          }
        });
      });

      // Start observing
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      // Also try to add buttons immediately if already in try-it mode
      setTimeout(() => {
        const boatEndpoint = document.querySelector('[data-path="/boat"][data-method="post"]');
        if (boatEndpoint && boatEndpoint.querySelector('.try-out__btn[disabled]')) {
          addFacilityButtons();
          addPlaceButtons();
        }
      }, 2000);
    }

    // Initialize when page loads
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeButtons);
    } else {
      initializeButtons();
    }

    function addFacilityButtons() {
      // Check if buttons already exist
      if (document.querySelector('.facility-buttons-container')) return;

      // Find the last facility-related row by looking for specific text content
      let lastFacilityRow = null;
      const allRows = document.querySelectorAll('[data-path="/boat"][data-method="post"] tr');

      for (let i = allRows.length - 1; i >= 0; i--) {
        const row = allRows[i];
        const text = row.textContent || '';
        if (text.includes('facilities[1].images') ||
            text.includes('facilities[0].images') ||
            text.includes('Snorkeling Gear') ||
            text.includes('facilities[1]') ||
            text.includes('facilities[0]')) {
          lastFacilityRow = row;
          break;
        }
      }

      if (!lastFacilityRow) {
        console.log('No facility row found, trying again later...');
        return;
      }

      console.log('Adding facility buttons after row:', lastFacilityRow.textContent);

      const buttonContainer = document.createElement('tr');
      buttonContainer.className = 'facility-buttons-container';
      buttonContainer.innerHTML = \`
        <td colspan="2">
          <div class="dynamic-buttons-container">
            <strong>🔧 Facilities (Unlimited)</strong><br>
            <button class="dynamic-add-btn" onclick="addFacility()" type="button">➕ Add Facility</button>
            <small style="display: block; margin-top: 5px; color: #666;">Click to add more facilities (Jet Ski, Snorkeling, etc.)</small>
          </div>
        </td>
      \`;

      lastFacilityRow.parentNode.insertBefore(buttonContainer, lastFacilityRow.nextSibling);
    }

    function addPlaceButtons() {
      // Check if buttons already exist
      if (document.querySelector('.place-buttons-container')) return;

      // Find the last place-related row by looking for specific text content
      let lastPlaceRow = null;
      const allRows = document.querySelectorAll('[data-path="/boat"][data-method="post"] tr');

      for (let i = allRows.length - 1; i >= 0; i--) {
        const row = allRows[i];
        const text = row.textContent || '';
        if (text.includes('recommendedPlaces[1].images') ||
            text.includes('recommendedPlaces[0].images') ||
            text.includes('Sunset Point') ||
            text.includes('recommendedPlaces[1]') ||
            text.includes('recommendedPlaces[0]')) {
          lastPlaceRow = row;
          break;
        }
      }

      if (!lastPlaceRow) {
        console.log('No place row found, trying again later...');
        return;
      }

      console.log('Adding place buttons after row:', lastPlaceRow.textContent);

      const buttonContainer = document.createElement('tr');
      buttonContainer.className = 'place-buttons-container';
      buttonContainer.innerHTML = \`
        <td colspan="2">
          <div class="dynamic-buttons-container">
            <strong>📍 Recommended Places (Unlimited)</strong><br>
            <button class="dynamic-add-btn" onclick="addPlace()" type="button">➕ Add Place</button>
            <small style="display: block; margin-top: 5px; color: #666;">Click to add more recommended places</small>
          </div>
        </td>
      \`;

      lastPlaceRow.parentNode.insertBefore(buttonContainer, lastPlaceRow.nextSibling);
    }

    window.addFacility = function() {
      const container = document.querySelector('[data-path="/boat"][data-method="post"] tbody');
      if (!container) return;

      // Create facility name field
      const nameRow = document.createElement('tr');
      nameRow.className = \`dynamic-facility-\${facilityCount}\`;
      nameRow.innerHTML = \`
        <td class="parameters-col_name">
          <div class="parameter__name required">
            facilities[\${facilityCount}].name
            <span style="color: red;">*</span>
          </div>
          <div class="parameter__type">string</div>
        </td>
        <td class="parameters-col_description">
          <div class="dynamic-field-group">
            <div class="dynamic-field-header">
              <span>🔧 Facility \${facilityCount + 1} - Name</span>
              <button class="dynamic-remove-btn" onclick="removeFacility(\${facilityCount})" type="button">❌ Remove</button>
            </div>
            <input type="text" name="facilities[\${facilityCount}].name" placeholder="e.g., Water Sports Equipment, Fishing Gear" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" />
          </div>
        </td>
      \`;

      // Create facility price field
      const priceRow = document.createElement('tr');
      priceRow.className = \`dynamic-facility-\${facilityCount}\`;
      priceRow.innerHTML = \`
        <td class="parameters-col_name">
          <div class="parameter__name required">
            facilities[\${facilityCount}].price
            <span style="color: red;">*</span>
          </div>
          <div class="parameter__type">number</div>
        </td>
        <td class="parameters-col_description">
          <div class="dynamic-field-group">
            <div class="dynamic-field-header">
              <span>💰 Facility \${facilityCount + 1} - Price (USD)</span>
            </div>
            <input type="number" name="facilities[\${facilityCount}].price" placeholder="e.g., 150" min="0" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" />
          </div>
        </td>
      \`;

      // Create facility images field
      const imagesRow = document.createElement('tr');
      imagesRow.className = \`dynamic-facility-\${facilityCount}\`;
      imagesRow.innerHTML = \`
        <td class="parameters-col_name">
          <div class="parameter__name">facilities[\${facilityCount}].images</div>
          <div class="parameter__type">file[]</div>
        </td>
        <td class="parameters-col_description">
          <div class="dynamic-field-group">
            <div class="dynamic-field-header">
              <span>📷 Facility \${facilityCount + 1} - Images</span>
            </div>
            <input type="file" name="facilities[\${facilityCount}].images" multiple accept="image/*" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" />
            <small style="color: #666;">Upload multiple images for this facility</small>
          </div>
        </td>
      \`;

      // Find the button container and insert before it
      const buttonContainer = container.querySelector('.dynamic-buttons-container');
      const insertPoint = buttonContainer ? buttonContainer.closest('tr') : container.lastElementChild;

      container.insertBefore(nameRow, insertPoint);
      container.insertBefore(priceRow, insertPoint);
      container.insertBefore(imagesRow, insertPoint);

      facilityCount++;
    };

    window.addPlace = function() {
      const container = document.querySelector('[data-path="/boat"][data-method="post"] tbody');
      if (!container) return;

      // Create place name field
      const nameRow = document.createElement('tr');
      nameRow.className = \`dynamic-place-\${placeCount}\`;
      nameRow.innerHTML = \`
        <td class="parameters-col_name">
          <div class="parameter__name required">
            recommendedPlaces[\${placeCount}].name
            <span style="color: red;">*</span>
          </div>
          <div class="parameter__type">string</div>
        </td>
        <td class="parameters-col_description">
          <div class="dynamic-field-group">
            <div class="dynamic-field-header">
              <span>📍 Place \${placeCount + 1} - Name</span>
              <button class="dynamic-remove-btn" onclick="removePlace(\${placeCount})" type="button">❌ Remove</button>
            </div>
            <input type="text" name="recommendedPlaces[\${placeCount}].name" placeholder="e.g., Hidden Cove, Coral Reef" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" />
          </div>
        </td>
      \`;

      // Create place description field
      const descRow = document.createElement('tr');
      descRow.className = \`dynamic-place-\${placeCount}\`;
      descRow.innerHTML = \`
        <td class="parameters-col_name">
          <div class="parameter__name required">
            recommendedPlaces[\${placeCount}].description
            <span style="color: red;">*</span>
          </div>
          <div class="parameter__type">string</div>
        </td>
        <td class="parameters-col_description">
          <div class="dynamic-field-group">
            <div class="dynamic-field-header">
              <span>📝 Place \${placeCount + 1} - Description</span>
            </div>
            <textarea name="recommendedPlaces[\${placeCount}].description" placeholder="Describe this amazing place and what makes it special..." style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; resize: vertical;"></textarea>
          </div>
        </td>
      \`;

      // Create place images field
      const imagesRow = document.createElement('tr');
      imagesRow.className = \`dynamic-place-\${placeCount}\`;
      imagesRow.innerHTML = \`
        <td class="parameters-col_name">
          <div class="parameter__name">recommendedPlaces[\${placeCount}].images</div>
          <div class="parameter__type">file[]</div>
        </td>
        <td class="parameters-col_description">
          <div class="dynamic-field-group">
            <div class="dynamic-field-header">
              <span>📷 Place \${placeCount + 1} - Images</span>
            </div>
            <input type="file" name="recommendedPlaces[\${placeCount}].images" multiple accept="image/*" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" />
            <small style="color: #666;">Upload multiple images of this recommended place</small>
          </div>
        </td>
      \`;

      // Find the button container and insert before it
      const buttonContainer = container.querySelector('.dynamic-buttons-container');
      const insertPoint = buttonContainer ? buttonContainer.closest('tr') : container.lastElementChild;

      container.insertBefore(nameRow, insertPoint);
      container.insertBefore(descRow, insertPoint);
      container.insertBefore(imagesRow, insertPoint);

      placeCount++;
    };

    window.removeFacility = function(index) {
      const elements = document.querySelectorAll(\`.dynamic-facility-\${index}\`);
      elements.forEach(element => element.remove());
    };

    window.removePlace = function(index) {
      const elements = document.querySelectorAll(\`.dynamic-place-\${index}\`);
      elements.forEach(element => element.remove());
    };
  `,
};

app.use(
  "/api-docs",
  swaggerUi.serve,
  swaggerUi.setup(swaggerDocument, swaggerOptions)
);

router.use(
  "/uploads",
  express.static(path.resolve(__dirname, "../shared/uploads"))
);

// Serve dynamic boat creation form
router.use("/boat-ms", express.static(path.resolve(__dirname, "../boat-ms")));

router.use(
  "/v1/auth",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["auth-ms"].PORT}`,
    changeOrigin: true,
  })
);
router.use(
  "/v1/user",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["user-ms"].PORT}`,
    changeOrigin: true,
  })
);
router.use(
  "/v1/boat",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["boat-ms"].PORT}`,
    changeOrigin: true,
  })
);
router.use(
  "/v1/wishlist",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["wishlist-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/affiliate",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["affiliate-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/booking",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["booking-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/notifications",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["notification-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/newsletter",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["newsletter-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/faqs",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["faq-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/cards",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["card-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/contact-us",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["contact-us-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/privacy-policy",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["privacy-policy-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/terms-condition",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["terms-condition-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/about-us",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["about-us-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/social-media",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["social-media-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/wallet",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["wallet-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/payment",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["payment-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/reviews",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["reviews-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/changelogs",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["changelogs-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/mails",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["mail-ms"].PORT}`,
    changeOrigin: true,
  })
);

router.use(
  "/v1/chat",
  proxy.createProxyMiddleware({
    target: `http://127.0.0.1:${envConfig.services["chat-ms"].PORT}`,
    changeOrigin: true,
  })
);

app.use(router);

const server = http.createServer(app);

server.listen(envConfig.PORT, () => {
  console.log(`Server is running on port ${envConfig.PORT}`);
  console.log(
    `API Documentation available at http://localhost:${envConfig.PORT}/api-docs`
  );
});

const io = new Server(server, {
  cors: {
    origin: "*",
  },
});

// Initialize socket handlers
io.on("connection", (socket: any) => {
  console.log("New connection", socket.id);
  socketHandler(io, socket);
});

export default app;
