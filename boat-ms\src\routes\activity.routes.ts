import { Router } from "express";
import {
  createActivity,
  getAllActivities,
  getActivity,
  getActivityForBooking,
  updateActivity,
  deleteActivity,
  checkWishlistStatus,
  addActivityImages,
  removeActivityImages,
  addActivityVideos,
  removeActivityVideos,
} from "../controller/activity.controller";
import { idParamValidator } from "../validator/activity.validator";
import { uploadFiles, uploadMultipleFields } from "../../../shared/middleware/fileUpload.middleware";

const router = Router();

// Public routes
router.get("/", getAllActivities);
router.get("/wishlist-status/:id", idParamValidator, checkWishlistStatus);
router.get("/:id/booking", idParamValidator, getActivityForBooking);
router.get("/:id", idParamValidator, getActivity);

// User routes - users can create/update/delete their own activities
router.post("/", uploadMultipleFields("activities", [
  { name: "images" },
  { name: "videos" }
]), createActivity);
router.put(
  "/:id",
  idParamValidator,
  uploadMultipleFields("activities", [
    { name: "images" },
    { name: "videos" }
  ]),
  updateActivity
);
router.delete("/:id", idParamValidator, deleteActivity);

// Activity image management routes
router.post("/:id/images", idParamValidator, uploadFiles("activities", "images"), addActivityImages);
router.delete("/:id/images", idParamValidator, removeActivityImages);

// Activity video management routes
router.post("/:id/videos", idParamValidator, uploadFiles("activities", "videos"), addActivityVideos);
router.delete("/:id/videos", idParamValidator, removeActivityVideos);

export default router;
