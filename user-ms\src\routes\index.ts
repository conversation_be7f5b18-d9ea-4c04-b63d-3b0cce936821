import { Router } from "express";
import {
  changePassword,
  changeUserRole,
  deleteAccount,
  getAllUsers,
  getProfile,
  logOut,
  updateDeviceId,
  updateProfile,
} from "../controller";
import {
  changePasswordValidator,
  switchRoleValidator,
} from "../validator/user.validator";
import { uploadSingeFiles } from "../../../shared/middleware/fileUpload.middleware";
import documentRoutes from "./document.routes";
import { adminAccess } from "../../../shared/middleware/admin";
import { getUserRewards } from "../controller/reward.controller";
import { deleteAccountValidator } from "../validator/user.validator";
const routes = Router();

routes.get("/profile", getProfile);
routes.put("/profile", uploadSingeFiles("users", "avatar"), updateProfile);
routes.post("/change-password", changePasswordValidator, changePassword);
routes.put("/switch-role", switchRoleValidator, changeUserRole);
routes.get("/list", adminAccess, getAllUsers);
routes.delete("/delete", deleteAccountValidator, deleteAccount);
routes.put("/update-device-id", updateDeviceId);
routes.get("/rewards", getUserRewards);
routes.post("/logout", logOut);

// Mount document routes
routes.use("/documents", documentRoutes);

export default routes;
