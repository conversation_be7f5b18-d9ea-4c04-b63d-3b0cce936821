#!/bin/bash

# Start Sea Escape Microservices
# This script starts all microservices sequentially
# Usage: ./start-services.sh [environment]
# Example: ./start-services.sh development

# Set default environment if not provided
ENV=${1:-dev}
echo "Using environment: $ENV"

echo "Starting Sea Escape Microservices..."
echo "-----------------------------------"

# Start API Gateway first
echo "Starting API Gateway..."
NODE_ENV=$ENV npx ts-node --files ./api/index.ts &
sleep 3
echo "API Gateway started successfully."

# Start Core Services (Auth and User)
echo "Starting Auth Microservice..."
NODE_ENV=$ENV npx ts-node --files ./auth-ms/index.ts &
sleep 3
echo "Auth Microservice started successfully."

echo "Starting User Microservice..."
NODE_ENV=$ENV npx ts-node --files ./user-ms/index.ts &
sleep 3
echo "User Microservice started successfully."

# Start Business Logic Microservices
echo "Starting Boat Microservice..."
NODE_ENV=$ENV npx ts-node --files ./boat-ms/index.ts &
sleep 3
echo "Boat Microservice started successfully."

echo "Starting Booking Microservice..."
NODE_ENV=$ENV npx ts-node --files ./booking-ms/index.ts &
sleep 3
echo "Booking Microservice started successfully."

echo "Starting Payment Microservice..."
NODE_ENV=$ENV npx ts-node --files ./payment-ms/index.ts &
sleep 3
echo "Payment Microservice started successfully."

echo "Starting Wallet Microservice..."
NODE_ENV=$ENV npx ts-node --files ./wallet-ms/index.ts &
sleep 3
echo "Wallet Microservice started successfully."

# Start Supporting Microservices
echo "Starting Wishlist Microservice..."
NODE_ENV=$ENV npx ts-node --files ./wishlist-ms/index.ts &
sleep 3
echo "Wishlist Microservice started successfully."

echo "Starting Affiliate Microservice..."
NODE_ENV=$ENV npx ts-node --files ./affiliate-ms/index.ts &
sleep 3
echo "Affiliate Microservice started successfully."

echo "Starting Card Microservice..."
NODE_ENV=$ENV npx ts-node --files ./card-ms/index.ts &
sleep 3
echo "Card Microservice started successfully."

# Start Content Microservices
echo "Starting FAQ Microservice..."
NODE_ENV=$ENV npx ts-node --files ./faq-ms/index.ts &
sleep 3
echo "FAQ Microservice started successfully."

echo "Starting Contact Us Microservice..."
NODE_ENV=$ENV npx ts-node --files ./contact-us-ms/index.ts &
sleep 3
echo "Contact Us Microservice started successfully."

echo "Starting Privacy Policy Microservice..."
NODE_ENV=$ENV npx ts-node --files ./privacy-policy-ms/index.ts &
sleep 3
echo "Privacy Policy Microservice started successfully."

echo "Starting Terms & Conditions Microservice..."
NODE_ENV=$ENV npx ts-node --files ./terms-condition-ms/index.ts &
sleep 3
echo "Terms & Conditions Microservice started successfully."

echo "Starting About Us Microservice..."
NODE_ENV=$ENV npx ts-node --files ./about-us-ms/index.ts &
sleep 3
echo "About Us Microservice started successfully."

# Start Communication Microservices
echo "Starting Notification Microservice..."
NODE_ENV=$ENV npx ts-node --files ./notification-ms/index.ts &
sleep 3
echo "Notification Microservice started successfully."

echo "Starting Newsletter Microservice..."
NODE_ENV=$ENV npx ts-node --files ./newsletter-ms/index.ts &
sleep 3
echo "Newsletter Microservice started successfully."

echo "Starting Social Media Microservice..."
NODE_ENV=$ENV npx ts-node --files ./social-media-ms/index.ts &
sleep 3
echo "Social Media Microservice started successfully."

echo "Starting Reviews Microservice..."
NODE_ENV=$ENV npx ts-node --files ./reviews-ms/index.ts &
sleep 3
echo "Reviews Microservice started successfully."

echo "Starting Changelogs Microservice..."
NODE_ENV=$ENV npx ts-node --files ./changelogs-ms/index.ts &
sleep 3
echo "Changelogs Microservice started successfully."

echo "Starting Mail Microservice..."
NODE_ENV=$ENV npx ts-node --files ./mail-ms/index.ts &
sleep 3
echo "Mail Microservice started successfully."

echo "Starting Chat Microservice..."
NODE_ENV=$ENV npx ts-node --files ./chat-ms/index.ts &
sleep 3
echo "Chat Microservice started successfully."

echo ""
echo "All Sea Escape Microservices have been started with environment: $ENV"
echo "Check individual logs for any errors."
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user to press Ctrl+C
wait 