import express from "express";
import { creditWallet, debitWallet } from "../services/transaction.service";
import mongoose from "mongoose";
import { StatusCodes } from "http-status-codes";
import { TransactionSource } from "../models/Transaction";
import logger from "../../../shared/services/logger.service";

const router = express.Router();

// Credit a wallet - internal endpoint called by other services
router.post("/credit", async (req: any, res: any) => {
  try {
    const {
      userId,
      amount,
      source,
      sourceId,
      bookingId,
      description,
      paymentMethod,
      reference,
    } = req.body;

    if (!userId || !amount || !source) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Missing required fields",
        error: "UserId, amount, and source are required",
      });
    }

    // Validate source
    if (!Object.values(TransactionSource).includes(source)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Invalid transaction source",
        error: `Source must be one of: ${Object.values(TransactionSource).join(", ")}`,
      });
    }

    const result = await creditWallet(
      new mongoose.Types.ObjectId(userId),
      amount,
      source as TransactionSource,
      sourceId ? new mongoose.Types.ObjectId(sourceId) : undefined,
      bookingId ? new mongoose.Types.ObjectId(bookingId) : undefined,
      description,
      paymentMethod,
      reference,
    );

    if (result.success) {
      return res.status(StatusCodes.OK).json({
        success: true,
        message: "Wallet credited successfully",
        data: {
          wallet: result.wallet,
          transaction: result.transaction,
        },
      });
    } else {
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: "Failed to credit wallet",
        error: result.error,
      });
    }
  } catch (error) {
    logger.error("Error in internal credit wallet endpoint", {
      service: "wallet-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "An error occurred while processing the request",
      error,
    });
  }
});

// Debit a wallet - internal endpoint called by other services
router.post("/debit", async (req: any, res: any) => {
  try {
    const {
      userId,
      amount,
      source,
      sourceId,
      bookingId,
      description,
      paymentMethod,
      reference,
    } = req.body;

    if (!userId || !amount || !source) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Missing required fields",
        error: "UserId, amount, and source are required",
      });
    }

    // Validate source
    if (!Object.values(TransactionSource).includes(source)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Invalid transaction source",
        error: `Source must be one of: ${Object.values(TransactionSource).join(", ")}`,
      });
    }

    const result = await debitWallet(
      new mongoose.Types.ObjectId(userId),
      amount,
      source as TransactionSource,
      sourceId ? new mongoose.Types.ObjectId(sourceId) : undefined,
      bookingId ? new mongoose.Types.ObjectId(bookingId) : undefined,
      description,
      paymentMethod,
      reference,
    );

    if (result.success) {
      return res.status(StatusCodes.OK).json({
        success: true,
        message: "Wallet debited successfully",
        data: {
          wallet: result.wallet,
          transaction: result.transaction,
        },
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Failed to debit wallet",
        error: result.error,
      });
    }
  } catch (error) {
    logger.error("Error in internal debit wallet endpoint", {
      service: "wallet-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "An error occurred while processing the request",
      error,
    });
  }
});

export default router;
