// ============================================================================
// COMMENTED OUT: OneSignal Implementation (Legacy)
// ============================================================================
// import { Client, HTTPError } from "onesignal-node";

// //send push notification
// const sendPushNotification = async (
//   deviceId: any,
//   content: any,
//   data: any,
//   type: any,
//   header = "header",
// ) => {
//   const client = new Client(
//     global.config.ONESIGNAL_APP_ID,
//     global.config.ONESIGNAL_API_KEY,
//   );
//   if (deviceId && deviceId != "") {
//     let NotificationDetails: any;
//     if (type && type == "Verify") {
//       NotificationDetails = {
//         name: global.config.SIGNAL_APP_NAME,
//         sms_from: global.config.SIGNAL_SMS_FROM,
//         contents: {
//           en: content,
//         },
//         headings: { en: header },
//         include_phone_numbers: [deviceId],
//         data: { data: data, type: type },
//       };
//     } else {
//       NotificationDetails = {
//         contents: {
//           en: content,
//         },
//         headings: { en: header },
//         include_player_ids: typeof deviceId != "string" ? deviceId : [deviceId],
//         data: { data: data, type: type },
//       };
//     }

// ============================================================================
// NEW: Firebase Cloud Messaging Implementation
// ============================================================================
import * as admin from "firebase-admin";

// Initialize Firebase Admin SDK (singleton pattern)
let firebaseApp: any = null;

const initializeFirebase = () => {
  if (!firebaseApp) {
    try {
      // Check if Firebase is already initialized
      firebaseApp = admin.app();
    } catch (error) {
      // Validate Firebase configuration before initialization
      if (!global.config.FIREBASE_PROJECT_ID ||
          !global.config.FIREBASE_PRIVATE_KEY ||
          !global.config.FIREBASE_CLIENT_EMAIL) {
        throw new Error(
          "Firebase configuration is incomplete. Please ensure FIREBASE_PROJECT_ID, " +
          "FIREBASE_PRIVATE_KEY, and FIREBASE_CLIENT_EMAIL are set in your config."
        );
      }

      // Initialize Firebase if not already done
      const privateKey = global.config.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n');

      try {
        firebaseApp = admin.initializeApp({
          credential: admin.credential.cert({
            projectId: global.config.FIREBASE_PROJECT_ID,
            privateKey: privateKey,
            clientEmail: global.config.FIREBASE_CLIENT_EMAIL,
          }),
        });
        console.log("Firebase Admin SDK initialized successfully");
      } catch (initError) {
        console.error("Failed to initialize Firebase Admin SDK:", initError);
        throw new Error(`Firebase initialization failed: ${initError instanceof Error ? initError.message : 'Unknown error'}`);
      }
    }
  }
  return firebaseApp;
};

//send push notification using Firebase FCM
const sendPushNotification = async (
  fcmToken: any,
  content: any,
  data: any,
  type: any,
  header = "Sea Escape",
) => {
  try {
    // Initialize Firebase if not already done
    initializeFirebase();

    // Validate FCM token
    if (!fcmToken || fcmToken === "") {
      console.log("No FCM token provided, skipping push notification");
      return { success: false, error: "No FCM token provided" };
    }

    // Handle array of tokens or single token
    const tokens = Array.isArray(fcmToken) ? fcmToken : [fcmToken];

    // Filter out empty tokens
    const validTokens = tokens.filter(token => token && token.trim() !== "");

    if (validTokens.length === 0) {
      console.log("No valid FCM tokens found, skipping push notification");
      return { success: false, error: "No valid FCM tokens found" };
    }

    // Prepare notification payload
    const message: any = {
      notification: {
        title: header,
        body: content,
      },
      data: {
        type: type || "general",
        payload: JSON.stringify(data || {}),
        notificationId: data?.notificationId?.toString() || "",
        clickAction: "FLUTTER_NOTIFICATION_CLICK", // For Flutter apps
      },
      android: {
        notification: {
          title: header,
          body: content,
          clickAction: "FLUTTER_NOTIFICATION_CLICK",
          channelId: "sea_escape_notifications", // Custom notification channel
          priority: "high" as const,
          defaultSound: true,
        },
        data: {
          type: type || "general",
          payload: JSON.stringify(data || {}),
          notificationId: data?.notificationId?.toString() || "",
        },
      },
      apns: {
        payload: {
          aps: {
            alert: {
              title: header,
              body: content,
            },
            sound: "default",
            badge: 1,
            category: "sea_escape_notification",
          },
          customData: {
            type: type || "general",
            payload: JSON.stringify(data || {}),
            notificationId: data?.notificationId?.toString() || "",
          },
        },
      },
      tokens: validTokens,
    };

    // Send the notification
    const response = await admin.messaging().sendMulticast(message);

    console.log(`Firebase notification sent successfully: ${response.successCount}/${validTokens.length} messages delivered`);

    // Handle failed tokens (optional: you might want to remove invalid tokens from database)
    if (response.failureCount > 0) {
      const failedTokens: string[] = [];
      response.responses.forEach((resp: any, idx: number) => {
        if (!resp.success) {
          failedTokens.push(validTokens[idx]);
          console.error(`Failed to send to token ${validTokens[idx]}:`, resp.error);
        }
      });

      return {
        success: true,
        successCount: response.successCount,
        failureCount: response.failureCount,
        failedTokens: failedTokens,
        response: response,
      };
    }

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      response: response,
    };

  } catch (error) {
    console.error("Error sending Firebase push notification:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

// ============================================================================
// COMMENTED OUT: Remaining OneSignal Implementation (Legacy)
// ============================================================================
//     try {
//       const response = await client.createNotification(NotificationDetails);
//       console.log("response", response);
//     } catch (e: any) {
//       console.log("error", e);
//       if (e instanceof HTTPError) {
//         // When status code of HTTP response is not 2xx, HTTPError is thrown.
//         console.log(e.statusCode);
//         console.log(e.body);
//       }
//       return false;
//     }
//   }
//   return true;
// };

export default sendPushNotification;
