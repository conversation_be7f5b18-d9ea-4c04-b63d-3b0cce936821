#!/usr/bin/env node

/**
 * Swagger Parameter Verification Script
 * 
 * This script verifies that all query parameters are properly documented
 * in the Swagger JSON file for both boats and activities APIs.
 */

const fs = require('fs');
const path = require('path');

// Load swagger.json
const swaggerPath = path.join(__dirname, 'api', 'swagger.json');
let swagger;

try {
  swagger = JSON.parse(fs.readFileSync(swaggerPath, 'utf8'));
} catch (error) {
  console.error('❌ Error loading swagger.json:', error.message);
  process.exit(1);
}

// Expected parameters for each endpoint
const expectedParams = {
  boats: [
    'page', 'limit', 'type', 'minPrice', 'maxPrice', 'minCapacity', 'maxCapacity',
    'minCabins', 'maxCabins', 'minBaths', 'maxBaths', 'location', 'fuel', 
    'patron', 'license', 'status', 'fromDate', 'toDate', 'search', 'sortBy', 
    'sortOrder', 'lat', 'lng', 'radius'
  ],
  activities: [
    'page', 'limit', 'status', 'location', 'search', 'fromDate', 'toDate',
    'type', 'minPrice', 'maxPrice', 'minDuration', 'maxDuration', 'sortBy', 'sortOrder'
  ]
};

function verifyEndpointParameters(endpointPath, expectedParamList, endpointName) {
  console.log(`\n🔍 Verifying ${endpointName} API parameters...`);
  console.log(`📍 Endpoint: GET ${endpointPath}`);
  
  const endpoint = swagger.paths[endpointPath];
  if (!endpoint || !endpoint.get) {
    console.log(`❌ Endpoint not found: ${endpointPath}`);
    return false;
  }
  
  const parameters = endpoint.get.parameters || [];
  const documentedParams = parameters
    .filter(param => param.in === 'query')
    .map(param => param.name);
  
  console.log(`📋 Expected parameters (${expectedParamList.length}):`, expectedParamList.sort());
  console.log(`📄 Documented parameters (${documentedParams.length}):`, documentedParams.sort());
  
  // Check for missing parameters
  const missing = expectedParamList.filter(param => !documentedParams.includes(param));
  const extra = documentedParams.filter(param => !expectedParamList.includes(param));
  
  if (missing.length === 0 && extra.length === 0) {
    console.log(`✅ Perfect match! All ${expectedParamList.length} parameters are documented correctly.`);
    return true;
  }
  
  if (missing.length > 0) {
    console.log(`❌ Missing parameters (${missing.length}):`, missing);
  }
  
  if (extra.length > 0) {
    console.log(`⚠️  Extra parameters (${extra.length}):`, extra);
  }
  
  return missing.length === 0;
}

function verifyParameterDetails(endpointPath, paramName) {
  const endpoint = swagger.paths[endpointPath];
  const parameters = endpoint.get.parameters || [];
  const param = parameters.find(p => p.name === paramName && p.in === 'query');
  
  if (!param) {
    return { exists: false };
  }
  
  return {
    exists: true,
    type: param.schema?.type,
    format: param.schema?.format,
    description: param.description,
    enum: param.schema?.enum,
    default: param.schema?.default
  };
}

function generateParameterReport() {
  console.log('\n📊 DETAILED PARAMETER REPORT\n');
  console.log('=' .repeat(80));
  
  // Boats API Report
  console.log('\n🚤 BOATS API PARAMETERS:');
  console.log('-'.repeat(40));
  
  const boatsEndpoint = '/boat';
  const boatsParams = swagger.paths[boatsEndpoint]?.get?.parameters || [];
  
  boatsParams
    .filter(param => param.in === 'query')
    .sort((a, b) => a.name.localeCompare(b.name))
    .forEach(param => {
      console.log(`📌 ${param.name}`);
      console.log(`   Type: ${param.schema?.type || 'unknown'}`);
      console.log(`   Description: ${param.description || 'No description'}`);
      if (param.schema?.enum) {
        console.log(`   Values: ${param.schema.enum.join(', ')}`);
      }
      if (param.schema?.default !== undefined) {
        console.log(`   Default: ${param.schema.default}`);
      }
      console.log('');
    });
  
  // Activities API Report
  console.log('\n🏄 ACTIVITIES API PARAMETERS:');
  console.log('-'.repeat(40));
  
  const activitiesEndpoint = '/boat/activities';
  const activitiesParams = swagger.paths[activitiesEndpoint]?.get?.parameters || [];
  
  activitiesParams
    .filter(param => param.in === 'query')
    .sort((a, b) => a.name.localeCompare(b.name))
    .forEach(param => {
      console.log(`📌 ${param.name}`);
      console.log(`   Type: ${param.schema?.type || 'unknown'}`);
      console.log(`   Description: ${param.description || 'No description'}`);
      if (param.schema?.enum) {
        console.log(`   Values: ${param.schema.enum.join(', ')}`);
      }
      if (param.schema?.default !== undefined) {
        console.log(`   Default: ${param.schema.default}`);
      }
      console.log('');
    });
}

function verifyDateParameters() {
  console.log('\n📅 DATE PARAMETER VERIFICATION:');
  console.log('-'.repeat(40));
  
  const endpoints = [
    { path: '/boat', name: 'Boats' },
    { path: '/boat/activities', name: 'Activities' }
  ];
  
  endpoints.forEach(endpoint => {
    console.log(`\n${endpoint.name} API:`);
    
    const fromDate = verifyParameterDetails(endpoint.path, 'fromDate');
    const toDate = verifyParameterDetails(endpoint.path, 'toDate');
    
    console.log(`  fromDate: ${fromDate.exists ? '✅' : '❌'} ${fromDate.exists ? `(${fromDate.type}, ${fromDate.format || 'no format'})` : 'Missing'}`);
    console.log(`  toDate: ${toDate.exists ? '✅' : '❌'} ${toDate.exists ? `(${toDate.type}, ${toDate.format || 'no format'})` : 'Missing'}`);
    
    if (fromDate.exists && toDate.exists) {
      console.log(`  📝 fromDate description: ${fromDate.description}`);
      console.log(`  📝 toDate description: ${toDate.description}`);
    }
  });
}

function main() {
  console.log('🔍 SWAGGER PARAMETER VERIFICATION TOOL');
  console.log('=' .repeat(60));
  
  let allValid = true;
  
  // Verify boats API
  const boatsValid = verifyEndpointParameters('/boat', expectedParams.boats, 'Boats');
  allValid = allValid && boatsValid;
  
  // Verify activities API
  const activitiesValid = verifyEndpointParameters('/boat/activities', expectedParams.activities, 'Activities');
  allValid = allValid && activitiesValid;
  
  // Verify date parameters specifically
  verifyDateParameters();
  
  // Generate detailed report
  generateParameterReport();
  
  // Final summary
  console.log('\n🎯 VERIFICATION SUMMARY:');
  console.log('=' .repeat(40));
  
  if (allValid) {
    console.log('✅ All API parameters are properly documented!');
    console.log('🎉 Swagger documentation is complete and accurate.');
  } else {
    console.log('❌ Some parameters are missing from documentation.');
    console.log('⚠️  Please update the Swagger JSON file.');
  }
  
  console.log(`\n📊 Statistics:`);
  console.log(`   Boats API: ${expectedParams.boats.length} parameters expected`);
  console.log(`   Activities API: ${expectedParams.activities.length} parameters expected`);
  console.log(`   Date filtering: ${boatsValid && activitiesValid ? 'Implemented' : 'Incomplete'}`);
}

if (require.main === module) {
  main();
}

module.exports = { verifyEndpointParameters, verifyParameterDetails, generateParameterReport };
