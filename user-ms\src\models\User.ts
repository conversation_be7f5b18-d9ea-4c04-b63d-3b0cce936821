import mongoose, { Schema, Document } from "mongoose";

export interface IUser extends Document {
  avatar?: mongoose.Types.ObjectId; // File ID
  username: string;
  email: string;
  password: string;
  currency: string;
  language: string;
  otp: number | null;
  otpExpireTime: Date | null;
  role: string;
  currentRole: string;
  status: string;
  interestedIn: string[];
  deviceType: string;
  deviceToken: string; // FCM token for Firebase push notifications
  stripeCustomerId: string; // Stripe customer ID for payment processing
  merchantAccountId: string;
  deleteReason: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  User = "user",
  Admin = "admin",
}

export enum UserCurrentRole {
  User = "user",
  Owner = "owner",
}

export enum UserStatus {
  Active = "active",
  Inactive = "inactive",
  Pending = "pending",
  Deleted = "deleted",
}

export enum UserLanguage {
  English = "en",
  Spanish = "es",
  German = "de",
}
export enum UserInterestedIn {
  Boat = "Boat",
  JetSkis = "JetSkis",
  Activity = "Activity",
}

export enum UserCurrency {
  USD = "USD",
  GBP = "GBP",
  EUR = "EUR",
}

export enum CurrencyCode {
  USD = "$",
  GBP = "£",
  EUR = "€",
}

const userSchema = new Schema(
  {
    avatar: {
      type: Schema.Types.ObjectId,
      ref: "File",
    },
    username: {
      type: String,
      // required: true,
      trim: true,
    },
    email: {
      type: String,
      // required: true,
      unique: true,
      trim: true,
    },
    password: {
      type: String,
      // required: true,
    },
    currency: {
      type: String,
      enum: [...Object.values(UserCurrency)],
      default: UserCurrency.USD,
    },
    language: {
      type: String,
      enum: [...Object.values(UserLanguage)],
      default: UserLanguage.English,
    },
    otp: {
      type: Number,
    },
    otpExpireTime: {
      type: Date,
    },
    role: {
      type: String,
      enum: [...Object.values(UserRole)],
      default: UserRole.User,
    },
    currentRole: {
      type: String,
      enum: [...Object.values(UserCurrentRole)],
      default: UserCurrentRole.User,
    },
    status: {
      type: String,
      enum: [...Object.values(UserStatus)],
      default: UserStatus.Pending,
    },
    interestedIn: [
      {
        type: String,
        enum: [...Object.values(UserInterestedIn)],
      },
    ],
    deviceType: {
      type: String,
      enum: ["android", "ios", "web"],
    },
    deviceToken: {
      type: String, // FCM token for Firebase push notifications
    },
    stripeCustomerId: {
      type: String,
      description: "Stripe customer ID for payment processing",
    },
    merchantAccountId: {
      type: String,
    },
    deleteReason: {
      type: String,
    },
  },
  {
    timestamps: true,
  },
);

export default mongoose.model<IUser>("Users", userSchema);
