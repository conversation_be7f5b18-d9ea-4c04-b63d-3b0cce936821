import mongoose from "mongoose";
import Affiliate, {
  AffiliateStatus,
} from "../../affiliate-ms/src/models/Affiliate";

export interface AffiliateCodeValidationResult {
  isValid: boolean;
  affiliate?: any;
  message: string;
  messageKey: string;
}

class AffiliateCodeService {
  /**
   * Validate affiliate code for boat/activity creation or update
   * @param affiliateCode - The affiliate code to validate
   * @returns Validation result with details
   */
  async validateAffiliateCode(
    affiliateCode: string
  ): Promise<AffiliateCodeValidationResult> {
    try {
      // If no affiliate code provided, it's valid (optional field)
      if (!affiliateCode || affiliateCode.trim() === "") {
        return {
          isValid: true,
          message: "No affiliate code provided",
          messageKey: "AFFILIATE_CODE_NONE",
        };
      }

      // Trim and normalize the code
      const normalizedCode = affiliateCode.trim().toUpperCase();

      // Check if affiliate code exists
      const affiliate = await Affiliate.findOne({
        affiliateCode: normalizedCode,
      });

      if (!affiliate) {
        return {
          isValid: false,
          message: "Affiliate code does not exist",
          messageKey: "AFFILIATE_CODE_NOT_FOUND",
        };
      }

      // Check if affiliate is approved and active
      if (affiliate.status !== AffiliateStatus.Approved) {
        return {
          isValid: false,
          affiliate,
          message: `Affiliate code is not active. Current status: ${affiliate.status}`,
          messageKey: "AFFILIATE_CODE_NOT_ACTIVE",
        };
      }

      // Check if affiliate code has expired (if expireDate is set)
      if (affiliate.expireDate && new Date() > new Date(affiliate.expireDate)) {
        return {
          isValid: false,
          affiliate,
          message: "Affiliate code has expired",
          messageKey: "AFFILIATE_CODE_EXPIRED",
        };
      }

      // All validations passed
      return {
        isValid: true,
        affiliate,
        message: "Affiliate code is valid and active",
        messageKey: "AFFILIATE_CODE_VALID",
      };
    } catch (error) {
      console.error("Error validating affiliate code:", error);
      return {
        isValid: false,
        message: "Error validating affiliate code",
        messageKey: "AFFILIATE_CODE_VALIDATION_ERROR",
      };
    }
  }

  /**
   * Get affiliate by code (for internal use)
   * @param affiliateCode - The affiliate code
   * @returns Affiliate document or null
   */
  async getAffiliateByCode(affiliateCode: string) {
    try {
      if (!affiliateCode) return null;

      return await Affiliate.findOne({
        affiliateCode: affiliateCode.trim().toUpperCase(),
        status: AffiliateStatus.Approved,
      });
    } catch (error) {
      console.error("Error fetching affiliate by code:", error);
      return null;
    }
  }

  /**
   * Check if affiliate code is available for use
   * @param affiliateCode - The affiliate code to check
   * @returns Boolean indicating availability
   */
  async isAffiliateCodeAvailable(affiliateCode: string): Promise<boolean> {
    try {
      if (!affiliateCode) return false;

      const existing = await Affiliate.findOne({
        affiliateCode: affiliateCode.trim().toUpperCase(),
      });

      return !existing;
    } catch (error) {
      console.error("Error checking affiliate code availability:", error);
      return false;
    }
  }

  /**
   * Generate a unique affiliate code
   * @param prefix - Optional prefix for the code
   * @returns Unique affiliate code
   */
  async generateUniqueAffiliateCode(prefix: string = "SEA"): Promise<string> {
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      // Generate random code
      const randomPart = Math.random()
        .toString(36)
        .substring(2, 8)
        .toUpperCase();
      const timestamp = Date.now().toString().slice(-4);
      const affiliateCode = `${prefix}${randomPart}${timestamp}`;

      // Check if it's available
      const isAvailable = await this.isAffiliateCodeAvailable(affiliateCode);
      if (isAvailable) {
        return affiliateCode;
      }

      attempts++;
    }

    throw new Error(
      "Unable to generate unique affiliate code after multiple attempts"
    );
  }

  /**
   * Validate and normalize affiliate code for storage
   * @param affiliateCode - Raw affiliate code
   * @returns Normalized affiliate code or null if invalid
   */
  normalizeAffiliateCode(affiliateCode: string): string | null {
    if (!affiliateCode || typeof affiliateCode !== "string") {
      return null;
    }

    const normalized = affiliateCode.trim().toUpperCase();

    // Basic format validation (alphanumeric, 6-20 characters)
    if (!/^[A-Z0-9]{6,20}$/.test(normalized)) {
      return null;
    }

    return normalized;
  }

  /**
   * Validate affiliate code format (basic format validation)
   * @param affiliateCode - The affiliate code to validate format
   * @returns Boolean indicating if format is valid
   */
  validateAffiliateCodeFormat(affiliateCode: string): boolean {
    if (!affiliateCode || typeof affiliateCode !== "string") {
      return false;
    }

    const normalized = affiliateCode.trim();

    // Basic format validation:
    // - Must be 6-20 characters long
    // - Can contain letters and numbers only
    // - Must start with a letter
    const formatRegex = /^[A-Za-z][A-Za-z0-9]{5,19}$/;

    return formatRegex.test(normalized);
  }

  /**
   * Bulk validate multiple affiliate codes
   * @param affiliateCodes - Array of affiliate codes to validate
   * @returns Array of validation results
   */
  async bulkValidateAffiliateCodes(
    affiliateCodes: string[]
  ): Promise<AffiliateCodeValidationResult[]> {
    const results: AffiliateCodeValidationResult[] = [];

    for (const code of affiliateCodes) {
      try {
        const result = await this.validateAffiliateCode(code);
        results.push(result);
      } catch (error) {
        results.push({
          isValid: false,
          message: "Error validating affiliate code",
          messageKey: "AFFILIATE_CODE_VALIDATION_ERROR",
        });
      }
    }

    return results;
  }

  /**
   * Get affiliate code usage statistics
   * @param affiliateCode - The affiliate code to get stats for
   * @returns Usage statistics
   */
  async getAffiliateCodeStats(affiliateCode: string) {
    try {
      if (!affiliateCode) return null;

      const normalizedCode = this.normalizeAffiliateCode(affiliateCode);
      if (!normalizedCode) return null;

      // Get affiliate info
      const affiliate = await this.getAffiliateByCode(normalizedCode);
      if (!affiliate) return null;

      // This would typically involve querying boats and bookings
      // For now, return basic info
      return {
        affiliateCode: normalizedCode,
        affiliateId: affiliate._id,
        affiliateName: affiliate.name,
        status: affiliate.status,
        createdAt: affiliate.createdAt,
        // Additional stats would be calculated here
        totalBoats: 0, // Would be calculated from boat collection
        totalBookings: 0, // Would be calculated from booking collection
        totalRevenue: 0, // Would be calculated from booking collection
      };
    } catch (error) {
      console.error("Error getting affiliate code stats:", error);
      return null;
    }
  }

  /**
   * Find affiliate by referral name and get their affiliate code
   * @param referralName - The referral name to search for
   * @returns Affiliate info with code or null if not found
   */
  async getAffiliateByReferralName(referralName: string) {
    try {
      if (!referralName || typeof referralName !== "string") {
        return null;
      }

      const trimmedName = referralName.trim();
      if (trimmedName.length === 0) {
        return null;
      }

      // Search for affiliate by name (case-insensitive)
      // Only return approved affiliates
      const affiliate = await Affiliate.findOne({
        name: { $regex: new RegExp(`^${trimmedName}$`, "i") },
        status: AffiliateStatus.Approved,
      });

      if (!affiliate) {
        return null;
      }

      return {
        affiliateId: affiliate._id,
        affiliateName: affiliate.name,
        affiliateCode: affiliate.affiliateCode,
        email: affiliate.email,
        status: affiliate.status,
      };
    } catch (error) {
      console.error("Error finding affiliate by referral name:", error);
      return null;
    }
  }

  /**
   * Validate referral name and return affiliate code if valid
   * @param referralName - The referral name to validate
   * @returns Validation result with affiliate code
   */
  async validateReferralName(referralName: string): Promise<{
    isValid: boolean;
    affiliateCode?: string;
    affiliateName?: string;
    message: string;
    messageKey: string;
  }> {
    try {
      // If no referral name provided, it's valid (optional field)
      if (!referralName || referralName.trim() === "") {
        return {
          isValid: true,
          message: "No referral name provided",
          messageKey: "REFERRAL_NAME_NONE",
        };
      }

      const affiliate = await this.getAffiliateByReferralName(referralName);

      if (!affiliate) {
        return {
          isValid: false,
          message: "Referral name not found or not active",
          messageKey: "REFERRAL_NAME_NOT_FOUND",
        };
      }

      return {
        isValid: true,
        affiliateCode: affiliate.affiliateCode,
        affiliateName: affiliate.affiliateName,
        message: "Referral name is valid",
        messageKey: "REFERRAL_NAME_VALID",
      };
    } catch (error) {
      console.error("Error validating referral name:", error);
      return {
        isValid: false,
        message: "Error validating referral name",
        messageKey: "REFERRAL_NAME_VALIDATION_ERROR",
      };
    }
  }
}

export default new AffiliateCodeService();
