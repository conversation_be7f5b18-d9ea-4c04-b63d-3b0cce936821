import { Request, Response, NextFunction } from 'express';
import logger from '../services/logger.service';

/**
 * App Version Check Middleware
 * 
 * Checks the client app version against configured min/max versions
 * and sets appropriate App-Version header response
 * 
 * Headers Expected:
 * - App-Version: Client app version (Android: integer, iOS: semantic version)
 * - Device-Type: "android" or "ios"
 * 
 * Response Headers Set:
 * - App-Version: "OK" | "Upgrade" | "Upgrade-Required"
 * 
 * Version Logic:
 * - OK: Client version is current/latest
 * - Upgrade: Client version is supported but newer version available
 * - Upgrade-Required: Client version is below minimum required version
 */

interface AppVersionConfig {
  MIN_APP_VERSION?: string;
  MAX_APP_VERSION?: string;
  MIN_IOS_VERSION?: string;
  MAX_IOS_VERSION?: string;
  [key: string]: any; // Allow other config properties
}

/**
 * Compare semantic versions (for iOS)
 * @param version1 First version to compare
 * @param version2 Second version to compare
 * @returns -1 if version1 < version2, 0 if equal, 1 if version1 > version2
 */
function compareSemanticVersions(version1: string, version2: string): number {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);
  
  // Ensure both arrays have same length by padding with zeros
  const maxLength = Math.max(v1Parts.length, v2Parts.length);
  while (v1Parts.length < maxLength) v1Parts.push(0);
  while (v2Parts.length < maxLength) v2Parts.push(0);
  
  for (let i = 0; i < maxLength; i++) {
    if (v1Parts[i] < v2Parts[i]) return -1;
    if (v1Parts[i] > v2Parts[i]) return 1;
  }
  
  return 0;
}

/**
 * Validate semantic version format
 * Supports formats like: 1.0.0, 1.0.1, 1.2.3, etc.
 * @param version Version string to validate
 * @returns true if valid semantic version format
 */
function isValidSemanticVersion(version: string): boolean {
  // Allow semantic versions with 2 or 3 parts: X.Y or X.Y.Z
  // Each part must be a non-negative integer
  const semanticVersionRegex = /^(\d+)\.(\d+)(?:\.(\d+))?$/;
  return semanticVersionRegex.test(version);
}

/**
 * Determine app version status for Android
 * @param clientVersion Client's app version (semantic version)
 * @param config Version configuration
 * @returns Version status
 */
function getAndroidVersionStatus(clientVersion: string, config: AppVersionConfig): string {
  const { MIN_APP_VERSION, MAX_APP_VERSION } = config;

  // If configuration is missing, default to OK
  if (typeof MIN_APP_VERSION !== 'string' || typeof MAX_APP_VERSION !== 'string') {
    return 'OK';
  }

  // Check if client version is below minimum required
  if (compareSemanticVersions(clientVersion, MIN_APP_VERSION) < 0) {
    return 'Upgrade-Required';
  }

  // Check if client version is below current/latest
  if (compareSemanticVersions(clientVersion, MAX_APP_VERSION) < 0) {
    return 'Upgrade';
  }

  return 'OK';
}

/**
 * Determine app version status for iOS
 * @param clientVersion Client's app version (semantic version)
 * @param config Version configuration
 * @returns Version status
 */
function getIOSVersionStatus(clientVersion: string, config: AppVersionConfig): string {
  const { MIN_IOS_VERSION, MAX_IOS_VERSION } = config;

  // If configuration is missing, default to OK
  if (typeof MIN_IOS_VERSION !== 'string' || typeof MAX_IOS_VERSION !== 'string') {
    return 'OK';
  }

  // Check if client version is below minimum required
  if (compareSemanticVersions(clientVersion, MIN_IOS_VERSION) < 0) {
    return 'Upgrade-Required';
  }

  // Check if client version is below current/latest
  if (compareSemanticVersions(clientVersion, MAX_IOS_VERSION) < 0) {
    return 'Upgrade';
  }

  return 'OK';
}

/**
 * App Version Check Middleware
 */
export const checkAppVersion = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Get version and device type from headers
    const appVersion = req.headers['app-version'] as string;
    const deviceType = req.headers['device-type'] as string;
    
    // Get configuration
    const config = global.config as AppVersionConfig;
    
    // If no version headers provided, skip version check
    if (!appVersion || !deviceType) {
      logger.debug('App version check skipped - missing headers', {
        appVersion,
        deviceType,
        userAgent: req.headers['user-agent']
      });
      return next();
    }
    
    let versionStatus = 'OK';
    
    // Normalize device type
    const normalizedDeviceType = deviceType.toLowerCase().trim();
    
    if (normalizedDeviceType === 'android') {
      // Android version handling (semantic versioning)
      if (!isValidSemanticVersion(appVersion)) {
        logger.warn('Invalid Android app version format', {
          appVersion,
          deviceType,
          userAgent: req.headers['user-agent']
        });
        return next();
      }

      versionStatus = getAndroidVersionStatus(appVersion, config);

      logger.debug('Android version check completed', {
        clientVersion: appVersion,
        minVersion: config.MIN_APP_VERSION,
        maxVersion: config.MAX_APP_VERSION,
        status: versionStatus
      });
      
    } else if (normalizedDeviceType === 'ios') {
      // iOS version handling (semantic versioning)
      if (!isValidSemanticVersion(appVersion)) {
        logger.warn('Invalid iOS app version format', {
          appVersion,
          deviceType,
          userAgent: req.headers['user-agent']
        });
        return next();
      }
      
      versionStatus = getIOSVersionStatus(appVersion, config);
      
      logger.debug('iOS version check completed', {
        clientVersion: appVersion,
        minVersion: config.MIN_IOS_VERSION,
        maxVersion: config.MAX_IOS_VERSION,
        status: versionStatus
      });
      
    } else {
      logger.warn('Unknown device type for version check', {
        deviceType,
        appVersion,
        userAgent: req.headers['user-agent']
      });
      return next();
    }
    
    // Set response header with version status
    res.setHeader('App-Version', versionStatus);
    
    logger.info('App version check completed', {
      deviceType: normalizedDeviceType,
      clientVersion: appVersion,
      versionStatus,
      userAgent: req.headers['user-agent']
    });
    
    next();
    
  } catch (error) {
    logger.error('Error in app version check middleware', {
      error: error instanceof Error ? error.message : 'Unknown error',
      appVersion: req.headers['app-version'],
      deviceType: req.headers['device-type'],
      userAgent: req.headers['user-agent']
    });
    
    // Continue without blocking the request on error
    next();
  }
};

export default checkAppVersion;
