/**
 * Boat service that directly queries the boat collection
 */

import Boat from "../../../boat-ms/src/models/Boat";
class BoatService {
  /**
   * Get boats by affiliate code
   * @param affiliateCode - The affiliate code to search for
   * @returns Array of boats with the specified affiliate code
   */
  async getBoatsByAffiliateCode(affiliateCode: string) {
    try {
      if (!affiliateCode) {
        return [];
      }

      console.log(`[BoatService] Searching for boats with affiliate code: "${affiliateCode}"`);

      // First, let's check if there are any boats with affiliate codes at all
      const totalBoatsWithAffiliateCode = await Boat.countDocuments({
        affiliateCode: { $exists: true, $ne: null, $ne: "" },
        status: { $ne: "deleted" }
      });
      console.log(`[BoatService] Total boats with affiliate codes: ${totalBoatsWithAffiliateCode}`);

      // Find boats with the given affiliate code and populate with enhanced data
      // Use case-insensitive regex to match affiliate codes
      const boats = await Boat.aggregate([
        {
          $match: {
            affiliateCode: { $regex: new RegExp(`^${affiliateCode}$`, "i") },
            status: { $ne: "deleted" }
          }
        },
        {
          $lookup: {
            from: "files",
            localField: "attachments.images",
            foreignField: "_id",
            as: "imageFiles"
          }
        },
        {
          $lookup: {
            from: "users",
            localField: "ownerId",
            foreignField: "_id",
            as: "owner"
          }
        },
        {
          $lookup: {
            from: "reviews",
            localField: "_id",
            foreignField: "boatId",
            as: "reviews"
          }
        },
        {
          $lookup: {
            from: "files",
            localField: "$owner.avatar",
            foreignField: "_id",
            as: "avatarImg"
          }
        },
        {
          $unwind: { path: "$owner", preserveNullAndEmptyArrays: true }
        },
        {
          $addFields: {
            image: {
              $cond: {
                if: { $gt: [{ $size: "$imageFiles" }, 0] },
                then: {
                  id: { $arrayElemAt: ["$imageFiles._id", 0] },
                  link: { $concat: [global.config.FILE_BASE_URL, { $arrayElemAt: ["$imageFiles.location", 0] }] }
                },
                else: null
              }
            },
            averageRating: {
              $cond: {
                if: { $gt: [{ $size: "$reviews" }, 0] },
                then: { $avg: "$reviews.rating" },
                else: 0
              }
            },
            reviewCount: { $size: "$reviews" },
            ownerProfile: {
              id: "$owner._id",
              name: "$owner.username",
              email: "$owner.email",
              avatar: {
                $cond: {
                  if: { $gt: [{ $size: "$avatarImg" }, 0] },
                  then: {
                    $concat: [global.config.FILE_BASE_URL, { $arrayElemAt: ["$avatarImg.location", 0] }]
                  },
                  else: null
                }
              }
            }
          }
        },
        {
          $project: {
            _id: 1,
            name: 1,
            type: 1,
            recordType: 1,
            pricePerDay: 1,
            price: 1,
            image: 1,
            averageRating: { $round: ["$averageRating", 1] },
            reviewCount: 1,
            ownerProfile: 1,
            location: 1,
            guestsCapacity: 1,
            status: 1,
            createdAt: 1
          }
        }
      ]);

      console.log(`[BoatService] Found ${boats.length} boats with affiliate code "${affiliateCode}"`);
      if (boats.length > 0) {
        console.log(`[BoatService] First boat:`, boats[0]);
      }

      return boats;
    } catch (error) {
      console.error("Error fetching boats by affiliate code:", error);
      return [];
    }
  }
}

export default new BoatService();
