import mongoose, { Schema, Document } from "mongoose";

export enum NewsletterStatus {
  Active = "active",
  Deleted = "deleted",
}

export interface INewsletter extends Document {
  imageId: mongoose.Types.ObjectId;
  header: string;
  content: string;
  status: NewsletterStatus;
  createdAt: Date;
  updatedAt: Date;
}

const NewsletterSchema: Schema = new Schema(
  {
    imageId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "File",
      required: true,
    },
    header: {
      type: String,
      required: true,
      trim: true,
    },
    content: {
      type: String,
      required: true,
      trim: true,
    },
    status: {
      type: String,
      enum: Object.values(NewsletterStatus),
      default: NewsletterStatus.Active,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
    },
  },
);

// Add virtual for image URL
NewsletterSchema.virtual("image").get(function () {
  if (this.populated("imageId")) {
    const imageDoc = this.imageId as any;
    return imageDoc?.location
      ? `${global.config.FILE_BASE_URL}${imageDoc.location}`
      : null;
  }
  return null;
});

// Add index for status to improve query performance
NewsletterSchema.index({ status: 1 });

export default mongoose.model<INewsletter>("Newsletter", NewsletterSchema);
