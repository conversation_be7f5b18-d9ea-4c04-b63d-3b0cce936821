import jwt from "jsonwebtoken";

const generateToken = async function (user: any) {
  try {
    const payload = user;
    const token = jwt.sign(payload, global.config.JWT_SECRET, {
      expiresIn: global.config.JWT_EXIPIRATION_TIME,
    });

    return token;
  } catch (e: any) {
    console.log(e);
  }
};

const generateRefreshToken = async function (user: any) {
  try {
    const payload = user;
    const token = jwt.sign(payload, global.config.JWT_REFRESH_SECRET, {
      expiresIn: global.config.JWT_REFRESH_EXIPIRATION_TIME,
    });

    return token;
  } catch (e: any) {
    console.log(e);
  }
};

const verifyToken = async function (token: any) {
  try {
    const decoded = jwt.verify(token, global.config.JWT_SECRET);
    return decoded;
  } catch (e: any) {
    return e;
  }
};
export { generateToken, verifyToken, generateRefreshToken };
