import Affiliate, { AffiliateStatus } from "../models/Affiliate";
import User from "../../../user-ms/src/models/User";
import File from "../../../shared/models/Files";
import boatService from "../services/boat.service";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";
import { emailSender } from "../../../shared/services/sendMail.service";
import { NotificationService } from "../../../notification-ms/src/services/notification.service";
import { EMAIL_CONSTANT } from "../../../auth-ms/src/constant/emailContant";
import { NOTIFICATION_TYPES } from "../../../notification-ms/src/constant/notificationConstant";
import affiliateCodeService from "../../../shared/services/affiliateCode.service";
import mongoose from "mongoose";
import Payment from "../models/Payment";
import { constants } from "buffer";

// Generate a unique affiliate code using the service
const generateAffiliateCode = async (): Promise<string> => {
  return await affiliateCodeService.generateUniqueAffiliateCode("SEA");
};

// Create a new affiliate
export const createAffiliate = async (req: any, res: any) => {
  try {
    const {
      name,
      email,
      phoneNo,
      accountHolderName,
      accountNumber,
      bankName,
      routingNumber,
      paypalEmail,
    } = req.body;

    // Check if affiliate already exists with this email
    const existingAffiliate = await Affiliate.findOne({ email });
    if (existingAffiliate) {
      return res.status(400).json({
        success: false,
        message: req.__("AFFILIATE_ALREADY_EXISTS"),
      });
    }

    // Generate unique affiliate code
    let affiliateCode;
    try {
      affiliateCode = await generateAffiliateCode();

      // The generateAffiliateCode function already ensures uniqueness
      // No need for additional validation since it's a new code
      console.log("Generated affiliate code:", affiliateCode);
    } catch (error) {
      console.error("Error generating affiliate code:", error);
      return res.status(500).json({
        success: false,
        message: req.__("AFFILIATE_CODE_GENERATION_FAILED"),
      });
    }

    // Create new affiliate
    const affiliate = new Affiliate({
      userId: req.user._id,
      name,
      email,
      phoneNo,
      accountHolderName,
      accountNumber,
      bankName,
      routingNumber,
      paypalEmail,
      affiliateCode,
      status: AffiliateStatus.Pending,
    });

    await affiliate.save();

    const changes = validateAndGetChanges({}, affiliate.toJSON());
    // Log the creation
    await createActivityLog(
      "affiliates",
      affiliate._id,
      "CREATE",
      changes,
      req.user._id
    );

    return res.status(201).json({
      success: true,
      message: req.__("AFFILIATE_CREATED_SUCCESS"),
      data: affiliate.toJSON(),
    });
  } catch (error) {
    console.error("Error creating affiliate:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Get all affiliates with enhanced features
export const getAllAffiliates = async (req: any, res: any) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      status
    } = req.query;

    const skip = (page - 1) * limit;

    // Build query
    const query: any = {
      status: { $ne: AffiliateStatus.Deleted },
    };

    // Handle status filter (single or comma-separated)
    if (status) {
      const statusArray = status.split(',').map((s: string) => s.trim()).filter((s: string) => s.length > 0);

      if (statusArray.length > 0) {
        // Validate all statuses
        const invalidStatuses = statusArray.filter(
          (statusValue: string) => !Object.values(AffiliateStatus).includes(statusValue as AffiliateStatus)
        );

        if (invalidStatuses.length > 0) {
          return res.status(400).json({
            success: false,
            message: req.__("AFFILIATE_INVALID_STATUS"),
            invalidStatuses
          });
        }

        // Use $in for multiple statuses or single status
        query.status = statusArray.length === 1 ? statusArray[0] : { $in: statusArray };
      }
    }
    
    // Add search filter for name and email
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } }
      ];
    }

    // Use aggregation pipeline to include user profile data
    const pipeline = [
      { $match: query },

      // Lookup user data
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "userProfile"
        }
      },

      // Lookup user avatar
      {
        $lookup: {
          from: "files",
          localField: "userProfile.avatar",
          foreignField: "_id",
          as: "userAvatar"
        }
      },

      // Add fields and transform data
      {
        $addFields: {
          userProfile: { $arrayElemAt: ["$userProfile", 0] },
          userAvatar: { $arrayElemAt: ["$userAvatar", 0] }
        }
      },

      // Project final structure
      {
        $project: {
          _id: 1,
          userId: 1,
          name: 1,
          email: 1,
          phoneNo: 1,
          accountHolderName: 1,
          accountNumber: 1,
          bankName: 1,
          routingNumber: 1,
          paypalEmail: 1,
          affiliateCode: 1,
          rejectionReason: 1,
          expireDate: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          currency: "$userProfile.currency",
          profileImage: {
            $cond: {
              if: "$userAvatar",
              then: {
                id: "$userAvatar._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$userAvatar.location"] }
              },
              else: null
            }
          }
        }
      },

      { $sort: { createdAt: -1 } },

      // Pagination
      {
        $facet: {
          data: [
            { $skip: Number(skip) },
            { $limit: Number(limit) }
          ],
          metadata: [
            { $count: "total" }
          ]
        }
      }
    ];

    const result = await Affiliate.aggregate(pipeline as any[]);
    const affiliates = result[0].data;
    const total = result[0].metadata[0]?.total || 0;

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_FETCH_SUCCESS"),
      data: {
        affiliates,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error("Error fetching affiliates:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Get a single affiliate by ID with enhanced data
export const getAffiliateById = async (req: any, res: any) => {
  try {
    const { id } = req.params;

    // Use aggregation to get affiliate with user profile data
    const pipeline = [
      {
        $match: {
          _id: new mongoose.Types.ObjectId(id),
          status: { $ne: AffiliateStatus.Deleted }
        }
      },

      // Lookup user data
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "userProfile"
        }
      },

      // Lookup user avatar
      {
        $lookup: {
          from: "files",
          localField: "userProfile.avatar",
          foreignField: "_id",
          as: "userAvatar"
        }
      },

      // Add fields and transform data
      {
        $addFields: {
          userProfile: { $arrayElemAt: ["$userProfile", 0] },
          userAvatar: { $arrayElemAt: ["$userAvatar", 0] }
        }
      },

      // Project final structure
      {
        $project: {
          _id: 1,
          userId: 1,
          name: 1,
          email: 1,
          phoneNo: 1,
          accountHolderName: 1,
          accountNumber: 1,
          bankName: 1,
          routingNumber: 1,
          paypalEmail: 1,
          affiliateCode: 1,
          rejectionReason: 1,
          expireDate: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          currency: "$userProfile.currency",
          profileImage: {
            $cond: {
              if: "$userAvatar",
              then: {
                id: "$userAvatar._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$userAvatar.location"] }
              },
              else: null
            }
          }
        }
      }
    ];

    const result = await Affiliate.aggregate(pipeline as any[]);
    const affiliate = result[0];

    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_FOUND"),
      });
    }

    // Get boats linked to this affiliate code
    let affiliatedBoats: any[] = [];
    if (
      affiliate.status === AffiliateStatus.Approved &&
      affiliate.affiliateCode
    ) {
      affiliatedBoats = await boatService.getBoatsByAffiliateCode(
        affiliate.affiliateCode
      );
    }

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_FETCH_SUCCESS"),
      data: {
        affiliate,
        affiliatedBoats,
      },
    });
  } catch (error) {
    console.error("Error fetching affiliate:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Update an affiliate
export const updateAffiliate = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Check if affiliate exists
    const affiliate = await Affiliate.findOne({
      _id: id,
      status: { $ne: AffiliateStatus.Deleted },
    });

    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_FOUND"),
      });
    }

    // If email is being updated, check if it's already in use
    if (updateData.email && updateData.email !== affiliate.email) {
      const emailExists = await Affiliate.findOne({
        email: updateData.email,
        _id: { $ne: id },
      });

      if (emailExists) {
        return res.status(400).json({
          success: false,
          message: req.__("AFFILIATE_ALREADY_EXISTS"),
        });
      }
    }

    // If affiliate code is being updated, validate it
    if (
      updateData.affiliateCode &&
      updateData.affiliateCode !== affiliate.affiliateCode
    ) {
      try {
        const codeValidation = await affiliateCodeService.validateAffiliateCode(
          updateData.affiliateCode
        );
        if (!codeValidation.isValid) {
          return res.status(400).json({
            success: false,
            message: req.__(codeValidation.messageKey),
          });
        }

        // Check if the new code is already taken by another affiliate
        const codeExists = await Affiliate.findOne({
          affiliateCode: updateData.affiliateCode.trim().toUpperCase(),
          _id: { $ne: id },
        });

        if (codeExists) {
          return res.status(400).json({
            success: false,
            message: req.__("AFFILIATE_CODE_ALREADY_EXISTS"),
          });
        }

        // Normalize the affiliate code
        updateData.affiliateCode = affiliateCodeService.normalizeAffiliateCode(
          updateData.affiliateCode
        );
      } catch (error) {
        console.error("Error validating affiliate code during update:", error);
        return res.status(500).json({
          success: false,
          message: req.__("AFFILIATE_CODE_VALIDATION_ERROR"),
        });
      }
    }

    // Get changes before update
    const changes = validateAndGetChanges(affiliate.toJSON(), updateData);

    // Update affiliate
    const updatedAffiliate = await Affiliate.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true }
    );

    // Log the changes if any
    if (changes.length > 0) {
      await createActivityLog(
        "affiliates",
        id,
        "UPDATE",
        changes,
        req.user._id
      );
    }

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_UPDATED_SUCCESS"),
      data: updatedAffiliate,
    });
  } catch (error) {
    console.error("Error updating affiliate:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Delete an affiliate (soft delete by setting status to deleted)
export const deleteAffiliate = async (req: any, res: any) => {
  try {
    const { id } = req.params;

    // Check if affiliate exists
    const affiliate = await Affiliate.findOne({
      _id: id,
      status: { $ne: AffiliateStatus.Deleted },
    });

    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_FOUND"),
      });
    }

    // Get changes before update
    const changes = validateAndGetChanges(affiliate.toJSON(), {
      status: AffiliateStatus.Deleted,
    });

    // Soft delete by updating status
    await Affiliate.findByIdAndUpdate(id, {
      status: AffiliateStatus.Deleted,
    });

    // Log the deletion
    await createActivityLog("affiliates", id, "DELETE", changes, req.user._id);

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_DELETED_SUCCESS"),
    });
  } catch (error) {
    console.error("Error deleting affiliate:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Approve an affiliate application
export const approveAffiliate = async (req: any, res: any) => {
  try {
    const { id } = req.params;

    // Check if affiliate exists
    const affiliate = await Affiliate.findOne({
      _id: id,
      status: { $ne: AffiliateStatus.Deleted },
    });

    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_FOUND"),
      });
    }

    // Check if already approved
    if (affiliate.status === AffiliateStatus.Approved) {
      return res.status(400).json({
        success: false,
        message: req.__("AFFILIATE_ALREADY_APPROVED"),
      });
    }

    // Get changes before update
    const changes = validateAndGetChanges(affiliate.toJSON(), {
      status: AffiliateStatus.Approved,
    });

    // Update affiliate status
    const updatedAffiliate = await Affiliate.findByIdAndUpdate(
      id,
      { $set: { status: AffiliateStatus.Approved } },
      { new: true }
    );

    // Log the changes
    await createActivityLog("affiliates", id, "UPDATE", changes, req.user._id);

    // Send notification
    await NotificationService.createAndSendNotification(
      affiliate.userId.toString(),
      req.user._id,
      NOTIFICATION_TYPES.AFFILIATE_APPROVED,
      {
        affiliateId: affiliate._id,
        affiliateCode: affiliate.affiliateCode,
      }
    );

    // Send email
    emailSender(
      affiliate.email,
      EMAIL_CONSTANT.AFFILIATE_APPROVED_EMAIL.subject,
      {
        name: affiliate.name,
        affiliateCode: affiliate.affiliateCode,
      },
      EMAIL_CONSTANT.AFFILIATE_APPROVED_EMAIL.templateName
    );

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_APPROVED_SUCCESS"),
      data: updatedAffiliate,
    });
  } catch (error) {
    console.error("Error approving affiliate:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Reject an affiliate application
export const rejectAffiliate = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: req.__("AFFILIATE_REJECTION_REASON_REQUIRED"),
      });
    }

    // Check if affiliate exists
    const affiliate = await Affiliate.findOne({
      _id: id,
      status: { $ne: AffiliateStatus.Deleted },
    });

    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_FOUND"),
      });
    }

    // Check if already rejected
    if (affiliate.status === AffiliateStatus.Rejected) {
      return res.status(400).json({
        success: false,
        message: req.__("AFFILIATE_ALREADY_REJECTED"),
      });
    }

    // Get changes before update
    const changes = validateAndGetChanges(affiliate.toJSON(), {
      status: AffiliateStatus.Rejected,
      rejectionReason: reason,
    });

    // Update affiliate status and add rejection reason
    const updatedAffiliate = await Affiliate.findByIdAndUpdate(
      id,
      {
        $set: {
          status: AffiliateStatus.Rejected,
          rejectionReason: reason,
        },
      },
      { new: true }
    );

    // Log the changes
    await createActivityLog("affiliates", id, "UPDATE", changes, req.user._id);

    // Send notification
    await NotificationService.createAndSendNotification(
      affiliate.userId.toString(),
      req.user._id,
      NOTIFICATION_TYPES.AFFILIATE_REJECTED,
      {
        affiliateId: affiliate._id,
        reason,
      }
    );

    // Send email
    emailSender(
      affiliate.email,
      EMAIL_CONSTANT.AFFILIATE_REJECTED_EMAIL.subject,
      {
        name: affiliate.name,
        reason,
      },
      EMAIL_CONSTANT.AFFILIATE_REJECTED_EMAIL.templateName
    );

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_REJECTED_SUCCESS"),
      data: updatedAffiliate,
    });
  } catch (error) {
    console.error("Error rejecting affiliate:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Get affiliates by status (for admin) with enhanced features
export const getAffiliatesByStatus = async (req: any, res: any) => {
  try {
    const { status } = req.params;
    const {
      page = 1,
      limit = 10,
      search
    } = req.query;

    const skip = (page - 1) * limit;

    // Validate status
    if (
      status &&
      !Object.values(AffiliateStatus).includes(status as AffiliateStatus)
    ) {
      return res.status(400).json({
        success: false,
        message: req.__("AFFILIATE_INVALID_STATUS"),
      });
    }

    // Build query
    const query: any = status
      ? { status }
      : { status: { $ne: AffiliateStatus.Deleted } };

    // Add search filter for name and email
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } }
      ];
    }

    // Use aggregation pipeline to include user profile data
    const pipeline = [
      { $match: query },

      // Lookup user data
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "userProfile"
        }
      },

      // Lookup user avatar
      {
        $lookup: {
          from: "files",
          localField: "userProfile.avatar",
          foreignField: "_id",
          as: "userAvatar"
        }
      },

      // Add fields and transform data
      {
        $addFields: {
          userProfile: { $arrayElemAt: ["$userProfile", 0] },
          userAvatar: { $arrayElemAt: ["$userAvatar", 0] }
        }
      },

      // Project final structure
      {
        $project: {
          _id: 1,
          userId: 1,
          name: 1,
          email: 1,
          phoneNo: 1,
          accountHolderName: 1,
          accountNumber: 1,
          bankName: 1,
          routingNumber: 1,
          paypalEmail: 1,
          affiliateCode: 1,
          rejectionReason: 1,
          expireDate: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          currency: "$userProfile.currency",
          profileImage: {
            $cond: {
              if: "$userAvatar",
              then: {
                id: "$userAvatar._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$userAvatar.location"] }
              },
              else: null
            }
          }
        }
      },

      { $sort: { createdAt: -1 } },

      // Pagination
      {
        $facet: {
          data: [
            { $skip: Number(skip) },
            { $limit: Number(limit) }
          ],
          metadata: [
            { $count: "total" }
          ]
        }
      }
    ];

    const result = await Affiliate.aggregate(pipeline as any[]);
    const affiliates = result[0].data;
    const total = result[0].metadata[0]?.total || 0;

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_FETCH_SUCCESS"),
      data: {
        affiliates,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error("Error fetching affiliates by status:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Get the current user's affiliate application with enhanced data
export const getMyAffiliateStatus = async (req: any, res: any) => {
  try {
    const userId = req.user._id; // From auth middleware

    // Use aggregation to get affiliate with user profile data
    const pipeline = [
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          status: { $ne: AffiliateStatus.Deleted }
        }
      },

      // Lookup user data
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "userProfile"
        }
      },

      // Lookup user avatar
      {
        $lookup: {
          from: "files",
          localField: "userProfile.avatar",
          foreignField: "_id",
          as: "userAvatar"
        }
      },

      // Add fields and transform data
      {
        $addFields: {
          userProfile: { $arrayElemAt: ["$userProfile", 0] },
          userAvatar: { $arrayElemAt: ["$userAvatar", 0] }
        }
      },

      // Project final structure
      {
        $project: {
          _id: 1,
          userId: 1,
          name: 1,
          email: 1,
          phoneNo: 1,
          accountHolderName: 1,
          accountNumber: 1,
          bankName: 1,
          routingNumber: 1,
          paypalEmail: 1,
          affiliateCode: 1,
          rejectionReason: 1,
          expireDate: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          currency: "$userProfile.currency",
          profileImage: {
            $cond: {
              if: "$userAvatar",
              then: {
                id: "$userAvatar._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$userAvatar.location"] }
              },
              else: null
            }
          }
        }
      }
    ];

    const result = await Affiliate.aggregate(pipeline as any[]);
    const affiliate = result[0];

    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_FOUND"),
      });
    }

    // If approved, get boats using affiliate code
    let affiliatedBoats: any[] = [];
    if (
      affiliate.status === AffiliateStatus.Approved &&
      affiliate.affiliateCode
    ) {
      affiliatedBoats = await boatService.getBoatsByAffiliateCode(
        affiliate.affiliateCode
      );
    }

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_FETCH_SUCCESS"),
      data: {
        affiliate,
        affiliatedBoats:
          affiliate.status === AffiliateStatus.Approved ? affiliatedBoats : [],
      },
    });
  } catch (error) {
    console.error("Error fetching user's affiliate status:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Get affiliates with multiple statuses (for UI sections like "Approved" and "Others")
export const getAffiliatesByMultipleStatuses = async (req: any, res: any) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      statuses // Comma-separated statuses like "pending,rejected" for "others" section
    } = req.query;

    const skip = (page - 1) * limit;

    if (!statuses) {
      return res.status(400).json({
        success: false,
        message: req.__("STATUSES_PARAMETER_REQUIRED"),
      });
    }

    // Parse statuses from comma-separated string
    const statusArray = statuses.split(',').map((s: string) => s.trim());

    // Validate all statuses
    const invalidStatuses = statusArray.filter(
      (status: string) => !Object.values(AffiliateStatus).includes(status as AffiliateStatus)
    );

    if (invalidStatuses.length > 0) {
      return res.status(400).json({
        success: false,
        message: req.__("AFFILIATE_INVALID_STATUS"),
        invalidStatuses
      });
    }

    // Build query
    const query: any = {
      status: { $in: statusArray },
    };

    // Add search filter for name and email
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } }
      ];
    }

    // Use aggregation pipeline to include user profile data
    const pipeline = [
      { $match: query },

      // Lookup user data
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "userProfile"
        }
      },

      // Lookup user avatar
      {
        $lookup: {
          from: "files",
          localField: "userProfile.avatar",
          foreignField: "_id",
          as: "userAvatar"
        }
      },

      // Add fields and transform data
      {
        $addFields: {
          userProfile: { $arrayElemAt: ["$userProfile", 0] },
          userAvatar: { $arrayElemAt: ["$userAvatar", 0] }
        }
      },

      // Project final structure
      {
        $project: {
          _id: 1,
          userId: 1,
          name: 1,
          email: 1,
          phoneNo: 1,
          accountHolderName: 1,
          accountNumber: 1,
          bankName: 1,
          routingNumber: 1,
          paypalEmail: 1,
          affiliateCode: 1,
          rejectionReason: 1,
          expireDate: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          currency: "$userProfile.currency",
          profileImage: {
            $cond: {
              if: "$userAvatar",
              then: {
                id: "$userAvatar._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$userAvatar.location"] }
              },
              else: null
            }
          }
        }
      },

      { $sort: { createdAt: -1 } },

      // Pagination
      {
        $facet: {
          data: [
            { $skip: Number(skip) },
            { $limit: Number(limit) }
          ],
          metadata: [
            { $count: "total" }
          ]
        }
      }
    ];

    const result = await Affiliate.aggregate(pipeline as any[]);
    const affiliates = result[0].data;
    const total = result[0].metadata[0]?.total || 0;

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_FETCH_SUCCESS"),
      data: {
        affiliates,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error("Error fetching affiliates by multiple statuses:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Get boats registered with my affiliate code
export const getMyAffiliatedBoats = async (req: any, res: any) => {
  try {
    const userId = req.user._id; // From auth middleware
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Find the user's affiliate account
    const affiliate = await Affiliate.findOne({
      userId,
      status: AffiliateStatus.Approved,
    });

    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_APPROVED"),
      });
    }

    // Get all boats linked to this affiliate code using the boat service
    const boats = await boatService.getBoatsByAffiliateCode(
      affiliate.affiliateCode
    );

    // Add pagination manually for now
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedBoats = boats.slice(startIndex, endIndex);

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_FETCH_BOATS_SUCCESS"),
      data: {
        affiliateCode: affiliate.affiliateCode,
        boats: paginatedBoats,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: boats.length,
          totalPages: Math.ceil(boats.length / Number(limit))
        },
      },
    });
  } catch (error) {
    console.error("Error fetching affiliated boats:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Get affiliate bookings with commission details
export const getMyAffiliateBookings = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Find the user's affiliate account
    const affiliate = await Affiliate.findOne({
      userId,
      status: AffiliateStatus.Approved,
    });

    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_APPROVED"),
      });
    }

    // Import Booking model
    const Booking = require("../../../booking-ms/src/models/Booking").default;

    // Aggregate bookings with affiliate commission details
    const pipeline = [
      {
        $match: {
          affiliateCode: affiliate.affiliateCode,
          status: { $nin: ["Cancelled", "Rejected"] }
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "user"
        }
      },
      {
        $lookup: {
          from: "boats",
          localField: "boatId",
          foreignField: "_id",
          as: "boat"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "user.avatar",
          foreignField: "_id",
          as: "userAvatar"
        }
      },
      {
        $unwind: { path: "$user", preserveNullAndEmptyArrays: true }},
      {
        $unwind: { path: "$boat", preserveNullAndEmptyArrays: true }},
      {
        $unwind: { path: "$userAvatar", preserveNullAndEmptyArrays: true }},
      {
        $addFields: {
          // Calculate affiliate commission (assuming 5% of total amount)
          affiliateCommissionAmount: { $multiply: ["$totalAmount", 0.05] },
          affiliateCommissionPercentage: 5,
          userProfile: {
            id: "$user._id",
            username: "$user.username",
            email: "$user.email",
            avatar: {
              $cond: {
                if: "$userAvatar",
                then: { $concat: [global.config.FILE_BASE_URL, "$userAvatar.location"] },
                else: null
              }
            }
          }
        }
      },
      {
        $project: {
          _id: 1,
          boatId: 1,
          boatName: "$boat.name",
          userProfile: 1,
          bookedTime: {
            startDate: "$startDate",
            endDate: "$endDate"
          },
          bookingPrice: "$totalAmount",
          affiliateCommission: {
            amount: "$affiliateCommissionAmount",
            percentage: "$affiliateCommissionPercentage"
          },
          status: 1,
          createdAt: 1
        }
      },
      { $sort: { createdAt: -1 }},
      {
        $facet: {
          data: [
            { $skip: skip },
            { $limit: limit }
          ],
          metadata: [
            { $count: "total" }
          ]
        }
      }
    ];

    const result = await Booking.aggregate(pipeline);
    const bookings = result[0].data;
    const total = result[0].metadata[0]?.total || 0;

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_BOOKINGS_FETCH_SUCCESS"),
      data: {
        bookings,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: total,
          totalPages: Math.ceil(total / Number(limit))
        }
      },
    });
  } catch (error: any) {
    console.error("Error fetching affiliate bookings:", error);

    return res.status(500).json({
      success: false,
      message: req.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

// Validate referral name (for booking form)
export const validateReferralName = async (req: any, res: any) => {
  try {
    const { referralName } = req.body;

    if (!referralName) {
      return res.status(400).json({
        success: false,
        message: req.__("REFERRAL_NAME_REQUIRED"),
      });
    }

    const validation =
      await affiliateCodeService.validateReferralName(referralName);

    if (validation.isValid) {
      return res.status(200).json({
        success: true,
        message: req.__(validation.messageKey),
        data: {
          referralName,
          affiliateCode: validation.affiliateCode,
          affiliateName: validation.affiliateName,
          isValid: true,
        },
      });
    } else {
      return res.status(400).json({
        success: false,
        message: req.__(validation.messageKey),
        data: {
          referralName,
          isValid: false,
        },
      });
    }
  } catch (error) {
    console.error("Error validating referral name:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Re-apply for affiliate program
export const reapplyAffiliate = async (req: any, res: any) => {
  try {
    const {
      name,
      email,
      phoneNo,
      accountHolderName,
      accountNumber,
      bankName,
      routingNumber,
      paypalEmail,
    } = req.body;
    const userId = req.user._id;

    // Check if user has a rejected application
    const existingAffiliate = await Affiliate.findOne({
      userId,
      status: AffiliateStatus.Rejected,
    });
    console.log(
      "Found existing rejected affiliate:",
      existingAffiliate ? "Yes" : "No"
    );

    if (!existingAffiliate) {
      return res.status(400).json({
        success: false,
        message: req.__("AFFILIATE_NO_REJECTED_APPLICATION"),
      });
    }

    // Generate new affiliate code
    let affiliateCode;
    try {
      affiliateCode = await generateAffiliateCode();

      // The generateAffiliateCode function already ensures uniqueness
      // No need for additional validation since it's a new code
      console.log(
        "Generated new affiliate code for reapplication:",
        affiliateCode
      );
    } catch (error) {
      console.error("Error generating affiliate code:", error);
      return res.status(500).json({
        success: false,
        message: req.__("AFFILIATE_CODE_GENERATION_FAILED"),
      });
    }

    // Update the existing application instead of creating a new one
    existingAffiliate.name = name;
    existingAffiliate.email = email;
    existingAffiliate.phoneNo = phoneNo;
    existingAffiliate.accountHolderName = accountHolderName;
    existingAffiliate.accountNumber = accountNumber;
    existingAffiliate.bankName = bankName;
    existingAffiliate.routingNumber = routingNumber;
    existingAffiliate.paypalEmail = paypalEmail;
    existingAffiliate.affiliateCode = affiliateCode;
    existingAffiliate.status = AffiliateStatus.Pending;
    existingAffiliate.rejectionReason = undefined; // Clear previous rejection reason
    await existingAffiliate.save();

    const changes = validateAndGetChanges({}, existingAffiliate.toJSON());
    // Log the reapplication as an UPDATE action
    await createActivityLog(
      "affiliates",
      existingAffiliate._id,
      "UPDATE",
      changes,
      userId
    );

    return res.status(201).json({
      success: true,
      message: req.__("AFFILIATE_REAPPLICATION_SUCCESS"),
      data: existingAffiliate.toJSON(),
    });
  } catch (error) {
    console.error("Error reapplying for affiliate:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Admin API: Get boats and activities for a specific affiliate or all boats/activities (Screen 215)
// Query Parameters:
// - id (optional): Affiliate ID to filter boats/activities by affiliate code
// - page: Page number for pagination (default: 1)
// - limit: Number of items per page (default: 10)
// If no ID provided, returns all boats and activities with owner and attachment details
export const getAffiliateBoats = async (req: any, res: any) => {
  try {
    console.log("\n\n\nHello from here\n\n\n")
    const { id, userId } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // console.log("getAffiliateBoats called with params:", { id, userId, page, limit });

    let affiliate = null;
    const matchCondition: any = {
      status: { $ne: "deleted" },
    };

    // If affiliate ID is provided, find the affiliate and filter by affiliate code
    if (id) {
      try {
        affiliate = await Affiliate.findById(id);
        if (!affiliate) {
          return res.status(404).json({
            success: false,
            message: req.__("AFFILIATE_NOT_FOUND"),
          });
        }

        // Only filter by affiliate code if affiliate is approved and has a code
        if (affiliate.status === AffiliateStatus.Approved && affiliate.affiliateCode) {
          matchCondition.affiliateCode = affiliate.affiliateCode;
        } 
      } catch (error) {
        console.error("Error finding affiliate:", error);
        return res.status(400).json({
          success: false,
          message: req.__("INVALID_AFFILIATE_ID"),
        });
      }
    }
    if (userId) {
      // Validate userId before creating ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({
          success: false,
          message: req.__("INVALID_USER_ID"),
        });
      }
      matchCondition.ownerId = new mongoose.Types.ObjectId(userId);
    }

    // console.log("Match condition:", matchCondition);

    // Import Boat model
    const Boat = require("../../../boat-ms/src/models/Boat").default;

    // Create a simplified, robust aggregation pipeline
    const pipeline = [
      // Stage 1: Match boats/activities based on condition
      {
        $match: matchCondition
      },

      // Stage 2: Lookup owner information
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "ownerData"
        }
      },

      // Stage 3: Lookup image files
      {
        $lookup: {
          from: "files",
          localField: "attachments.images",
          foreignField: "_id",
          as: "imageFiles"
        }
      },

      // Stage 4: Lookup video files
      {
        $lookup: {
          from: "files",
          localField: "attachments.videos",
          foreignField: "_id",
          as: "videoFiles"
        }
      },
      // Stage 5: Lookup reviews for rating calculation
      {
        $lookup: {
          from: "reviews",
          localField: "_id",
          foreignField: "boatId",
          as: "reviews"
        }
      },

      // Stage 6: Lookup active bookings
      {
        $lookup: {
          from: "bookings",
          localField: "_id",
          foreignField: "boatId",
          as: "bookings"
        }
      },

      // Stage 7: Transform and calculate fields
      {
        $addFields: {
          owner: { $arrayElemAt: ["$ownerData", 0] },
          activeReviews: {
            $filter: {
              input: "$reviews",
              cond: { $eq: ["$$this.status", "Active"] }
            }
          },
          activeBookings: {
            $filter: {
              input: "$bookings",
              cond: {
                $and: [
                  { $in: ["$$this.status", ["Pending", "Confirmed", "Accepted", "ReadyForPayment"]] },
                  { $gte: ["$$this.endDate", new Date()] }
                ]
              }
            }
          },
          activeImageFiles: {
            $filter: {
              input: "$imageFiles",
              cond: { $ne: ["$$this.status", "deleted"] }
            }
          },
          activeVideoFiles: {
            $filter: {
              input: "$videoFiles",
              cond: { $ne: ["$$this.status", "deleted"] }
            }
          }
        }
      },
      // Stage 8: Calculate final fields
      {
        $addFields: {
          averageRating: {
            $cond: [
              { $gt: [{ $size: "$activeReviews" }, 0] },
              { $avg: "$activeReviews.rating" },
              0
            ]
          },
          reviewCount: { $size: "$activeReviews" },
          isBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
          images: {
            $map: {
              input: "$activeImageFiles",
              as: "img",
              in: {
                id: "$$img._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$$img.location"] }
              }
            }
          },
          videos: {
            $map: {
              input: "$activeVideoFiles",
              as: "video",
              in: {
                id: "$$video._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$$video.location"] }
              }
            }
          },
          ownerInfo: {
            id: "$owner._id",
            name: "$owner.username",
            email: "$owner.email",
            firstName: "$owner.firstName",
            lastName: "$owner.lastName",
            currency: "$owner.currency"
          }
        }
      },
      // Stage 9: Project final fields
      {
        $project: {
          _id: 1,
          name: 1,
          type: 1,
          recordType: 1,
          description: 1,
          location: 1,
          lat: 1,
          lng: 1,
          affiliateCode: 1,
          availability: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,

          // Boat-specific fields
          guestsCapacity: 1,
          cabins: 1,
          baths: 1,
          pricePerDay: 1,
          fullDayWithPatron: 1,
          fullDayWithoutPatron: 1,
          halfDayWithPatron: 1,
          halfDayWithoutPatron: 1,
          fuel: 1,
          patron: 1,
          license: 1,
          language: 1,
          checkInNotes: 1,
          checkOutNotes: 1,

          // Activity-specific fields
          price: 1,
          duration: 1,
          safetyInstructions: 1,

          // Calculated fields
          images: 1,
          videos: 1,
          averageRating: { $round: ["$averageRating", 1] },
          reviewCount: 1,
          isBooked: 1,
          ownerInfo: 1
        }
      },

      // Stage 10: Sort by creation date
      { $sort: { createdAt: -1 } },

      // Stage 11: Pagination with facet
      {
        $facet: {
          data: [
            { $skip: skip },
            { $limit: limit }
          ],
          totalCount: [
            { $count: "count" }
          ]
        }
      }
    ];

    const result = await Boat.aggregate(pipeline);

    // Handle empty result or malformed aggregation result
    if (!result || !Array.isArray(result) || result.length === 0) {
      // Calculate total earnings even for empty results if affiliate is provided
      let totalEarnings = 0;
      if (affiliate && affiliate.status === AffiliateStatus.Approved && affiliate.affiliateCode) {
        try {

          const earningsResult = await Payment.aggregate([
            {
              $match: {
                affiliateId: affiliate._id,
                status: "completed"
              }
            },
            {
              $group: {
                _id: null,
                totalAmount: { $sum: "$affiliateFee" }
              }
            }
          ]);

          totalEarnings = earningsResult.length > 0 ? earningsResult[0].totalAmount : 0;
        } catch (error) {
          console.error("Error calculating total earnings:", error);
          // Continue without earnings data
        }
      }

      return res.status(200).json({
        success: true,
        message: req.__("AFFILIATE_BOATS_FETCH_SUCCESS"),
        data: {
          affiliate: affiliate ? {
            _id: affiliate._id,
            name: affiliate.name,
            email: affiliate.email,
            affiliateCode: affiliate.affiliateCode,
            status: affiliate.status,
            totalEarnings: totalEarnings
          } : null,
          boats: [],
          pagination: {
            page: Number(page),
            size: Number(limit),
            totalCount: 0,
            totalPages: 0
          },
        },
      });
    }
    // console.log("Aggregation result:", JSON.stringify(result, null, 2));

    // Safely extract aggregation result with proper null checks
    const aggregationResult = result[0] || {};
    const boatsAndActivities = Array.isArray(aggregationResult.data) ? aggregationResult.data : [];

    // Safely extract total count with multiple fallbacks
    let totalCount = 0;
    if (aggregationResult.totalCount && Array.isArray(aggregationResult.totalCount) && aggregationResult.totalCount.length > 0) {
      totalCount = aggregationResult.totalCount[0]?.count || 0;
    }

    // Calculate total earnings for this affiliate if affiliate is provided
    let totalEarnings = 0;
    if (affiliate && affiliate.status === AffiliateStatus.Approved && affiliate.affiliateCode) {
      try {
        const Payment = require("../../../payment-ms/src/models/Payment").default;

        const earningsResult = await Payment.aggregate([
          {
            $match: {
              affiliateId: affiliate._id,
              status: "completed"
            }
          },
          {
            $group: {
              _id: null,
              totalAmount: { $sum: "$affiliateFee" }
            }
          }
        ]);

        totalEarnings = earningsResult.length > 0 ? earningsResult[0].totalAmount : 0;
      } catch (error) {
        console.error("Error calculating total earnings:", error);
        // Continue without earnings data
      }
    }

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_BOATS_FETCH_SUCCESS"),
      data: {
        affiliate: affiliate ? {
          _id: affiliate._id,
          name: affiliate.name,
          email: affiliate.email,
          affiliateCode: affiliate.affiliateCode,
          status: affiliate.status,
          totalEarnings: totalEarnings
        } : null,
        boats: boatsAndActivities,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: totalCount,
          totalPages: Math.ceil(totalCount / Number(limit))
        },
      },
    });

  } catch (error: any) {
    console.error("Error in getAffiliateBoats:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error: error.message
    });
  }
};

// Admin API: Get boat details with bookings and earnings for specific affiliate (Screen 218)
export const getAffiliateBoatDetails = async (req: any, res: any) => {
  try {
    const { id, boatId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Validate boatId parameter
    if (!boatId || !mongoose.Types.ObjectId.isValid(boatId)) {
      return res.status(400).json({
        success: false,
        message: req.__("INVALID_BOAT_ID"),
      });
    }

    // Find the affiliate by ID
    const affiliate = await Affiliate.findById(id);
    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_FOUND"),
      });
    }

    if (affiliate.status !== AffiliateStatus.Approved || !affiliate.affiliateCode) {
      return res.status(400).json({
        success: false,
        message: req.__("AFFILIATE_NOT_APPROVED"),
      });
    }

    // Import Booking model
    const Booking = require("../../../booking-ms/src/models/Booking").default;
    const Boat = require("../../../boat-ms/src/models/Boat").default;

    // Get boat details with comprehensive information
    const boatPipeline = [
      {
        $match: { _id: new mongoose.Types.ObjectId(boatId) }
      },
      {
        $lookup: {
          from: "users",
          localField: "ownerId",
          foreignField: "_id",
          as: "owner"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "attachments.images",
          foreignField: "_id",
          as: "imageFiles"
        }
      },
      {
        $unwind: { path: "$owner", preserveNullAndEmptyArrays: true }
      },
      {
        $addFields: {
          images: {
            $map: {
              input: "$imageFiles",
              as: "img",
              in: {
                id: "$$img._id",
                link: { $concat: [global.config.FILE_BASE_URL, "$$img.location"] }
              }
            }
          },
          ownerInfo: {
            id: "$owner._id",
            name: "$owner.username",
            email: "$owner.email",
            phone: "$owner.phoneNo"
          }
        }
      }
    ];

    const boatResult = await Boat.aggregate(boatPipeline);

    // Safely extract boat result with proper null checks
    if (!boatResult || !Array.isArray(boatResult) || boatResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: req.__("BOAT_NOT_FOUND"),
      });
    }

    const boat = boatResult[0];

    if (!boat) {
      return res.status(404).json({
        success: false,
        message: req.__("BOAT_NOT_FOUND"),
      });
    }

    // Verify this boat uses the affiliate's code
    if (boat.affiliateCode !== affiliate.affiliateCode) {
      return res.status(403).json({
        success: false,
        message: req.__("BOAT_NOT_LINKED_TO_AFFILIATE"),
      });
    }

    // Get commission rates to calculate affiliate commission
    const CommissionRate = require("../../../booking-ms/src/models/CommissionRate").default;
    const commissionRate = await CommissionRate.findOne({ status: "active" });
    const affiliateRatePercentage = commissionRate ? commissionRate.affiliateRate : 5; // Default 5%

    // Get bookings for this boat with affiliate commission details
    const pipeline = [
      {
        $match: {
          boatId: boat._id,
          affiliateCode: affiliate.affiliateCode,
          status: { $nin: ["Cancelled", "Rejected"] }
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "user"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "user.avatar",
          foreignField: "_id",
          as: "userAvatar"
        }
      },
      {
        $unwind: { path: "$user", preserveNullAndEmptyArrays: true }
      },
      {
        $unwind: { path: "$userAvatar", preserveNullAndEmptyArrays: true }
      },
      {
        $addFields: {
          // Calculate affiliate commission dynamically
          affiliateCommissionAmount: { $multiply: ["$totalAmount", affiliateRatePercentage / 100] },
          affiliateCommissionPercentage: affiliateRatePercentage,
          userProfile: {
            id: "$user._id",
            name: "$user.username",
            email: "$user.email",
            phone: "$user.phoneNo",
            avatar: {
              $cond: {
                if: "$userAvatar",
                then: {
                  id: "$userAvatar._id",
                  link: { $concat: [global.config.FILE_BASE_URL, "$userAvatar.location"] }
                },
                else: null
              }
            }
          }
        }
      },
      {
        $project: {
          _id: 1,
          userProfile: 1,
          bookedTime: {
            startDate: "$startDate",
            endDate: "$endDate"
          },
          duration: 1,
          patronType: 1,
          location: 1,
          bookingPrice: "$totalAmount",
          adminFee: 1,
          netAmount: 1,
          currency: 1,
          affiliateCommission: {
            amount: "$affiliateCommissionAmount",
            percentage: "$affiliateCommissionPercentage"
          },
          status: 1,
          createdAt: 1,
          updatedAt: 1
        }
      },
      { $sort: { createdAt: -1 }},
      {
        $facet: {
          data: [
            { $skip: skip },
            { $limit: limit }
          ],
          metadata: [
            { $count: "total" }
          ],
          totalEarnings: [
            {
              $group: {
                _id: null,
                totalAmount: { $sum: "$affiliateCommissionAmount" }
              }
            }
          ]
        }
      }
    ];

    const result = await Booking.aggregate(pipeline);

    // Safely extract aggregation result with proper null checks
    if (!result || !Array.isArray(result) || result.length === 0) {
      return res.status(200).json({
        success: true,
        message: req.__("AFFILIATE_BOAT_DETAILS_FETCH_SUCCESS"),
        data: {
          affiliate: {
            _id: affiliate._id,
            name: affiliate.name,
            email: affiliate.email,
            affiliateCode: affiliate.affiliateCode,
          },
          boat: {
            _id: boat._id,
            name: boat.name,
            type: boat.type,
            guestsCapacity: boat.guestsCapacity,
            cabins: boat.cabins,
            baths: boat.baths,
            pricePerDay: boat.pricePerDay,
            fullDayWithPatron: boat.fullDayWithPatron,
            fullDayWithoutPatron: boat.fullDayWithoutPatron,
            halfDayWithPatron: boat.halfDayWithPatron,
            halfDayWithoutPatron: boat.halfDayWithoutPatron,
            description: boat.description,
            location: boat.location,
            lat: boat.lat,
            lng: boat.lng,
            fuel: boat.fuel,
            patron: boat.patron,
            license: boat.license,
            language: boat.language,
            images: boat.images || [],
            ownerInfo: boat.ownerInfo,
            affiliateCode: boat.affiliateCode,
          },
          totalBenefitAmount: 0,
          bookings: [],
          pagination: {
            page: Number(page),
            size: Number(limit),
            totalCount: 0,
            totalPages: 0
          },
        },
      });
    }

    const aggregationResult = result[0] || {};
    const bookings = Array.isArray(aggregationResult.data) ? aggregationResult.data : [];

    // Safely extract total count
    let totalCount = 0;
    if (aggregationResult.metadata && Array.isArray(aggregationResult.metadata) && aggregationResult.metadata.length > 0) {
      totalCount = aggregationResult.metadata[0]?.total || 0;
    }

    // Safely extract total earnings
    let totalEarnings = 0;
    if (aggregationResult.totalEarnings && Array.isArray(aggregationResult.totalEarnings) && aggregationResult.totalEarnings.length > 0) {
      totalEarnings = aggregationResult.totalEarnings[0]?.totalAmount || 0;
    }

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_BOAT_DETAILS_FETCH_SUCCESS"),
      data: {
        affiliate: {
          _id: affiliate._id,
          name: affiliate.name,
          email: affiliate.email,
          affiliateCode: affiliate.affiliateCode,
        },
        boat: {
          _id: boat._id,
          name: boat.name,
          type: boat.type,
          guestsCapacity: boat.guestsCapacity,
          cabins: boat.cabins,
          baths: boat.baths,
          pricePerDay: boat.pricePerDay,
          fullDayWithPatron: boat.fullDayWithPatron,
          fullDayWithoutPatron: boat.fullDayWithoutPatron,
          halfDayWithPatron: boat.halfDayWithPatron,
          halfDayWithoutPatron: boat.halfDayWithoutPatron,
          description: boat.description,
          location: boat.location,
          lat: boat.lat,
          lng: boat.lng,
          fuel: boat.fuel,
          patron: boat.patron,
          license: boat.license,
          language: boat.language,
          images: boat.images || [],
          ownerInfo: boat.ownerInfo,
          affiliateCode: boat.affiliateCode,
        },
        totalBenefitAmount: totalEarnings,
        bookings,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount,
          totalPages: Math.ceil(totalCount / Number(limit))
        },
      },
    });
  } catch (error) {
    console.error("Error fetching affiliate boat details:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};

// Admin API: Get total earnings for a specific affiliate
export const getAffiliateEarnings = async (req: any, res: any) => {
  try {
    const { id } = req.params;

    // Find the affiliate by ID
    const affiliate = await Affiliate.findById(id);
    if (!affiliate) {
      return res.status(404).json({
        success: false,
        message: req.__("AFFILIATE_NOT_FOUND"),
      });
    }

    if (affiliate.status !== AffiliateStatus.Approved || !affiliate.affiliateCode) {
      return res.status(200).json({
        success: true,
        message: req.__("AFFILIATE_EARNINGS_FETCH_SUCCESS"),
        data: {
          affiliate: {
            _id: affiliate._id,
            name: affiliate.name,
            email: affiliate.email,
            affiliateCode: affiliate.affiliateCode,
            status: affiliate.status,
          },
          totalEarnings: 0,
          totalBookings: 0,
          earningsByBoat: [],
        },
      });
    }

    // Import Booking model and get commission rates
    const Booking = require("../../../booking-ms/src/models/Booking").default;
    const CommissionRate = require("../../../booking-ms/src/models/CommissionRate").default;
    const commissionRate = await CommissionRate.findOne({ status: "active" });
    const affiliateRatePercentage = commissionRate ? commissionRate.affiliateRate : 5; // Default 5%

    // Get total earnings and breakdown by boat
    const pipeline = [
      {
        $match: {
          affiliateCode: affiliate.affiliateCode,
          status: { $nin: ["Cancelled", "Rejected"] }
        }
      },
      {
        $lookup: {
          from: "boats",
          localField: "boatId",
          foreignField: "_id",
          as: "boat"
        }
      },
      {
        $unwind: { path: "$boat", preserveNullAndEmptyArrays: true }
      },
      {
        $addFields: {
          // Calculate affiliate commission dynamically
          affiliateCommissionAmount: { $multiply: ["$totalAmount", affiliateRatePercentage / 100] }
        }
      },
      {
        $group: {
          _id: "$boatId",
          boatName: { $first: "$boat.name" },
          boatType: { $first: "$boat.type" },
          boatLocation: { $first: "$boat.location" },
          boatImage: { $first: "$boat.image" },
          totalEarnings: { $sum: "$affiliateCommissionAmount" },
          totalBookings: { $sum: 1 },
          averageCommission: { $avg: "$affiliateCommissionAmount" }
        }
      },
      {
        $sort: { totalEarnings: -1 }
      }
    ];

    const earningsByBoat = await Booking.aggregate(pipeline);

    // Calculate total earnings across all boats
    const totalEarnings = earningsByBoat.reduce((sum: number, boat: any) => sum + (boat.totalEarnings || 0), 0);
    const totalBookings = earningsByBoat.reduce((sum: number, boat: any) => sum + (boat.totalBookings || 0), 0);

    return res.status(200).json({
      success: true,
      message: req.__("AFFILIATE_EARNINGS_FETCH_SUCCESS"),
      data: {
        affiliate: {
          _id: affiliate._id,
          name: affiliate.name,
          email: affiliate.email,
          affiliateCode: affiliate.affiliateCode,
          status: affiliate.status,
        },
        totalEarnings: Math.round(totalEarnings * 100) / 100, // Round to 2 decimal places
        totalBookings,
        earningsByBoat: earningsByBoat.map((boat: any) => ({
          boatId: boat._id,
          boatName: boat.boatName,
          boatType: boat.boatType,
          boatLocation: boat.boatLocation,
          boatImage: boat.boatImage,
          totalEarnings: Math.round(boat.totalEarnings * 100) / 100,
          totalBookings: boat.totalBookings,
          averageCommission: Math.round(boat.averageCommission * 100) / 100,
        })),
      },
    });
  } catch (error) {
    console.error("Error fetching affiliate earnings:", error);
    return res.status(500).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
    });
  }
};
