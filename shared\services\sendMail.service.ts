import nodemailer from "nodemailer";
import handlebars from "handlebars";
import { addMail, readHTMLFile } from "../helper/common";
import path from "path";
import { mailStatus } from "../models/Mail";

const sendEmail = async function (
  to: any,
  subject: any,
  template: any,
  from = global.config.SMTP_EMAIL,
  data: any,
  attachments?: any,
) {
  try {
    let transporter = null;
    if (
      typeof global.config.IS_EMAIL_USE_SMTP !== "undefined" &&
      global.config.IS_EMAIL_USE_SMTP == "on"
    ) {
      transporter = nodemailer.createTransport({
        host: global.config.SMTP_HOST,
        port: global.config.SMTP_PORT,
        secure: global.config.SMTP_PORT == 465 ? true : false,
        auth: {
          user: global.config.SMTP_USER,
          pass: global.config.SMTP_PASSWORD,
        },
      });
    } else {
      transporter = nodemailer.createTransport({
        sendmail: true,
        newline: "unix",
        path: "/usr/sbin/sendmail",
      });
    }

    const mailOptions: any = {
      from: from,
      to: to,
      subject: `${subject} ${
        process.env.NODE_ENV === undefined
          ? "(development)"
          : process.env.NODE_ENV !== "production"
            ? `(${process.env.NODE_ENV})`
            : ""
      }`,
      html: template,
    };

    const mail_detail: any = {
      mailSubject: mailOptions.subject,
      mailTo: mailOptions.to,
      mailFrom: mailOptions.from,
      mailBody: JSON.stringify(data),
    };
    if (attachments && attachments.length > 0) {
      mailOptions.attachments = attachments;
    }
    if (to && to != "") {
      return await transporter.sendMail(
        mailOptions,
        async (error: any, info: any) => {
          console.log("error", error);
          console.log("info", info);
          if (error) {
            mail_detail.mailStatus = mailStatus.FAILED;
            mail_detail.mailResponse = JSON.stringify(error);
            await addMail(mail_detail);
            console.log("\n if Email fail ==> ", error);
            // const templateData = {
            //   message: error.message ? error.message : "",
            // };
          } else if (info) {
            mail_detail.mailResponse = JSON.stringify(info);
            mail_detail.mailStatus = mailStatus.SENT;
            await addMail(mail_detail);
          }
        },
      );
    } else {
      return;
    }
  } catch (e) {
    console.log("\nEmail failed catch ==> ", e);
  }
};

const emailSender = (
  to: string,
  subject: string,
  data: any,
  template: any,
  attachments?: any,
) => {
  readHTMLFile(
    path.join(__dirname, `../../shared/email_templates/${template}.html`),
    async function (err: any, html: any) {
      try {
        const compiledTemplate = handlebars.compile(html);
        const htmlToSend = compiledTemplate(data);
        await sendEmail(
          to,
          subject,
          htmlToSend,
          global.config.FROM_EMAIL,
          data,
          attachments,
        );
      } catch (e) {
        console.log("error", e);
      }
    },
  );
};

export { emailSender };
