import { celebrate, Joi, Segments } from "celebrate";
import { BoatStatus, BoatType, DayDuration } from "../models/Boat";

const scheduleTimeSchema = Joi.object().keys({
  day: Joi.string().required(),
  duration: Joi.string()
    .valid(...Object.values(DayDuration))
    .required(),
  startTime: Joi.string()
    .required()
    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9] (AM|PM)$/),
  endTime: Joi.string()
    .required()
    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9] (AM|PM)$/),
});

export const createBoatValidatorBody = Joi.object().keys({
  name: Joi.string().required(),
  type: Joi.string()
    .required(),
  guestsCapacity: Joi.number().required().min(1),
  cabins: Joi.number().required().min(0),
  baths: Joi.number().required().min(0),
  pricePerDay: Joi.number().required().min(0),
  visualizationUrl: Joi.string().uri().optional().allow(null, ""),
  description: Joi.string().optional().allow(null, ""),
  fullDayWithPatron: Joi.number().required().min(0),
  fullDayWithoutPatron: Joi.number().required().min(0),
  halfDayWithPatron: Joi.number().required().min(0),
  halfDayWithoutPatron: Joi.number().required().min(0),
  affiliateCode: Joi.string().optional().allow(null, ""),
  language: Joi.array().items(Joi.string()).required(),
  fuel: Joi.boolean().required(),
  patron: Joi.boolean().required(),
  license: Joi.boolean().required(),
  schedule: Joi.array().items(scheduleTimeSchema).min(1).required(),
  checkInNotes: Joi.string().optional().allow(null, ""),
  checkOutNotes: Joi.string().optional().allow(null, ""),
  location: Joi.string().required(),
  lat: Joi.number().required(),
  lng: Joi.number().required(),
  recommendedPlaces: Joi.array().items(Joi.object()).optional(),
  availability: Joi.object()
    .keys({
      start: Joi.date().iso().required(),
      end: Joi.date().iso().min(Joi.ref("start")).required(),
    })
    .required(),
  facilities: Joi.array().items(Joi.object()).optional(),
  status: Joi.string()
    .valid(...Object.values(BoatStatus))
    .optional(),
  documents: Joi.array().items(Joi.string()).optional(),
  images: Joi.any(),
  videos: Joi.any(),
});

export const createBoatValidator = celebrate({
  [Segments.BODY]: createBoatValidatorBody,
});

export const updateBoatValidatorBody = Joi.object().keys({
  name: Joi.string().optional(),
  type: Joi.string()
    .optional(),
  guestsCapacity: Joi.number().optional().min(1),
  cabins: Joi.number().optional().min(0),
  baths: Joi.number().optional().min(0),
  pricePerDay: Joi.number().optional().min(0),
  visualizationUrl: Joi.string().uri().optional().allow(null, ""),
  description: Joi.string().optional().allow(null, ""),
  fullDayWithPatron: Joi.number().optional().min(0),
  fullDayWithoutPatron: Joi.number().optional().min(0),
  halfDayWithPatron: Joi.number().optional().min(0),
  halfDayWithoutPatron: Joi.number().optional().min(0),
  affiliateCode: Joi.string().optional().allow(null, ""),
  language: Joi.array().items(Joi.string()).optional(),
  fuel: Joi.boolean().optional(),
  patron: Joi.boolean().optional(),
  license: Joi.boolean().optional(),
  schedule: Joi.array().items(scheduleTimeSchema).min(1).optional(),
  checkInNotes: Joi.string().optional().allow(null, ""),
  checkOutNotes: Joi.string().optional().allow(null, ""),
  location: Joi.string().optional(),
  lat: Joi.number().optional(),
  lng: Joi.number().optional(),
  recommendedPlaces: Joi.array().optional().allow(null),
  availability: Joi.object()
    .keys({
      start: Joi.date().iso().required(),
      end: Joi.date().iso().min(Joi.ref("start")).required(),
    })
    .optional(),
  facilities: Joi.array().optional().allow(null),
  status: Joi.string()
    .valid(...Object.values(BoatStatus))
    .optional(),
  documents: Joi.array().items(Joi.string()).optional().allow(null, ""),
  images: Joi.any().optional().allow(null, ""),
  videos: Joi.any().optional().allow(null, ""),
  existingImages: Joi.array().items(Joi.string()).allow(null),
  existingVideos: Joi.array().items(Joi.string()).allow(null),
});

export const updateBoatValidator = celebrate({
  [Segments.BODY]: updateBoatValidatorBody,
});

export const uploadAttachmentValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string().required(),
    type: Joi.string().valid("images", "videos").required(),
  }),
});

export const setAvailabilityValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    start: Joi.date().iso().required(),
    end: Joi.date().iso().min(Joi.ref("start")).required(),
  }),
});

export const idParamValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    id: Joi.string().required(),
  }),
});

export const addScheduleTimeValidator = celebrate({
  [Segments.BODY]: scheduleTimeSchema,
});
