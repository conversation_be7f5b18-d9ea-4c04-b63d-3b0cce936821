import { StatusCodes } from "http-status-codes";
import Payment, { PaymentMethod, PaymentStatus } from "../models/Payment";
import mongoose from "mongoose";
import logger from "../../../shared/services/logger.service";
import {
  createActivityLog,
  validateAndGetChanges,
} from "../../../shared/models/ActivityLog";
import StripeService from "../services/stripe.service";
import Booking, { BookingStatus } from "../../../booking-ms/src/models/Booking";
import Wallet from "../../../wallet-ms/src/models/Wallet";
import Transaction, {
  TransactionStatus,
  TransactionType,
  TransactionSource,
} from "../../../wallet-ms/src/models/Transaction";
import User, { UserCurrency } from "../../../user-ms/src/models/User";
import Affiliate from "../../../affiliate-ms/src/models/Affiliate";
import CommissionRate from "../../../booking-ms/src/models/CommissionRate";
import Card, { CardStatus } from "../../../card-ms/src/models/Card";
import "../../../boat-ms/src/models/Boat"; // Import Boat model schema
import {
  redeemPoints,
  checkWelcomeDiscounts,
  useWelcomeDiscount as applyWelcomeDiscount
} from "../../../shared/services/reward.service";

// Currency conversion rates (in a real app, these would come from an API)
const CURRENCY_CONVERSION_RATES = {
  [UserCurrency.USD]: 1.0,     // Base currency
  [UserCurrency.GBP]: 0.78,    // 1 USD = 0.78 GBP
  [UserCurrency.EUR]: 0.92,    // 1 USD = 0.92 EUR
};

/**
 * Convert amount from one currency to another
 * @param amount The amount to convert
 * @param fromCurrency The source currency
 * @param toCurrency The target currency
 * @returns The converted amount
 */
const convertCurrency = (
  amount: number,
  fromCurrency: UserCurrency,
  toCurrency: UserCurrency
): number => {
  if (fromCurrency === toCurrency) {
    return amount;
  }

  // Convert to USD first (as base currency)
  const amountInUSD = 
    fromCurrency === UserCurrency.USD 
      ? amount 
      : amount / CURRENCY_CONVERSION_RATES[fromCurrency];

  // Then convert from USD to target currency
  const convertedAmount = 
    toCurrency === UserCurrency.USD 
      ? amountInUSD 
      : amountInUSD * CURRENCY_CONVERSION_RATES[toCurrency];

  return parseFloat(convertedAmount.toFixed(2));
};

// Create payment intent for booking
export const createPaymentIntent = async (req: any, res: any) => {
  try {
    const { bookingId, usePoints = 0, useWelcomeDiscount = false } = req.body;
    const userId = req.user._id;

    // Validate booking exists and belongs to user
    const booking = await Booking.findOne({
      _id: bookingId,
      userId: userId,
      status: { $in: [BookingStatus.Pending, BookingStatus.ReadyForPayment] }
    }).populate('boatId', 'name ownerId');

    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    // Check if payment intent already exists for this booking
    if (booking.paymentIntentId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("PAYMENT_INTENT_ALREADY_EXISTS"),
        data: {
          paymentIntentId: booking.paymentIntentId,
          clientSecret: booking.paymentIntentClientSecret,
          stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
        }
      });
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("USER_NOT_FOUND"),
      });
    }

    // Calculate final amount with discounts
    let totalAmount = booking.totalAmount;
    let discountAmount = 0;
    const originalAmount = totalAmount;

    // Apply reward points if requested
    if (usePoints > 0) {
      try {
        await redeemPoints(
          userId,
          usePoints,
          bookingId,
          `Points redeemed for booking ${bookingId}`
        );
        discountAmount += usePoints; // Each point is worth 1 USD/EUR
      } catch (error: any) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: error.message,
        });
      }
    }

    // Apply welcome discount if requested
    if (useWelcomeDiscount) {
      try {
        const { available } = await checkWelcomeDiscounts(userId);
        if (available > 0) {
          // Actually use the welcome discount
          await applyWelcomeDiscount(userId, bookingId);
          const welcomeDiscountAmount = originalAmount * 0.1; // 10% discount
          discountAmount += welcomeDiscountAmount;
        } else {
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: req.__("WELCOME_DISCOUNT_NOT_AVAILABLE"),
          });
        }
      } catch (error: any) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: error.message,
        });
      }
    }

    // Apply total discount
    totalAmount -= discountAmount;

    // Ensure minimum amount
    if (totalAmount < 0.50) { // Stripe minimum is $0.50
      totalAmount = 0.50;
    }

    // Get or create user's Stripe customer ID
    let stripeCustomerId = user.stripeCustomerId;

    if (!stripeCustomerId) {
      const customerResult = await StripeService.createCustomer({
        email: user.email,
        name: user.name || user.username || user.email,
        metadata: {
          userId: userId.toString(),
        },
      });

      if (!customerResult.success) {
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: req.__("STRIPE_CUSTOMER_CREATION_FAILED"),
        });
      }

      stripeCustomerId = customerResult.customer!.id;

      // Update user with Stripe customer ID
      await User.findByIdAndUpdate(userId, { stripeCustomerId });
    }

    // Prepare metadata for payment intent
    const paymentMetadata = {
      bookingId: bookingId,
      userId: userId.toString(),
      boatId: booking.boatId._id.toString(),
      boatOwnerId: booking.boatId.ownerId.toString(),
      originalAmount: originalAmount.toString(),
      discountAmount: discountAmount.toString(),
      finalAmount: totalAmount.toString(),
      currency: booking.currency || 'usd',
      usePoints: usePoints.toString(),
      useWelcomeDiscount: useWelcomeDiscount.toString(),
    };

    // Create payment intent with hold capture method
    const paymentIntentResult = await StripeService.createPaymentIntent(
      totalAmount,
      booking.currency || 'usd',
      {
        customerId: stripeCustomerId,
        description: `Sea Escape booking payment for ${booking.boatId.name}`,
        metadata: paymentMetadata,
        captureMethod: 'manual', // Hold the payment
      }
    );

    if (!paymentIntentResult.success) {
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: req.__("PAYMENT_INTENT_CREATION_FAILED"),
        error: paymentIntentResult.error,
      });
    }

    // Update booking with payment intent details and discount info
    await Booking.findByIdAndUpdate(bookingId, {
      paymentIntentId: paymentIntentResult.paymentIntent!.id,
      paymentIntentStatus: paymentIntentResult.paymentIntent!.status,
      paymentIntentClientSecret: paymentIntentResult.clientSecret,
      status: BookingStatus.ReadyForPayment,
      // Store discount information in booking for later use
      totalAmount: totalAmount, // Update with discounted amount
    });

    // Log the payment intent creation
    const changes = validateAndGetChanges({}, {
      paymentIntentId: paymentIntentResult.paymentIntent!.id,
      paymentIntentStatus: paymentIntentResult.paymentIntent!.status,
      status: BookingStatus.ReadyForPayment,
      discountApplied: discountAmount,
    });
    await createActivityLog("bookings", bookingId, "UPDATE", changes, userId);

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: req.__("PAYMENT_INTENT_CREATED_SUCCESS"),
      data: {
        paymentIntentId: paymentIntentResult.paymentIntent!.id,
        clientSecret: paymentIntentResult.clientSecret,
        stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
        originalAmount: originalAmount,
        discountAmount: discountAmount,
        finalAmount: totalAmount,
        currency: booking.currency || 'usd',
        status: paymentIntentResult.paymentIntent!.status,
        metadata: paymentMetadata,
      },
    });
  } catch (error) {
    logger.error("Error creating payment intent", {
      service: "payment-ms",
      error: error,
      userId: req.user._id,
      bookingId: req.body.bookingId,
    });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Process payment for a booking
export const processPayment = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const {
      bookingId,
      paymentMethod,
      cardId,
      usePoints = 0,               // NEW: Points to use for discount
      useWelcomeDiscount = false   // NEW: Whether to apply welcome discount
    } = req.body;

    // Validate payment method
    if (!Object.values(PaymentMethod).includes(paymentMethod)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("PAYMENT_INVALID_METHOD"),
      });
    }

    // Get user details including currency preference
    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("USER_PROFILE_FETCH_FAILED"),
      });
    }

    const userCurrency = user.currency as UserCurrency;
    if (!Object.values(UserCurrency).includes(userCurrency)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("INVALID_CURRENCY"),
      });
    }

    // Check if booking exists and is ready for payment
    const booking = await Booking.findById(bookingId).populate("boatId");

    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    if (booking.status !== BookingStatus.ReadyForPayment) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("BOOKING_NOT_READY_FOR_PAYMENT"),
      });
    }

    if (booking.userId.toString() !== userId.toString()) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: req.__("UNAUTHORIZED_BOOKING_PAYMENT"),
      });
    }

    // Extract boat details and boat owner ID
    const boatDetails = booking.boatId as unknown as {
      ownerId: mongoose.Types.ObjectId;
      affiliateCode?: string;
    };

    const boatOwnerId = boatDetails?.ownerId || null;

    if (!boatOwnerId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("BOAT_OWNER_NOT_FOUND"),
      });
    }

    // Get boat owner details
    const boatOwner = await User.findById(boatOwnerId);
    if (!boatOwner) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("BOAT_OWNER_NOT_FOUND"),
      });
    }

    const ownerCurrency = boatOwner.currency as UserCurrency || UserCurrency.USD;

    // Get the booking currency information
    const bookingCurrency = (booking.currency as UserCurrency) || userCurrency;
    const storedOwnerCurrency = (booking.ownerCurrency as UserCurrency) || ownerCurrency;

    // Handle currency differences if needed
    let totalAmount = booking.totalAmount;
    let adminFee = booking.adminFee;
    let ownerAmount = booking.netAmount;
    let affiliateFee = 0;

    // If user's current currency differs from the booking currency, convert amounts
    if (userCurrency !== bookingCurrency) {
      totalAmount = convertCurrency(totalAmount, bookingCurrency, userCurrency);
      adminFee = convertCurrency(adminFee, bookingCurrency, userCurrency);
      ownerAmount = convertCurrency(ownerAmount, bookingCurrency, userCurrency);
    }

    // NEW: Apply rewards and discounts if requested
    let discountAmount = 0;
    let originalTotalAmount = totalAmount;

    // Apply reward points if requested
    if (usePoints > 0) {
      try {
        await redeemPoints(
          userId,
          usePoints,
          bookingId,
          `Points redeemed for booking ${bookingId}`
        );
        discountAmount += usePoints; // Each point is worth 1 USD/EUR
      } catch (error: any) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: error.message,
        });
      }
    }

    // Apply welcome discount if requested
    if (useWelcomeDiscount) {
      try {
        const { available } = await checkWelcomeDiscounts(userId);
        if (available > 0) {
          await useWelcomeDiscount(userId, bookingId);
          const welcomeDiscountAmount = originalTotalAmount * 0.1; // 10% discount
          discountAmount += welcomeDiscountAmount;
        }
      } catch (error: any) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: error.message,
        });
      }
    }

    // Apply total discount
    totalAmount -= discountAmount;

    // Get commission rates
    const commissionRates = await CommissionRate.findOne({});

    const affiliateFeePercentage = commissionRates?.affiliateRate || 0;
    let affiliateId = null;
    let referralName = null;

    // First check if booking has referralName
    if (booking.referralName) {
      referralName = booking.referralName;
    } else {
      // If no referralName in booking, check boat's affiliateCode
      if (boatDetails && boatDetails.affiliateCode) {
        referralName = boatDetails.affiliateCode;
      }
    }

    // If we have a referral name from either source, process affiliate commission
    if (referralName) {
      try {
        const affiliate = await Affiliate.findOne({
          affiliateCode: referralName,
        });

        if (affiliate && affiliate._id) {
          affiliateId = affiliate._id;
          affiliateFee = affiliateFeePercentage
            ? (totalAmount * affiliateFeePercentage) / 100
            : 0;

          // Subtract affiliate fee from owner amount
          ownerAmount -= affiliateFee;
        }
      } catch (error) {
        logger.error("Error fetching affiliate", {
          service: "payment-ms",
          referralName,
          error,
        });
        // Continue even if affiliate lookup fails
      }
    }

    // Process payment based on payment method
    let paymentReference = null;
    let transactionDetails = null;

    if (paymentMethod === PaymentMethod.WALLET) {
      // Process wallet payment
      const userWallet = await Wallet.findOne({ userId });

      if (!userWallet) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: req.__("WALLET_NOT_FOUND"),
        });
      }

      if (userWallet.balance < totalAmount) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: req.__("WALLET_PAYMENT_FAILED_INSUFFICIENT_BALANCE"),
        });
      }

      // Create transaction record
      const transaction = new Transaction({
        userId,
        amount: totalAmount,
        type: TransactionType.DEBIT,
        status: TransactionStatus.COMPLETED,
        source: TransactionSource.BOOKING,
        sourceId: booking._id,
        description: `Payment for booking #${bookingId}`,
        currency: userCurrency, // Add currency to transaction
      });

      await transaction.save();

      // Update wallet balance
      userWallet.balance -= totalAmount;
      await userWallet.save();

      paymentReference = transaction._id ? transaction._id.toString() : null;
    } else if (paymentMethod === PaymentMethod.CARD) {
      let selectedCard;

      // Method 1: If a specific card ID is provided, use that
      if (cardId) {
        selectedCard = await Card.findOne({
          _id: cardId,
          ownerId: userId,
          status: CardStatus.ACTIVE,
        });

        if (!selectedCard) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: req.__("CARD_NOT_FOUND"),
          });
        }
      }
      // Method 2: Otherwise, find the default active card
      else {
        selectedCard = await Card.findOne({
          ownerId: userId,
          isDefault: true,
          status: CardStatus.ACTIVE,
        });

        if (!selectedCard) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: req.__("DEFAULT_CARD_NOT_FOUND"),
          });
        }
      }

      // Process payment with Stripe using saved card source
      const paymentResult = await StripeService.processPaymentWithSource(
        totalAmount,
        selectedCard.stripeCustomerId,
        selectedCard.stripeSourceId,
        {
          currency: userCurrency.toLowerCase(),
          description: `Sea Escape booking payment for booking ${bookingId}`,
          metadata: {
            bookingId: bookingId.toString(),
            userCurrency: userCurrency,
            bookingCurrency: bookingCurrency,
            ownerCurrency: storedOwnerCurrency,
            cardId: (selectedCard._id as any).toString(),
            sourceId: selectedCard.stripeSourceId,
          },
        },
      );



      if (!paymentResult.success) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: paymentResult.error || req.__("CARD_PAYMENT_FAILED"),
        });
      }

      // Store Stripe payment intent info
      transactionDetails = paymentResult.paymentIntent;
      paymentReference = transactionDetails?.id || null;
    } else {
      // Unsupported payment method
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: req.__("PAYMENT_METHOD_NOT_SUPPORTED"),
      });
    }

    // Create payment record
    const payment = new Payment({
      bookingId,
      userId,
      boatOwnerId,
      amount: totalAmount,
      adminFee,
      ownerAmount, // Amount after deducting admin and affiliate fees
      affiliateId,
      affiliateFee,
      paymentMethod,
      status: PaymentStatus.COMPLETED,
      paymentReference,
      transactionDetails,
      currency: userCurrency, // Store the currency used for payment
      originalCurrency: bookingCurrency, // Store the original booking currency
      ownerCurrency: storedOwnerCurrency, // Store the boat owner's currency
      // NEW: Add reward fields
      originalAmount: originalTotalAmount,
      discountAmount,
      pointsUsed: usePoints,
      welcomeDiscountApplied: useWelcomeDiscount
    });

    await payment.save();

    // Update the booking
    booking.status = BookingStatus.Accepted;
    booking.paymentId = payment._id as mongoose.Types.ObjectId;
    await booking.save();

    // Log the payment
    const changes = validateAndGetChanges({}, payment.toJSON());
    await createActivityLog("payments", payment._id, "CREATE", changes, userId);

    // Log the booking update
    const bookingChanges = validateAndGetChanges(
      { status: BookingStatus.ReadyForPayment },
      { status: BookingStatus.Accepted },
    );
    await createActivityLog(
      "bookings",
      booking._id,
      "UPDATE",
      bookingChanges,
      userId,
    );

    // Credit boat owner's wallet (converting to their currency if different)
    // The ownerAmount is in the user's currency, so convert to owner's currency
    let ownerAmountInOwnerCurrency = ownerAmount;
    if (userCurrency !== ownerCurrency) {
      ownerAmountInOwnerCurrency = convertCurrency(ownerAmount, userCurrency, ownerCurrency);
    }

    // Find or create owner's wallet and add the payment
    let ownerWallet = await Wallet.findOne({ userId: boatOwnerId });
    if (!ownerWallet) {
      ownerWallet = new Wallet({
        userId: boatOwnerId,
        balance: 0,
        status: "active",
      });
    }

    // Add the payment to the owner's wallet
    ownerWallet.balance += ownerAmountInOwnerCurrency;
    await ownerWallet.save();

    // Record transaction for the owner's wallet
    const ownerTransaction = new Transaction({
      userId: boatOwnerId,
      amount: ownerAmountInOwnerCurrency,
      type: TransactionType.CREDIT,
      status: TransactionStatus.COMPLETED,
      source: TransactionSource.BOOKING,
      sourceId: booking._id,
      description: `Payment received for booking #${bookingId}`,
      currency: ownerCurrency, // Add owner's currency to transaction
    });

    await ownerTransaction.save();

    // Credit affiliate's wallet if applicable
    if (affiliateId && affiliateFee > 0) {
      // Get affiliate user
      const affiliateUser = await User.findById(affiliateId);
      if (affiliateUser) {
        const affiliateCurrency = (affiliateUser.currency as UserCurrency) || UserCurrency.USD;
        
        // Convert affiliate fee to affiliate's currency if different
        let affiliateFeeInAffiliateCurrency = affiliateFee;
        if (userCurrency !== affiliateCurrency) {
          affiliateFeeInAffiliateCurrency = convertCurrency(
            affiliateFee, 
            userCurrency, 
            affiliateCurrency
          );
        }

        // Find or create affiliate's wallet
        let affiliateWallet = await Wallet.findOne({ userId: affiliateId });
        if (!affiliateWallet) {
          affiliateWallet = new Wallet({
            userId: affiliateId,
            balance: 0,
            status: "active",
          });
        }

        // Add the commission to the affiliate's wallet
        affiliateWallet.balance += affiliateFeeInAffiliateCurrency;
        await affiliateWallet.save();

        // Record transaction for the affiliate
        const affiliateTransaction = new Transaction({
          userId: affiliateId,
          amount: affiliateFeeInAffiliateCurrency,
          type: TransactionType.CREDIT,
          status: TransactionStatus.COMPLETED,
          source: TransactionSource.AFFILIATE,
          sourceId: booking._id,
          description: `Commission for referred booking #${bookingId}`,
          currency: affiliateCurrency, // Add affiliate's currency to transaction
        });

        await affiliateTransaction.save();
      }
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("PAYMENT_SUCCESSFUL"),
      data: {
        payment,
      },
    });
  } catch (error) {
    logger.error("Error processing payment", {
      service: "payment-ms",
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("PAYMENT_FAILED"),
      error,
    });
  }
};

// Get payment details
export const getPaymentDetails = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    const payment = await Payment.findOne({
      _id: id,
      $or: [{ userId }, { boatOwnerId: userId }],
    });

    if (!payment) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("PAYMENT_NOT_FOUND"),
      });
    }

    // Parse transaction details if available
    let transactionDetails = undefined;
    if (payment.transactionDetails) {
      try {
        transactionDetails = JSON.parse(payment.transactionDetails);
      } catch (e) {
        logger.error("Error parsing transaction details", {
          service: "payment-ms",
          paymentId: payment._id,
          error: e,
        });
      }
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("PAYMENT_DETAILS_FETCH_SUCCESS"),
      data: {
        ...payment.toJSON(),
        transactionDetails,
      },
    });
  } catch (error) {
    logger.error("Error in getPaymentDetails", {
      service: "payment-ms",
      userId: req.user._id,
      paymentId: req.params.id,
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Get user's payment history
export const getPaymentHistory = async (req: any, res: any) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10, type } = req.query;
    const pageNumber = parseInt(page as string);
    const limitNumber = parseInt(limit as string);

    const skip = (pageNumber - 1) * limitNumber;

    const query: any = {
      $or: [{ userId }, { boatOwnerId: userId }],
    };

    if (type === "received") {
      query.boatOwnerId = userId;
      delete query.$or;
    } else if (type === "sent") {
      query.userId = userId;
      delete query.$or;
    }

    const payments = await Payment.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNumber);

    const totalCount = await Payment.countDocuments(query);

    // Parse transaction details for all payments
    const formattedPayments = payments.map((payment) => {
      let transactionDetails = undefined;
      if (payment.transactionDetails) {
        try {
          transactionDetails = JSON.parse(payment.transactionDetails);
        } catch (e) {
          logger.error("Error parsing transaction details", {
            service: "payment-ms",
            paymentId: payment._id,
            error: e,
          });
        }
      }

      return {
        ...payment.toJSON(),
        transactionDetails,
      };
    });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("PAYMENT_HISTORY_FETCH_SUCCESS"),
      data: {
        payments: formattedPayments,
        pagination: {
          page: pageNumber,
          size: limitNumber,
          totalCount: totalCount,
          totalPages: Math.ceil(totalCount / limitNumber),
        },
      },
    });
  } catch (error) {
    logger.error("Error in getPaymentHistory", {
      service: "payment-ms",
      userId: req.user._id,
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// Generate setup intent for Stripe (server-side only, no client secret)
export const generateClientToken = async (req: any, res: any) => {
  try {
    const result = await StripeService.createSetupIntent();

    if (!result.success) {
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: result.error || req.__("CLIENT_TOKEN_GENERATION_FAILED"),
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("CLIENT_TOKEN_GENERATED"),
      data: {
        setupIntentId: result.setupIntentId,
        message: "Setup intent created successfully. Use card tokens for payment processing.",
      },
    });
  } catch (error) {
    logger.error("Error generating setup intent", {
      service: "payment-ms",
      userId: req.user._id,
      error,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("CLIENT_TOKEN_GENERATION_FAILED"),
      error,
    });
  }
};

// Test different Stripe payment methods
export const testStripePayment = async (req: any, res: any) => {
  try {
    const {
      amount,
      paymentMethodId,
      method = "payment_intent", // Options: "payment_intent", "direct_charge", "two_step"
      currency = "usd"
    } = req.body;

    if (!amount || !paymentMethodId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Amount and payment method ID are required",
      });
    }

    let result: any;

    switch (method) {
      case "payment_intent":
        result = await StripeService.processPayment(
          amount,
          paymentMethodId,
          undefined,
          {
            currency,
            description: "Test payment using Payment Intent",
            metadata: { test: "true", method: "payment_intent" },
          }
        );
        break;

      case "direct_charge":
        result = await StripeService.processDirectCharge(
          amount,
          paymentMethodId,
          undefined,
          {
            currency,
            description: "Test payment using Direct Charge",
            metadata: { test: "true", method: "direct_charge" },
          }
        );
        break;

      case "two_step":
        // Step 1: Create payment intent
        const createResult = await StripeService.createPaymentIntent(
          amount,
          currency,
          {
            description: "Test payment using Two-Step Process",
            metadata: { test: "true", method: "two_step" },
          }
        );

        if (!createResult.success) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: "Failed to create payment intent",
            error: createResult.error,
          });
        }

        // Step 2: Confirm payment intent
        const confirmResult = await StripeService.confirmPaymentIntent(
          createResult.paymentIntent!.id,
          paymentMethodId
        );

        result = {
          success: confirmResult.success,
          paymentIntent: confirmResult.paymentIntent,
          error: confirmResult.error,
        };
        break;

      default:
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: "Invalid payment method. Use: payment_intent, direct_charge, or two_step",
        });
    }

    if (result.success) {
      return res.status(StatusCodes.OK).json({
        success: true,
        message: `Payment successful using ${method}`,
        data: result,
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: `Payment failed using ${method}`,
        error: result.error,
      });
    }

  } catch (error: any) {
    logger.error("Error testing Stripe payment", {
      service: "payment-ms",
      error: error.message,
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Internal server error during payment test",
      error: error.message,
    });
  }
};
