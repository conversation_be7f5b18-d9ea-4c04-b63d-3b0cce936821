import { StatusCodes } from "http-status-codes";
import logger from "../../../shared/services/logger.service";
import Wishlist from "../models/Wishlist";
import mongoose from "mongoose";
import { createActivityLog } from "../../../shared/models/ActivityLog";
import { RecordType } from "../../../boat-ms/src/models/Boat";
import { ReviewStatus } from "../../../reviews-ms/src/models/Review";
import { BookingStatus } from "../../../booking-ms/src/models/Booking";

/**
 * Add a boat or activity to user's wishlist
 */
export const addToWishlist = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { boatId } = req.body;

    // Validate boatId
    if (!mongoose.Types.ObjectId.isValid(boatId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("WISHLIST_INVALID_BOAT_ID"),
      });
    }

    // Check if already in wishlist
    const existing = await Wishlist.findOne({ userId, boatId });
    if (existing) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("WISHLIST_ALREADY_IN_WISHLIST"),
      });
    }

    // Add to wishlist
    const wishlist = new Wishlist({
      userId,
      boatId,
    });
    await wishlist.save();

    // Log the wishlist addition
    await createActivityLog("wishlists", wishlist._id, "CREATE", [], userId);

    res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("WISHLIST_ADDED_SUCCESS"),
      data: wishlist,
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Remove a boat or activity from user's wishlist
 */
export const removeFromWishlist = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { boatId } = req.params;

    // Validate boatId
    if (!mongoose.Types.ObjectId.isValid(boatId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("WISHLIST_INVALID_BOAT_ID"),
      });
    }

    // Get the wishlist entry before deletion
    const wishlist = await Wishlist.findOne({ userId, boatId });

    // If not found
    if (!wishlist) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("WISHLIST_NOT_IN_WISHLIST"),
      });
    }

    // Remove from wishlist
    await Wishlist.findOneAndDelete({ userId, boatId });

    // Log the wishlist removal
    await createActivityLog("wishlists", wishlist._id, "DELETE", [], userId);

    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("WISHLIST_REMOVED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Get all wishlisted boats and activities for a user
 */
export const getWishlist = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    // Use aggregation to get wishlist with boat details
    const pipeline = [
      // Match stage - find all wishlist entries for the user
      { $match: { userId: new mongoose.Types.ObjectId(userId) } },

      // Sort by most recently added
      { $sort: { createdAt: -1 } },

      // Pagination
      { $skip: skip },
      { $limit: Number(limit) },

      // Lookup boat details
      {
        $lookup: {
          from: "boats",
          localField: "boatId",
          foreignField: "_id",
          as: "boat",
        },
      },

      // Unwind the boat array
      { $unwind: "$boat" },

      // Lookup boat images
      {
        $lookup: {
          from: "files",
          let: { imageIds: "$boat.attachments.images" },
          pipeline: [{ $match: { $expr: { $in: ["$_id", "$$imageIds"] } } }],
          as: "boatImages",
        },
      },
      {
        $lookup: {
          from: "bookings",
          let: { boatId: "$boat._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $in: ["$status", [BookingStatus.Pending, BookingStatus.Accepted]] },
                    { $gte: ["$endDate", new Date()] }, // Only active bookings
                  ],
                },
              },
            },
          ],
          as: "activeBookings",
        },
      },

      // Lookup reviews to calculate average rating
      {
        $lookup: {
          from: "reviews",
          let: { boatId: "$boat._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$boatId", "$$boatId"] },
                    { $eq: ["$status", ReviewStatus.ACTIVE] },
                  ],
                },
              },
            },
          ],
          as: "reviews",
        },
      },

      // Project the needed fields based on recordType
      {
        $project: {
          _id: 1,
          boatId: 1,
          createdAt: 1,
          boat: {
            _id: "$boat._id",
            name: "$boat.name",
            recordType: "$boat.recordType",
            location: "$boat.location",
            // Conditional fields based on recordType
            // Fields for boat
            type: {
              $cond: {
                if: { $eq: ["$boat.recordType", RecordType.BOAT] },
                then: "$boat.type",
                else: "$$REMOVE",
              }
            },
            pricePerDay: {
              $cond: {
                if: { $eq: ["$boat.recordType", RecordType.BOAT] },
                then: "$boat.pricePerDay",
                else: "$$REMOVE",
              }
            },
            guestsCapacity: {
              $cond: {
                if: { $eq: ["$boat.recordType", RecordType.BOAT] },
                then: "$boat.guestsCapacity",
                else: "$$REMOVE",
              }
            },
            cabins: {
              $cond: {
                if: { $eq: ["$boat.recordType", RecordType.BOAT] },
                then: "$boat.cabins",
                else: "$$REMOVE",
              }
            },
            baths: {
              $cond: {
                if: { $eq: ["$boat.recordType", RecordType.BOAT] },
                then: "$boat.baths",
                else: "$$REMOVE",
              }
            },
            // Fields for activity
            duration: {
              $cond: {
                if: { $eq: ["$boat.recordType", RecordType.ACTIVITY] },
                then: "$boat.duration",
                else: "$$REMOVE",
              }
            },
            price: {
              $cond: {
                if: { $eq: ["$boat.recordType", RecordType.ACTIVITY] },
                then: "$boat.price",
                else: "$$REMOVE",
              }
            },
            description: {
              $cond: {
                if: { $eq: ["$boat.recordType", RecordType.ACTIVITY] },
                then: "$boat.description",
                else: "$$REMOVE",
              }
            },
            safetyInstructions: {
              $cond: {
                if: { $eq: ["$boat.recordType", RecordType.ACTIVITY] },
                then: "$boat.safetyInstructions",
                else: "$$REMOVE",
              }
            },
            // Common for both
            images: {
              $map: {
                input: "$boatImages",
                as: "img",
                in: {
                  $concat: [global.config.FILE_BASE_URL, "$$img.location"],
                },
              },
            },
            averageRating: {
              $cond: [
                { $gt: [{ $size: "$reviews" }, 0] },
                { $avg: "$reviews.rating" },
                0,
              ],
            },
            reviewCount: { $size: "$reviews" },
            isBooked: { $gt: [{ $size: "$activeBookings" }, 0] },
          },
        },
      },
    ] as any[]; // Add type assertion to avoid TypeScript errors

    // Execute aggregation
    const wishlisted = await Wishlist.aggregate(pipeline);

    // Get total count for pagination
    const total = await Wishlist.countDocuments({ userId });

    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("WISHLIST_FETCH_SUCCESS"),
      data: {
        wishlisted,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: total,
          totalPages: Math.ceil(total / Number(limit)),
        },
      },
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Check if a boat or activity is wishlisted by the current user
 */
export const checkWishlistStatus = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;
    const { boatId } = req.params;

    // Validate boatId
    if (!mongoose.Types.ObjectId.isValid(boatId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("WISHLIST_INVALID_BOAT_ID"),
      });
    }

    // Check if in wishlist
    const isWishlisted = await Wishlist.exists({ userId, boatId });

    res.status(StatusCodes.OK).json({
      status: true,
      data: {
        isWishlisted: !!isWishlisted,
      },
    });
  } catch (error) {
    logger.error(error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
