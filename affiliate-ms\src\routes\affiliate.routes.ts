import { Router } from "express";
import {
  createAffiliate,
  getAllAffiliates,
  getAffiliateById,
  updateAffiliate,
  deleteAffiliate,
  approveAffiliate,
  rejectAffiliate,
  getAffiliatesByStatus,
  getAffiliatesByMultipleStatuses,
  getMyAffiliateStatus,
  getMyAffiliatedBoats,
  getMyAffiliateBookings,
  reapplyAffiliate,
  validateReferralName,
  getAffiliateBoats,
  getAffiliateBoatDetails,
  getAffiliateEarnings,
} from "../controller/affiliate.controller";
import {
  createAffiliateValidator,
  updateAffiliateValidator,
  getAffiliateValidator,
  deleteAffiliateValidator,
  approveAffiliateValidator,
  rejectAffiliateValidator,
  getAffiliateBoatDetailsValidator,
} from "../validator/affiliate.validator";
import { adminAccess } from "../../../shared/middleware/admin";

const router = Router();

// User-facing affiliate routes
router.get("/me", getMyAffiliateStatus);
router.get("/me/boats", getMyAffiliatedBoats);
router.get("/me/bookings", getMyAffiliateBookings);
router.post("/register", createAffiliateValidator, createAffiliate);
router.post("/reapply", createAffiliateValidator, reapplyAffiliate);

// Public routes (no authentication required)
router.post("/validate-referral", validateReferralName);

// Admin routes - protected with adminAccess middleware
router.get("/admin", adminAccess, getAllAffiliates);
router.get("/admin/status/:status", adminAccess, getAffiliatesByStatus);
router.get("/admin/multiple-statuses", adminAccess, getAffiliatesByMultipleStatuses);

// New admin routes for affiliate dashboard screens 215 & 218
router.get("/admin/:id/boat/:boatId/details", adminAccess, getAffiliateBoatDetailsValidator, getAffiliateBoatDetails);
router.get("/admin/:id/earnings", adminAccess, getAffiliateValidator, getAffiliateEarnings);
router.get("/admin/boats", adminAccess, getAffiliateBoats); // ID is now optional query parameter


router.get("/admin/:id", adminAccess, getAffiliateValidator, getAffiliateById);
router.put(
  "/admin/:id",
  adminAccess,
  updateAffiliateValidator,
  updateAffiliate
);
router.delete(
  "/admin/:id",
  adminAccess,
  deleteAffiliateValidator,
  deleteAffiliate
);

// Admin status management - protected with adminAccess middleware
router.post(
  "/admin/:id/approve",
  adminAccess,
  approveAffiliateValidator,
  approveAffiliate
);
router.post(
  "/admin/:id/reject",
  adminAccess,
  rejectAffiliateValidator,
  rejectAffiliate
);

export default router;
