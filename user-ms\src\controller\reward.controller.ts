import { StatusCodes } from "http-status-codes";
import { getRewardDetails } from "../../../shared/services/reward.service";
import logger from "../../../shared/services/logger.service";

/**
 * Get user's reward details
 */
export const getUserRewards = async (req: any, res: any): Promise<any> => {
  try {
    const userId = req.user._id;

    // Get reward details
    const rewards = await getRewardDetails(userId);

    if (!rewards) {
      return res.status(StatusCodes.OK).json({
        success: true,
        message: req.__("NO_REWARDS_FOUND"),
        data: {
          points: 0,
          pointsExpiryDate: null,
          welcomeDiscounts: [],
          pointsHistory: []
        }
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: req.__("REWARDS_FETCH_SUCCESS"),
      data: rewards
    });
  } catch (error: any) {
    logger.error(`Error fetching rewards: ${error.message}`, {
      service: "user-ms",
      error,
    });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
}; 