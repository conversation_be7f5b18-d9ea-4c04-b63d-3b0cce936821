import { celebrate, Joi, Segments } from "celebrate";
import { PaymentMethod } from "../models/Payment";

export const paymentValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    bookingId: Joi.string().required().messages({
      "string.base": "Booking ID must be a string",
      "any.required": "Booking ID is required",
    }),
    paymentMethod: Joi.string()
      .valid(...Object.values(PaymentMethod))
      .required()
      .messages({
        "string.base": "Payment method must be a string",
        "any.required": "Payment method is required",
        "any.only": `Payment method must be one of: ${Object.values(
          PaymentMethod,
        ).join(", ")}`,
      }),
    cardId: Joi.string().when("paymentMethod", {
      is: PaymentMethod.CARD,
      then: Joi.optional().messages({
        "string.base": "Card ID must be a string",
      }),
      otherwise: Joi.optional(),
    }),
    paymentMethodNonce: Joi.string().when("paymentMethod", {
      is: [PaymentMethod.PAYPAL],
      then: Joi.required().messages({
        "string.base": "Payment method nonce must be a string",
        "any.required": "Payment method nonce is required for PayPal payments",
      }),
      otherwise: Joi.optional(),
    }),
    deviceData: Joi.string().optional().messages({
      "string.base": "Device data must be a string",
    }),
    usePoints: Joi.number().integer().min(0).optional().default(0).messages({
      "number.base": "Points must be a number",
      "number.integer": "Points must be an integer",
      "number.min": "Points cannot be negative",
    }),
    useWelcomeDiscount: Joi.boolean().optional().default(false).messages({
      "boolean.base": "Welcome discount flag must be a boolean",
    }),
  }),
});

export const paymentIntentValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    bookingId: Joi.string()
      .required()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .messages({
        "string.base": "Booking ID must be a string",
        "string.pattern.base": "Invalid booking ID format",
        "any.required": "Booking ID is required"
      }),
    usePoints: Joi.number()
      .integer()
      .min(0)
      .optional()
      .default(0)
      .messages({
        "number.base": "Points must be a number",
        "number.integer": "Points must be an integer",
        "number.min": "Points cannot be negative",
      }),
    useWelcomeDiscount: Joi.boolean()
      .optional()
      .default(false)
      .messages({
        "boolean.base": "Welcome discount flag must be a boolean",
      }),
  }),
});
