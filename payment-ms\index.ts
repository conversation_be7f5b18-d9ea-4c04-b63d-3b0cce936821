import express from "express";
import cors from "cors";
import * as config from "../shared/config.json";
import i18n from "./src/services/i18n.service";
import connectDB from "../shared/db";
import { authenticateJWT } from "../shared/middleware/auth";
import logger from "../shared/services/logger.service";
import HandleErrorMessage from "../shared/middleware/validator";
import responseFormatter from "../shared/middleware/responseFormatter.middleware";
import { checkAppVersion } from "../shared/middleware/appVersionCheck.middleware";

const app = express();

require("dotenv").config();

const environment = process.env.NODE_ENV! || "dev";

const envConfig: any = JSON.parse(JSON.stringify(config))[environment];

global.config = envConfig;

try {
  connectDB();
  logger.info("Database connected successfully", { service: "payment-ms" });
} catch (error: any) {
  logger.error(`Database connection failed: ${error.message}`, {
    service: "payment-ms",
    error,
  });
  process.exit(1);
}

import routes from "./src/routes/index";
import PaymentCronService from "./src/helpers/paymentCron.service";
import { handleStripeWebhook } from "./src/controller/webhook.controller";

// Import Boat model to register schema
import "../boat-ms/src/models/Boat";

// Enable CORS for all origins
app.use(cors({ origin: "*" }));

app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(i18n.init);

// Apply response formatter middleware
app.use(responseFormatter);

// Apply app version check middleware (before authentication)
app.use(checkAppVersion);

// Webhook routes - no authentication required
app.post("/webhook/stripe", express.raw({ type: 'application/json' }), handleStripeWebhook);

// Regular API routes - require JWT authentication
app.use("/", authenticateJWT, routes);

app.use(HandleErrorMessage);

app.listen(envConfig.services["payment-ms"].PORT, () => {
  logger.info(
    `Payment microservice is running on port ${envConfig.services["payment-ms"].PORT}`,
    {
      service: "payment-ms",
      port: envConfig.services["payment-ms"].PORT,
    }
  );

  // Start payment cron jobs
  PaymentCronService.startCronJobs();
});

export default app;
