import SocialMediaProfile, { ISocialMediaProfile } from "../model/socialMedia";
import mongoose from "mongoose";
import logger from "../../../shared/services/logger.service";
import File, { FileType } from "../../../shared/models/Files";
import { getRelativePath } from "../../../shared/middleware/fileUpload.middleware";
import <PERSON><PERSON> from "joi";

// Validation schemas
const platformSchema = Joi.object({
  platform: Joi.string().valid("Twitter", "Instagram", "Facebook", "Snapchat", "Other").required(),
  username: Joi.string().required(),
  url: Joi.string().uri().optional().allow(''),
  icon: Joi.any().optional(), // File will be handled separately
  isActive: Joi.boolean().optional().default(true),
});

const createSocialMediaSchema = Joi.object({
  version: Joi.string().required(),
  platforms: Joi.array().items(platformSchema).min(1).required(),
  isActive: Joi.boolean().optional().default(false),
});

const updateSocialMediaSchema = Joi.object({
  version: Joi.string().optional(),
  platforms: Joi.array().items(platformSchema).min(1).optional(),
  isActive: Joi.boolean().optional(),
});

// Validation function
const validateSocialMediaData = (data: any, schema: Joi.ObjectSchema) => {
  const { error, value } = schema.validate(data, { abortEarly: false });
  return { error, value };
};

// Create a new Social Media Profile
export const createSocialMediaProfile = async (req: any, res: any) => {
  try {
    const socialMediaData: any = JSON.parse(JSON.stringify(req.body));

    // Parse platforms if it's a string (from form-data)
    if (typeof socialMediaData.platforms === 'string') {
      try {
        socialMediaData.platforms = JSON.parse(socialMediaData.platforms);
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: res.__("INVALID_PLATFORMS_DATA"),
        });
      }
    }
    // Validate the data
    const { error, value } = validateSocialMediaData(socialMediaData, createSocialMediaSchema);
    if (error) {
      return res.status(400).json({
        success: false,
        message: res.__("VALIDATION_ERROR"),
        errors: error.details.map((err) => ({
          field: err.path.join('.'),
          message: err.message,
        })),
      });
    }

    // Use validated data
    const validatedData = value;

    // Handle icon file uploads for each platform
    if (req.files && Array.isArray(req.files) && req.files.length > 0 && Array.isArray(validatedData.platforms)) {
      // Create a map of files by fieldname for easier lookup
      const fileMap = new Map();
      req.files.forEach((file: any) => {
        fileMap.set(file.fieldname, file);
      });

      for (let i = 0; i < validatedData.platforms.length; i++) {
        const platform = validatedData.platforms[i];
        const iconFile = fileMap.get(`platforms[${i}][icon]`) || fileMap.get(`icon_${i}`);

        if (iconFile) {
          // Create a File record for the icon
          const relativePath = getRelativePath(iconFile.path);
          const newFile = await File.create({
            name: iconFile.filename,
            size: iconFile.size,
            fileType: iconFile.mimetype,
            ext: iconFile.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.IMAGE,
            ownerId: req.user?._id,
          });
          platform.icon = newFile._id as mongoose.Types.ObjectId;
        }
      }
    }

    // Check if version already exists
    const existingVersion = await SocialMediaProfile.findOne({
      version: validatedData.version,
    });

    if (existingVersion) {
      return res.status(400).json({
        success: false,
        message: res.__("SOCIAL_MEDIA_VERSION_EXISTS"),
      });
    }

    // If isActive is true, set all other documents to false
    if (validatedData.isActive) {
      await SocialMediaProfile.updateMany({}, { isActive: false });
    }

    const socialMediaProfile = new SocialMediaProfile(validatedData);
    await socialMediaProfile.save();

    // Populate the icon references and transform to {_id, link} format
    const populatedProfile = await SocialMediaProfile.findById(socialMediaProfile._id).populate('platforms.icon');
    const profileData = populatedProfile?.toObject();

    if (profileData) {
      profileData.platforms = profileData.platforms.map((platform: any) => {
        // Transform icon to {_id, link} format
        if (platform.icon && platform.icon.location) {
          platform.icon = {
            _id: platform.icon._id,
            link: `${global.config.FILE_BASE_URL}${platform.icon.location}`
          };
        } else if (platform.icon && platform.icon._id) {
          // If icon exists but no location, just return the _id
          platform.icon = {
            _id: platform.icon._id,
            link: null
          };
        } else {
          // No icon
          platform.icon = null;
        }
        return platform;
      });
    }

    logger.info("Social Media Profile created successfully", {
      service: "social-media-ms",
      profileId: socialMediaProfile._id.toString(),
    });

    return res.status(201).json({
      success: true,
      data: profileData,
      message: res.__("SOCIAL_MEDIA_CREATED"),
    });
  } catch (error: any) {
    logger.error(`Error creating Social Media Profile: ${error.message}`, {
      service: "social-media-ms",
      error,
    });

    return res.status(500).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

// Get the currently active Social Media Profile
export const getActiveSocialMediaProfile = async (_req: any, res: any) => {
  try {
    const socialMediaProfile = await SocialMediaProfile.findOne({
      isActive: true,
    }).populate('platforms.icon');

    if (!socialMediaProfile) {
      return res.status(200).json({
        success: true,
        data: [],
        message: res.__("SOCIAL_MEDIA_NOT_FOUND"),
      });
    }

    // Transform platforms to include icon in {_id, link} format
    const platforms = socialMediaProfile.platforms.map((platform: any) => {
      const platformData = platform.toObject();

      // Transform icon to {_id, link} format
      if (platformData.icon && platformData.icon.location) {
        platformData.icon = {
          _id: platformData.icon._id,
          link: `${global.config.FILE_BASE_URL || ""}${platformData.icon.location}`
        };
      } else if (platformData.icon && platformData.icon._id) {
        // If icon exists but no location, just return the _id
        platformData.icon = {
          _id: platformData.icon._id,
          link: null
        };
      } else {
        // No icon
        platformData.icon = null;
      }

      return platformData;
    });

    return res.status(200).json({
      success: true,
      data: platforms,
      message: res.__("SOCIAL_MEDIA_RETRIEVED"),
    });
  } catch (error: any) {
    logger.error(
      `Error fetching active Social Media Profile: ${error.message}`,
      {
        service: "social-media-ms",
        error,
      }
    );

    return res.status(500).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

// Get Social Media Profile by version
export const getSocialMediaProfileByVersion = async (req: any, res: any) => {
  try {
    const { version } = req.params;
    const socialMediaProfile = await SocialMediaProfile.findOne({ version }).populate('platforms.icon');

    if (!socialMediaProfile) {
      return res.status(404).json({
        success: false,
        message: res.__("SOCIAL_MEDIA_NOT_FOUND"),
      });
    }

    // Transform the profile data to include icon in {_id, link} format
    const profileData = socialMediaProfile.toObject();
    profileData.platforms = profileData.platforms.map((platform: any) => {
      // Transform icon to {_id, link} format
      if (platform.icon && platform.icon.location) {
        platform.icon = {
          _id: platform.icon._id,
          link: `${global.config.FILE_BASE_URL}${platform.icon.location}`
        };
      } else if (platform.icon && platform.icon._id) {
        // If icon exists but no location, just return the _id
        platform.icon = {
          _id: platform.icon._id,
          link: null
        };
      } else {
        // No icon
        platform.icon = null;
      }
      return platform;
    });

    return res.status(200).json({
      success: true,
      data: profileData,
    });
  } catch (error: any) {
    logger.error(
      `Error fetching Social Media Profile by version: ${error.message}`,
      {
        service: "social-media-ms",
        error,
        version: req.params.version,
      }
    );

    return res.status(500).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

// Get all Social Media Profiles (admin)
export const getAllSocialMediaProfiles = async (req: any, res: any) => {
  try {
    // Parse pagination parameters
    const page = Math.max(1, parseInt(req.query.page as string) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 10));
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const total = await SocialMediaProfile.countDocuments();

    // Get paginated profiles
    const socialMediaProfiles = await SocialMediaProfile.find()
      .populate('platforms.icon')
      .sort({
        createdAt: -1,
      })
      .skip(skip)
      .limit(limit);

    // Transform all profiles to include icon in {_id, link} format
    const transformedProfiles = socialMediaProfiles.map((profile: any) => {
      const profileData = profile.toObject();
      profileData.platforms = profileData.platforms.map((platform: any) => {
        // Transform icon to {_id, link} format
        if (platform.icon && platform.icon.location) {
          platform.icon = {
            _id: platform.icon._id,
            link: `${global.config.FILE_BASE_URL || ""}${platform.icon.location}`
          };
        } else if (platform.icon && platform.icon._id) {
          // If icon exists but no location, just return the _id
          platform.icon = {
            _id: platform.icon._id,
            link: null
          };
        } else {
          // No icon
          platform.icon = null;
        }
        return platform;
      });
      return profileData;
    });

    return res.status(200).json({
      success: true,
      data: {
        profiles: transformedProfiles,
        pagination: {
          page,
          size: limit,
          totalCount: total,
          totalPages: Math.ceil(total / limit),
        },
      },
      message: res.__("SOCIAL_MEDIA_PROFILES_RETRIEVED"),
    });
  } catch (error: any) {
    logger.error(`Error fetching all Social Media Profiles: ${error.message}`, {
      service: "social-media-ms",
      error,
    });

    return res.status(500).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

// Update a Social Media Profile
export const updateSocialMediaProfile = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: res.__("INVALID_ID"),
      });
    }

    // Find the Social Media Profile
    const socialMediaProfile = await SocialMediaProfile.findById(id);
    if (!socialMediaProfile) {
      return res.status(404).json({
        success: false,
        message: res.__("SOCIAL_MEDIA_NOT_FOUND"),
      });
    }

    // Parse platforms if it's a string (from form-data)
    if (typeof updateData.platforms === 'string') {
      try {
        updateData.platforms = JSON.parse(updateData.platforms);
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: res.__("INVALID_PLATFORMS_DATA"),
        });
      }
    }

    // Validate the data
    const { error, value } = validateSocialMediaData(updateData, updateSocialMediaSchema);
    if (error) {
      return res.status(400).json({
        success: false,
        message: res.__("VALIDATION_ERROR"),
        errors: error.details.map((err) => ({
          field: err.path.join('.'),
          message: err.message,
        })),
      });
    }

    // Use validated data
    const validatedData = value;

    // Handle icon file uploads for each platform
    if (req.files && Array.isArray(req.files) && req.files.length > 0 && Array.isArray(validatedData.platforms)) {
      // Create a map of files by fieldname for easier lookup
      const fileMap = new Map();
      req.files.forEach((file: any) => {
        fileMap.set(file.fieldname, file);
      });

      for (let i = 0; i < validatedData.platforms.length; i++) {
        const platform = validatedData.platforms[i];
        const iconFile = fileMap.get(`platforms[${i}][icon]`) || fileMap.get(`icon_${i}`);

        if (iconFile) {
          // Create a File record for the icon
          const relativePath = getRelativePath(iconFile.path);
          const newFile = await File.create({
            name: iconFile.filename,
            size: iconFile.size,
            fileType: iconFile.mimetype,
            ext: iconFile.originalname.split(".").pop(),
            location: relativePath,
            type: FileType.IMAGE,
            ownerId: req.user?._id,
          });
          platform.icon = newFile._id as mongoose.Types.ObjectId;
        }
      }
    }

    // If isActive is set to true, deactivate all other versions
    if (validatedData.isActive) {
      await SocialMediaProfile.updateMany(
        { _id: { $ne: id } },
        { isActive: false }
      );
    }

    // Update the Social Media Profile
    const updatedProfile = await SocialMediaProfile.findByIdAndUpdate(
      id,
      { $set: validatedData },
      { new: true }
    ).populate('platforms.icon');

    // Transform the updated profile to include icon in {_id, link} format
    const profileData = updatedProfile?.toObject();
    if (profileData) {
      profileData.platforms = profileData.platforms.map((platform: any) => {
        // Transform icon to {_id, link} format
        if (platform.icon && platform.icon.location) {
          platform.icon = {
            _id: platform.icon._id,
            link: `${global.config.FILE_BASE_URL}${platform.icon.location}`
          };
        } else if (platform.icon && platform.icon._id) {
          // If icon exists but no location, just return the _id
          platform.icon = {
            _id: platform.icon._id,
            link: null
          };
        } else {
          // No icon
          platform.icon = null;
        }
        return platform;
      });
    }

    logger.info("Social Media Profile updated successfully", {
      service: "social-media-ms",
      profileId: id,
    });

    return res.status(200).json({
      success: true,
      data: profileData,
      message: res.__("SOCIAL_MEDIA_UPDATED"),
    });
  } catch (error: any) {
    logger.error(`Error updating Social Media Profile: ${error.message}`, {
      service: "social-media-ms",
      error,
      profileId: req.params.id,
    });

    return res.status(500).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};

// Delete a Social Media Profile
export const deleteSocialMediaProfile = async (req: any, res: any) => {
  try {
    const { id } = req.params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: res.__("INVALID_ID"),
      });
    }

    // Find the Social Media Profile
    const socialMediaProfile = await SocialMediaProfile.findById(id);
    if (!socialMediaProfile) {
      return res.status(404).json({
        success: false,
        message: res.__("SOCIAL_MEDIA_NOT_FOUND"),
      });
    }

    // Check if this is the active version
    if (socialMediaProfile.isActive) {
      return res.status(400).json({
        success: false,
        message: res.__("CANNOT_DELETE_ACTIVE_SOCIAL_MEDIA"),
      });
    }

    // Delete the Social Media Profile
    await SocialMediaProfile.findByIdAndDelete(id);

    logger.info("Social Media Profile deleted successfully", {
      service: "social-media-ms",
      profileId: id,
    });

    return res.status(200).json({
      success: true,
      message: res.__("SOCIAL_MEDIA_DELETED"),
    });
  } catch (error: any) {
    logger.error(`Error deleting Social Media Profile: ${error.message}`, {
      service: "social-media-ms",
      error,
      profileId: req.params.id,
    });

    return res.status(500).json({
      success: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
    });
  }
};
