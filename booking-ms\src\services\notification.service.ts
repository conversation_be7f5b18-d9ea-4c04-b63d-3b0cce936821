import { NotificationService } from "../../../notification-ms/src/services/notification.service";
import { NOTIFICATION_TYPES } from "../../../notification-ms/src/constant/notificationConstant";
import { BookingStatus } from "../models/Booking";
import Boat from "../../../boat-ms/src/models/Boat";

export class BookingNotificationService {
  static async sendBookingNotification(booking: any, boat: any) {
    // Send notification to boat owner
    await NotificationService.createAndSendNotification(
      boat.ownerId?._id.toString(),
      booking.userId.toString(),
      NOTIFICATION_TYPES.BOOKING_CREATED,
      {
        bookingId: booking._id,
        boatId: boat._id,
        boatName: boat.name,
        startDate: booking.startDate,
        endDate: booking.endDate,
        totalAmount: booking.totalAmount,
      },
    );
  }

  static async sendBookingModificationNotification(booking: any, boat: any) {
    // Send notification to boat owner
    await NotificationService.createAndSendNotification(
      boat.ownerId?.toString(),
      booking.userId.toString(),
      NOTIFICATION_TYPES.BOOKING_MODIFIED,
      {
        bookingId: booking._id,
        boatId: boat._id,
        boatName: boat.name,
        startDate: booking.startDate,
        endDate: booking.endDate,
        totalAmount: booking.totalAmount,
      },
    );
  }

  static async sendBookingApprovalNotification(booking: any, boat: any) {
    // Send notification to user
    await NotificationService.createAndSendNotification(
      booking.userId.toString(),
      boat.ownerId.toString(),
      NOTIFICATION_TYPES.BOOKING_APPROVED,
      {
        bookingId: booking._id,
        boatId: boat._id,
        boatName: boat.name,
        startDate: booking.startDate,
        endDate: booking.endDate,
        totalAmount: booking.totalAmount,
      },
    );
  }

  static async sendBookingRejectionNotification(
    booking: any,
    boat: any,
    reason: string,
  ) {
    // Send notification to user
    await NotificationService.createAndSendNotification(
      booking.userId.toString(),
      boat.ownerId.toString(),
      NOTIFICATION_TYPES.BOOKING_REJECTED,
      {
        bookingId: booking._id,
        boatId: boat._id,
        boatName: boat.name,
        startDate: booking.startDate,
        endDate: booking.endDate,
        totalAmount: booking.totalAmount,
        rejectionReason: reason,
      },
    );
  }

  static async sendBookingCancellationNotification(booking: any, boat: any) {
    // Send notification to boat owner
    await NotificationService.createAndSendNotification(
      boat.ownerId.toString(),
      booking.userId.toString(),
      NOTIFICATION_TYPES.BOOKING_CANCELLED,
      {
        bookingId: booking._id,
        boatId: boat._id,
        boatName: boat.name,
        startDate: booking.startDate,
        endDate: booking.endDate,
        totalAmount: booking.totalAmount,
      },
    );
  }

  static async sendStatusChangeNotification(booking: any) {
    try {
      // Get the boat details
      const boat = await Boat.findById(booking.boatId);
      if (!boat) {
        throw new Error(`Boat not found for booking ${booking._id}`);
      }

      let notificationType;
      let recipientId;
      let senderId;

      // Determine notification type and recipient based on status
      switch (booking.status) {
        case BookingStatus.ReadyForPayment:
          notificationType = NOTIFICATION_TYPES.BOOKING_READY_FOR_PAYMENT;
          recipientId = booking.userId.toString();
          senderId = boat.ownerId.toString();
          break;
        case BookingStatus.Accepted:
          notificationType = NOTIFICATION_TYPES.BOOKING_APPROVED;
          recipientId = booking.userId.toString();
          senderId = boat.ownerId.toString();
          break;
        case BookingStatus.Rejected:
          notificationType = NOTIFICATION_TYPES.BOOKING_REJECTED;
          recipientId = booking.userId.toString();
          senderId = boat.ownerId.toString();
          break;
        case BookingStatus.Cancelled:
          notificationType = NOTIFICATION_TYPES.BOOKING_CANCELLED;
          recipientId = boat.ownerId.toString();
          senderId = booking.userId.toString();
          break;
        case BookingStatus.Completed:
          notificationType = NOTIFICATION_TYPES.BOOKING_COMPLETED;
          recipientId = booking.userId.toString();
          senderId = boat.ownerId.toString();
          break;
        default:
          // No notification for other status changes
          return;
      }

      // Send notification
      await NotificationService.createAndSendNotification(
        recipientId,
        senderId,
        notificationType,
        {
          bookingId: booking._id,
          boatId: boat._id,
          boatName: boat.name,
          startDate: booking.startDate,
          endDate: booking.endDate,
          totalAmount: booking.totalAmount,
          rejectionReason: booking.rejectionReason,
          status: booking.status,
        },
      );
    } catch (error) {
      console.error("Error sending booking status notification:", error);
    }
  }
}
