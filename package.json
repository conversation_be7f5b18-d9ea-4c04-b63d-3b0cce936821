{"name": "sea-escape", "version": "1.0.0", "main": "index.js", "scripts": {"start": "concurrently \"npm run start:api\" \"npm run start:auth\" \"npm run start:user\" \"npm run start:boat\" \"npm run start:booking\" \"npm run start:wishlist\" \"npm run start:affiliate\" \"npm run start:notification\" \"npm run start:newsletter\" \"npm run start:faq\" \"npm run start:card\" \"npm run start:contact-us\" \"npm run start:privacy-policy\" \"npm run start:terms-condition\" \"npm run start:about-us\" \"npm run start:social-media\" \"npm run start:payment\" \"npm run start:wallet\" \"npm run start:reviews\" \"npm run start:changelog\" \"npm run start:mail\" \"npm run start:chat\"", "start:dev": "nodemon", "lint": "eslint --config eslint.config.js", "lint:fix": "eslint --config eslint.config.js --fix", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "start:api": "ts-node --files ./api/index.ts", "start:user": "ts-node --files ./user-ms/index.ts", "start:auth": "ts-node --files ./auth-ms/index.ts", "start:boat": "ts-node --files ./boat-ms/index.ts", "start:booking": "ts-node --files ./booking-ms/index.ts", "start:wishlist": "ts-node --files ./wishlist-ms/index.ts", "start:affiliate": "ts-node --files ./affiliate-ms/index.ts", "start:notification": "ts-node --files ./notification-ms/index.ts", "start:newsletter": "ts-node --files ./newsletter-ms/index.ts", "start:faq": "ts-node --files ./faq-ms/index.ts", "start:card": "ts-node --files ./card-ms/index.ts", "start:contact-us": "ts-node --files ./contact-us-ms/index.ts", "start:privacy-policy": "ts-node --files ./privacy-policy-ms/index.ts", "start:terms-condition": "ts-node --files ./terms-condition-ms/index.ts", "start:about-us": "ts-node --files ./about-us-ms/index.ts", "start:social-media": "ts-node --files ./social-media-ms/index.ts", "start:payment": "ts-node --files ./payment-ms/index.ts", "start:wallet": "ts-node --files ./wallet-ms/index.ts", "start:reviews": "ts-node --files ./reviews-ms/index.ts", "start:changelog": "ts-node --files ./changelogs-ms/index.ts", "start:mail": "ts-node --files ./mail-ms/index.ts", "start:chat": "ts-node --files ./chat-ms/index.ts", "pm2:start": "pm2 start ecosystem.config.js", "pm2:start:env": "cross-env NODE_ENV=\"${npm_config_env:-dev}\" pm2 start ecosystem.config.js --env ${npm_config_env:-dev}", "pm2:start:optimized": "node -e \"process.platform === 'win32' ? require('child_process').spawn('cmd.exe', ['/c', 'start-pm2-optimized.bat', process.env.npm_config_env || 'dev'], {stdio: 'inherit'}) : require('child_process').spawn('bash', ['./start-pm2-optimized.sh', process.env.npm_config_env || 'dev'], {stdio: 'inherit'})\"", "pm2:start:regular": "node -e \"process.platform === 'win32' ? require('child_process').spawn('cmd.exe', ['/c', 'start-pm2.bat', process.env.npm_config_env || 'dev'], {stdio: 'inherit'}) : require('child_process').spawn('bash', ['./start-pm2.sh', process.env.npm_config_env || 'dev'], {stdio: 'inherit'})\"", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:restart:env": "cross-env NODE_ENV=\"${npm_config_env:-dev}\" pm2 restart ecosystem.config.js --env ${npm_config_env:-dev}", "pm2:delete": "pm2 delete ecosystem.config.js", "pm2:logs": "pm2 logs", "pm2:monitor": "pm2 monit", "pm2:status": "pm2 status", "pm2:resource-check": "node pm2-monitor.js", "pm2:resource-monitor": "cross-env CONTINUOUS_MONITORING=true node pm2-monitor.js", "pm2:reload-all": "pm2 reload all", "pm2:save": "pm2 save", "pm2:startup": "pm2 startup"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/braintree": "^3.3.14", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.14.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^9.22.0", "nodemon": "^3.1.9", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0"}, "dependencies": {"@types/swagger-ui-express": "^4.1.8", "axios": "^1.9.0", "body-parser": "^1.20.3", "celebrate": "^15.0.3", "colors": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "firebase-admin": "^13.4.0", "form-data": "^4.0.2", "handlebars": "^4.7.8", "helmet": "^8.1.0", "http-proxy-middleware": "^3.0.3", "http-status-codes": "^2.3.0", "i18n": "^0.15.1", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.12.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "onesignal-node": "^3.4.0", "pbkdf2-password-hash": "^3.1.1", "sea-escape": "file:", "socket.io": "^4.8.1", "stripe": "^14.25.0", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "xlsx": "^0.18.5"}}