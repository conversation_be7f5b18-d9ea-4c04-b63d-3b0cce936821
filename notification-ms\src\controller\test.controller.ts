import { Request, Response } from "express";
import sendPushNotification from "../../../shared/services/sendNotification.service";
import FirebaseService from "../../../shared/services/firebase.service";
import User from "../../../user-ms/src/models/User";

/**
 * Test Firebase push notification functionality
 */
export const testFirebaseNotification = async (req: Request, res: Response) => {
  try {
    const { fcmToken, title, message, userId } = req.body;

    // Validate required fields
    if (!fcmToken && !userId) {
      return res.status(400).json({
        success: false,
        message: "Either fcmToken or userId is required",
      });
    }

    let targetToken = fcmToken;

    // If userId is provided, get FCM token from user
    if (userId && !fcmToken) {
      const user = await User.findById(userId);
      if (!user || !user.deviceToken) {
        return res.status(404).json({
          success: false,
          message: "User not found or no FCM token available",
        });
      }
      targetToken = user.deviceToken;
    }

    // Test data
    const testTitle = title || "Test Notification";
    const testMessage = message || "This is a test notification from Sea Escape backend";
    const testData = {
      type: "test",
      timestamp: new Date().toISOString(),
      source: "backend_test",
    };

    // Send notification using the main service
    const result = await sendPushNotification(
      targetToken,
      testMessage,
      testData,
      "test",
      testTitle
    );

    return res.status(200).json({
      success: true,
      message: "Test notification sent",
      data: {
        result,
        sentTo: targetToken,
        title: testTitle,
        message: testMessage,
      },
    });

  } catch (error) {
    console.error("Error sending test notification:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to send test notification",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Check Firebase configuration status
 */
export const checkFirebaseConfig = async (req: Request, res: Response) => {
  try {
    const firebaseService = FirebaseService.getInstance();
    
    const configStatus = {
      isConfigured: firebaseService.isConfigured(),
      hasProjectId: !!global.config.FIREBASE_PROJECT_ID,
      hasPrivateKey: !!global.config.FIREBASE_PRIVATE_KEY,
      hasClientEmail: !!global.config.FIREBASE_CLIENT_EMAIL,
    };

    let initializationStatus = "not_attempted";
    let initializationError = null;

    try {
      firebaseService.initialize();
      initializationStatus = "success";
    } catch (error) {
      initializationStatus = "failed";
      initializationError = error instanceof Error ? error.message : "Unknown error";
    }

    return res.status(200).json({
      success: true,
      message: "Firebase configuration status",
      data: {
        configuration: configStatus,
        initialization: {
          status: initializationStatus,
          error: initializationError,
        },
      },
    });

  } catch (error) {
    console.error("Error checking Firebase config:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to check Firebase configuration",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Validate FCM token format
 */
export const validateFCMToken = async (req: Request, res: Response) => {
  try {
    const { fcmToken } = req.body;

    if (!fcmToken) {
      return res.status(400).json({
        success: false,
        message: "FCM token is required",
      });
    }

    const firebaseService = FirebaseService.getInstance();
    const isValid = firebaseService.isValidFCMToken(fcmToken);

    return res.status(200).json({
      success: true,
      message: "FCM token validation result",
      data: {
        token: fcmToken,
        isValid,
        length: fcmToken.length,
      },
    });

  } catch (error) {
    console.error("Error validating FCM token:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to validate FCM token",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
