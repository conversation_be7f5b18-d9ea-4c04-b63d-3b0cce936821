{"SOCIAL_MEDIA_CREATED": "Social-media-profil erfolg<PERSON>ich erstellt", "SOCIAL_MEDIA_UPDATED": "Social-media-profil erfolgreich aktualisiert", "SOCIAL_MEDIA_DELETED": "Social-media-profil er<PERSON><PERSON><PERSON><PERSON><PERSON>", "SOCIAL_MEDIA_NOT_FOUND": "Social-media-profil nicht gefunden", "SOCIAL_MEDIA_VERSION_EXISTS": "Ein social-media-profil mit dieser version existiert bereits", "SOCIAL_MEDIA_VERSION_REQUIRED": "Version ist erforderlich", "SOCIAL_MEDIA_VERSION_LENGTH": "Die version muss zwischen 1 und 20 zeichen lang sein", "SOCIAL_MEDIA_PLATFORM_REQUIRED": "Plattformname ist erforderlich", "SOCIAL_MEDIA_PLATFORM_INVALID": "Ungültige social-media-plattform", "SOCIAL_MEDIA_USERNAME_REQUIRED": "Benutzername ist erforderlich", "SOCIAL_MEDIA_USERNAME_LENGTH": "Der benutzername muss zwischen 1 und 100 zeichen lang sein", "SOCIAL_MEDIA_URL_INVALID": "Ungültiges url-Format", "SOCIAL_MEDIA_PLATFORMS_REQUIRED": "Mindestens eine plattform ist erforderlich", "SOCIAL_MEDIA_ID_REQUIRED": "Social-media-profil-id ist erforderlich", "SOCIAL_MEDIA_INVALID_ID": "Ungültiges Format der social-media-profil-id", "CANNOT_DELETE_ACTIVE_SOCIAL_MEDIA": "Aktives social-media-profil kann nicht gelöscht werden", "INVALID_ID": "Ungültiges id-Format", "INTERNAL_SERVER_ERROR": "Ein interner serverfehler ist aufgetreten", "FORBIDDEN": "<PERSON>e sind nicht berechtigt, auf diese ressource zuzugreifen.", "TOKEN_REQUIRED": "Authentifizierungstoken erforderlich", "FAIL_TOKEN_EXPIRED": "Sitzung ist abgelaufen. bitte melden sie sich erneut an", "USER_NOT_FOUND": "Benutzer nicht gefunden", "ACCOUNT_INACTIVE": "Konto ist inaktiv oder gelöscht. bitte kontaktieren sie den support für hilfe", "SOMETHING_WENT_WRONG": "Etwas ist schiefgelaufen. bitte versuchen sie es später erneut", "ACCESS_DENIED": "Zugriff verweigert. sie haben keine berechtigung für diese aktion", "INVALID_PAGINATION_PARAMS": "Ungültige paginierungsparameter", "INVALID_FILTER_PARAMS": "Ungültige filterparameter", "INVALID_PLATFORMS_DATA": "Ungültig platforms data Format", "VALIDATION_ERROR": "Validierung fehler aufgetreten", "SOCIAL_MEDIA_PROFILES_RETRIEVED": "Social media profiles ab<PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON>", "SOCIAL_MEDIA_RETRIEVED": "Social media profile ab<PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON>"}