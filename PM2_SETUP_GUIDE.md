# PM2 Setup Guide for Sea Escape Microservices

This guide explains how to use PM2 to manage your Sea Escape microservices.

## Prerequisites

- Node.js installed
- PM2 installed globally (`npm install -g pm2`)

## Starting Microservices with PM2

### Windows

You can start all microservices using:

```bash
npm run pm2:start
```

Or use the batch file:

```bash
start-pm2.bat
```

### Linux/Unix

You can start all microservices using:

```bash
npm run pm2:start
```

Or use the shell script:

```bash
# Make scripts executable first (one-time setup)
chmod +x start-pm2.sh stop-pm2.sh

# Start services
./start-pm2.sh
```

## PM2 Commands

The following npm scripts are available to manage your PM2 services:

- `npm run pm2:start` - Start all microservices
- `npm run pm2:stop` - Stop all microservices
- `npm run pm2:restart` - Restart all microservices
- `npm run pm2:delete` - Delete all microservices from PM2
- `npm run pm2:logs` - View logs for all microservices
- `npm run pm2:monitor` - Open PM2 monitoring dashboard
- `npm run pm2:status` - Check status of all microservices

## Setting Up PM2 as a Service

### Windows

#### Fix PowerShell Execution Policy

Before setting up PM2 as a Windows service, you need to allow PowerShell scripts to run:

1. Open PowerShell as Administrator
2. Run the following command to check the current execution policy:
   ```
   Get-ExecutionPolicy
   ```
3. If it's restricted, run one of these commands to change it:
   ```
   # Option 1: Set for the current user only (recommended)
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

   # Option 2: Set for all users (requires admin)
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned
   ```
4. Confirm the change when prompted

#### Install and Configure PM2 Windows Service

To run PM2 as a Windows service (so your microservices start automatically when the system boots):

1. Open Command Prompt as Administrator (not PowerShell)
2. Run the following commands:

```cmd
:: Install PM2 Windows Service module
npm install pm2-windows-service -g

:: Create the PM2 Windows Service
pm2-service-install -n PM2SeaEscape

:: Start all your microservices
cd /d D:\kmphitech\sea_escape_backend
pm2 start ecosystem.config.js

:: Save the current PM2 process list
pm2 save
```

3. Configure the service to auto-start using Windows Services manager:
   - Open "Services" from Windows Administrative Tools
   - Find "PM2SeaEscape" service
   - Right-click and select "Properties"
   - Set "Startup type" to "Automatic"
   - Click "Apply" and "OK"

### Linux/Unix

To set up PM2 as a service on Linux/Unix systems:

1. Start your applications with PM2:
   ```bash
   pm2 start ecosystem.config.js
   ```

2. Generate a startup script:
   ```bash
   pm2 startup
   ```
   
3. This command will output another command that you should copy and run with sudo privileges.

4. Save the current PM2 process list:
   ```bash
   pm2 save
   ```

5. PM2 will now automatically restart your applications when the server reboots.

## Running PM2 from Command Prompt (Alternative for Windows)

If you prefer to use Command Prompt instead of PowerShell:

1. Open Command Prompt
2. Navigate to your project directory:
   ```
   cd /d D:\kmphitech\sea_escape_backend
   ```
3. Run PM2 commands directly, for example:
   ```
   pm2 start ecosystem.config.js
   ```

## Troubleshooting

If you encounter issues:

1. Check if PM2 is running: `pm2 status`
2. View detailed logs: `pm2 logs`
3. Restart a specific service: `pm2 restart [service-name]`
4. Delete and restart all services: `pm2 delete all` followed by `npm run pm2:start`
5. Check if your Node.js path is correctly set in your environment variables
6. For Linux/Unix, ensure the shell scripts have execute permissions: `chmod +x *.sh`

### Common Errors

#### ts-node not found in PATH

If you see an error like:
```
[PM2][ERROR] Error: Interpreter ts-node is NOT AVAILABLE in PATH. (type 'which ts-node' to double check.)
```

You have three options:

1. **Option 1 (Recommended)**: Use the custom TypeScript runner script (ts-runner.js) that's now included with the project.

2. **Option 2**: Install ts-node globally:
   ```bash
   npm install -g ts-node
   ```

3. **Option 3**: Compile your TypeScript code before running with PM2:
   ```bash
   # First compile TypeScript
   npm run build
   
   # Then modify ecosystem.config.js to use compiled JavaScript
   # Change script paths from *.ts to *.js and remove the interpreter property
   ```

#### Unknown file extension ".ts"

If you see errors like:
```
TypeError [ERR_UNKNOWN_FILE_EXTENSION]: Unknown file extension ".ts"
```

This has been fixed in the latest ecosystem.config.js by using a custom wrapper script (ts-runner.js) that properly registers ts-node before requiring the TypeScript files.

The current setup is:
1. A JavaScript file (ts-runner.js) that registers ts-node
2. The ecosystem.config.js points to this wrapper script
3. The TypeScript file path is passed as an argument

If you're still experiencing issues:

1. Make sure ts-node is installed:
   ```bash
   npm install --save-dev ts-node typescript @types/node
   ```

2. Verify that the ts-runner.js file exists in the project root and has the correct content:
   ```javascript
   // This is a wrapper script to run TypeScript files with ts-node
   require('ts-node').register({ transpileOnly: true, files: true });
   require(process.argv[2]);
   ```

3. Test the wrapper script directly:
   ```bash
   node ts-runner.js ./api/index.ts
   ```

4. If all else fails, consider compiling your TypeScript to JavaScript first and then using PM2 to run the compiled JavaScript files. 