{"BOAT_NOT_FOUND": "Boat not found", "BOAT_CREATED_SUCCESS": "Boat created successfully", "BOAT_UPDATED_SUCCESS": "Boat updated successfully", "BOAT_DELETED_SUCCESS": "Boat deleted successfully", "BOAT_ATTACHMENT_ADDED": "Attachment added successfully", "BOAT_ATTACHMENT_REMOVED": "Attachment removed successfully", "BOAT_AVAILABILITY_SET": "Boat availability set successfully", "BOAT_NOT_OWNER": "You are not the owner of this boat", "BOAT_PLACE_ADDED": "Recommended place added successfully", "BOAT_PLACE_REMOVED": "Recommended place removed successfully", "BOAT_PLACE_NOT_FOUND": "Recommended place not found", "BOAT_CHECKIN_ADDED": "Check-in schedule added successfully", "BOAT_CHECKOUT_ADDED": "Check-out schedule added successfully", "BOAT_CHECKIN_REMOVED": "Check-in schedule removed successfully", "BOAT_CHECKOUT_REMOVED": "Check-out schedule removed successfully", "BOAT_SCHEDULE_ADDED": "Schedule added successfully", "BOAT_SCHEDULE_REMOVED": "Schedule removed successfully", "BOAT_SCHEDULE_NOT_FOUND": "Schedule not found", "BOAT_INVALID_SCHEDULE_TYPE": "Invalid schedule type. Must be either 'check-in' or 'check-out'", "BOAT_INVALID_DOCUMENT_IDS": "One or more document IDs are invalid or do not belong to you", "PLACE_NOT_FOUND": "Recommended place not found", "PLACE_CREATED_SUCCESS": "Recommended place created successfully", "PLACE_UPDATED_SUCCESS": "Recommended place updated successfully", "PLACE_DELETED_SUCCESS": "Recommended place deleted successfully", "PLACE_NOT_OWNER": "You are not the owner of this recommended place", "PLACE_IMAGE_ADDED": "Recommended place image added successfully", "PLACE_IMAGES_REMOVED": "Recommended place images removed successfully", "FACILITY_NOT_FOUND": "Facility not found", "FACILITY_CREATED_SUCCESS": "Facility created successfully", "FACILITY_UPDATED_SUCCESS": "Facility updated successfully", "FACILITY_DELETED_SUCCESS": "Facility deleted successfully", "FACILITY_IMAGE_ADDED": "Facility image added successfully", "FACILITY_IMAGES_REMOVED": "Facility images removed successfully", "SOMETHING_WENT_WRONG": "Something went wrong, please try again", "UNAUTHORIZED": "Unauthorized access", "DOCUMENT_USE_USER_API": "For document uploads, please use the document upload API in the user service", "DOCUMENT_INVALID_IDS": "One or more document IDs are invalid or do not belong to you", "DOCUMENT_UPLOAD_SUCCESS": "Document uploaded successfully", "TOKEN_REQUIRED": "Authorization token is required", "FAIL_TOKEN_EXPIRED": "Session is expired. Please log in again.", "ACTIVITY_NOT_FOUND": "Activity not found", "ACTIVITY_CREATED_SUCCESS": "Activity created successfully", "ACTIVITY_UPDATED_SUCCESS": "Activity updated successfully", "ACTIVITY_DELETED_SUCCESS": "Activity deleted successfully", "ACTIVITY_NOT_OWNER": "You are not the owner of this activity", "ACTIVITY_INVALID_ID": "Invalid activity ID", "USER_NOT_FOUND": "User not found.", "ACCOUNT_INACTIVE": "Account is inactive or deleted. Please contact support for assistance.", "BOAT_TYPES_FETCHED": "Boat types fetched successfully", "BOAT_LOCATIONS_FETCHED": "Boat locations fetched successfully", "POPULAR_DESTINATIONS_FETCHED": "Popular destinations fetched successfully", "NEARBY_BOATS_FETCHED": "Nearby boats and activities fetched successfully", "LAT_LNG_REQUIRED": "Latitude and longitude are required for location-based search", "NO_FILES_UPLOADED": "No files were uploaded", "BOAT_IMAGES_ADDED": "Images added to boat successfully", "BOAT_IMAGES_REMOVED": "Images removed from boat successfully", "IMAGE_IDS_REQUIRED": "Image IDs are required for removal", "OWNER_ACTIVITIES_FETCHED": "Owner activities fetched successfully", "ACCESS_DENIED": "Access denied.", "FORBIDDEN": "You are not authorized to access this resource.", "INTERNAL_SERVER_ERROR": "Internal server error occurred.", "INVALID_ID": "Invalid ID provided.", "INVALID_PAGINATION_PARAMS": "Invalid pagination parameters.", "INVALID_FILTER_PARAMS": "Invalid filter parameters.", "AFFILIATE_CODE_NOT_FOUND": "Affiliate code does not exist. Please check the code and try again.", "AFFILIATE_CODE_NOT_ACTIVE": "Affiliate code is not active. Only approved affiliate codes can be used.", "AFFILIATE_CODE_EXPIRED": "Affiliate code has expired and can no longer be used.", "AFFILIATE_CODE_VALID": "Affiliate code is valid and active.", "AFFILIATE_CODE_VALIDATION_ERROR": "Error occurred while validating affiliate code. Please try again.", "AFFILIATE_CODE_INVALID_FORMAT": "Affiliate code format is invalid. Code must be 6-20 alphanumeric characters.", "REFERRAL_NAME_NONE": "No referral name provided.", "REFERRAL_NAME_NOT_FOUND": "Referral name not found or not active. Please check the name and try again.", "REFERRAL_NAME_VALID": "Referral name is valid and active.", "REFERRAL_NAME_VALIDATION_ERROR": "Error occurred while validating referral name. Please try again.", "INVALID_BOAT_ID": "Invalid boat ID provided.", "BOAT_NOT_FOUND_OR_NOT_AVAILABLE": "Boat not found or not available.", "BOAT_OWNER_NOT_FOUND": "Boat owner not found.", "CANNOT_BOOK_OWN_BOAT": "You cannot book your own boat.", "BOAT_FETCH_SUCCESS": "Boat fetched successfully.", "INVALID_IMAGE_IDS": "One or more image IDs are invalid.", "VIDEO_IDS_REQUIRED": "Video IDs are required for removal.", "INVALID_VIDEO_IDS": "One or more video IDs are invalid.", "BOAT_VIDEOS_REMOVED": "Videos removed from boat successfully."}