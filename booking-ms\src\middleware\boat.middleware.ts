import { NextFunction } from "express";
import { StatusCodes } from "http-status-codes";
import Booking from "../models/Booking";
import Boat from "../../../boat-ms/src/models/Boat";
import logger from "../../../shared/services/logger.service";

export const isBoatOwner = async (req: any, res: any, next: NextFunction) => {
  try {
    const bookingId = req.params.id;
    const userId = req.user._id;

    // Get booking details
    const booking = await Booking.findById(bookingId);
    if (!booking) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOOKING_NOT_FOUND"),
      });
    }

    // Get boat details
    const boat = await Boat.findById(booking.boatId);
    if (!boat) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: req.__("BOAT_NOT_FOUND_OR_NOT_OWNER"),
      });
    }

    // Check if user is boat owner - convert both to string for comparison
    if (boat.ownerId.toString() !== userId.toString()) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: req.__("BOOKING_INSUFFICIENT_PERMISSIONS"),
      });
    }

    next();
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: req.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
