// This is a wrapper script to run TypeScript files with ts-node
const { spawn } = require("child_process");
const path = require("path");
const fs = require("fs");

// Get the TypeScript file path from the command-line arguments
const tsFilePath = process.argv[2];

if (!tsFilePath) {
  console.error("Error: No TypeScript file specified");
  process.exit(1);
}

// Detect if this is the API service
const isApiService = tsFilePath.includes("api/index.ts");

// Create a modified environment to avoid port conflicts
const env = Object.assign({}, process.env);

// If this is the API service, check for port 3000 conflicts and adjust if needed
if (isApiService) {
  // Check if port 3000 is already in use by trying to open a server on it
  const net = require("net");
  const server = net.createServer();

  server.once("error", (err) => {
    if (err.code === "EADDRINUSE") {
      console.log(
        "Port 3000 is already in use, setting API to use port 4000 instead",
      );
      // Set PORT env var to 4000 to avoid conflict
      env.PORT = "4000";
    }
    startTsNode();
  });

  server.once("listening", () => {
    // Port is free, close the server
    server.close(() => {
      startTsNode();
    });
  });

  // Try to listen on port 3000 to check if it's available
  server.listen(3000);
} else {
  // Not API service, start normally
  startTsNode();
}

function startTsNode() {
  // Construct the path to ts-node executable
  const tsNodeBin = path.resolve(__dirname, "node_modules", ".bin", "ts-node");

  // Check if ts-node exists
  if (!fs.existsSync(tsNodeBin)) {
    console.error(`Error: ts-node not found at ${tsNodeBin}`);
    console.error(
      "Please ensure ts-node is installed: npm install --save-dev ts-node",
    );
    process.exit(1);
  }

  // Get TypeScript compiler options from tsconfig.json to ensure compatibility
  let tsConfigOptions = [];
  try {
    const tsConfigPath = path.resolve(__dirname, "tsconfig.json");
    if (fs.existsSync(tsConfigPath)) {
      // Add compiler options to make sure TypeScript runs correctly
      tsConfigOptions = ["--project", tsConfigPath];
    }
  } catch (err) {
    console.warn("Warning: Could not read tsconfig.json", err.message);
  }

  // Launch the TypeScript file using ts-node in a child process with appropriate flags
  const child = spawn(
    tsNodeBin,
    [
      "--transpile-only", // Faster execution without type checking
      "--files", // Include files from tsconfig.json
      ...tsConfigOptions, // Use project's tsconfig.json
      tsFilePath,
    ],
    {
      stdio: "inherit",
      shell: process.platform === "win32", // Use shell on Windows
      env: env, // Use our potentially modified env variables
    },
  );

  // Handle process termination
  child.on("exit", (code) => {
    process.exit(code);
  });

  // Forward signals to the child process
  ["SIGINT", "SIGTERM", "SIGQUIT"].forEach((signal) => {
    process.on(signal, () => {
      if (!child.killed) {
        child.kill(signal);
      }
    });
  });
}
