import mongoose from "mongoose";
import Message, { IMessage } from "../models/Chat";
import { MessageStatus, MessageType } from "../constants/socket.constants";
import logger from "../../../shared/services/logger.service";

interface MessageInput {
  senderId: mongoose.Types.ObjectId;
  receiverId: mongoose.Types.ObjectId;
  content?: string;
  messageType: MessageType;
  mediaUrl?: string;
}

/**
 * Save a new message to the database
 */
export const saveMessage = async (messageData: MessageInput, res?: any): Promise<IMessage> => {
  try {
    const message = new Message({
      senderId: messageData.senderId,
      receiverId: messageData.receiverId,
      content: messageData.content,
      messageType: messageData.messageType,
      mediaUrl: messageData.mediaUrl,
      status: MessageStatus.SENT
    });

    await message.save();
    return message;
  } catch (error: any) {
    logger.error(`Error saving message: ${error.message}`, {
      service: "chat-ms",
      error
    });
    throw error
  }
};

/**
 * Get populated message data for socket events
 */
export const getPopulatedMessage = async (messageId: string): Promise<any> => {
  try {
    const populatedMessage = await Message.aggregate([
      {
        $match: { _id: new mongoose.Types.ObjectId(messageId) }
      },
      {
        $lookup: {
          from: "users",
          localField: "senderId",
          foreignField: "_id",
          as: "senderInfo"
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "receiverId",
          foreignField: "_id",
          as: "receiverInfo"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "senderInfo.avatar",
          foreignField: "_id",
          as: "senderAvatarFile"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "receiverInfo.avatar",
          foreignField: "_id",
          as: "receiverAvatarFile"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "mediaUrl",
          foreignField: "_id",
          as: "mediaFile"
        }
      },
      {
        $project: {
          _id: 1,
          content: 1,
          messageType: 1,
          status: 1,
          readAt: 1,
          createdAt: 1,
          updatedAt: 1,
          mediaUrl: {
            $cond: [
              { $and: [{ $ne: ["$messageType", "text"] }, { $gt: [{ $size: "$mediaFile" }, 0] }] },
              {
                $concat: [
                  global.config.FILE_BASE_URL,
                  { $arrayElemAt: ["$mediaFile.location", 0] }
                ]
              },
              "$mediaUrl"
            ]
          },
          sender: {
            _id: { $arrayElemAt: ["$senderInfo._id", 0] },
            username: { $arrayElemAt: ["$senderInfo.username", 0] },
            avatarUrl: {
              $cond: [
                { $gt: [{ $size: "$senderAvatarFile" }, 0] },
                {
                  $concat: [
                    global.config.FILE_BASE_URL,
                    { $arrayElemAt: ["$senderAvatarFile.location", 0] }
                  ]
                },
                null
              ]
            }
          },
          receiver: {
            _id: { $arrayElemAt: ["$receiverInfo._id", 0] },
            username: { $arrayElemAt: ["$receiverInfo.username", 0] },
            avatarUrl: {
              $cond: [
                { $gt: [{ $size: "$receiverAvatarFile" }, 0] },
                {
                  $concat: [
                    global.config.FILE_BASE_URL,
                    { $arrayElemAt: ["$receiverAvatarFile.location", 0] }
                  ]
                },
                null
              ]
            }
          }
        }
      }
    ]);

    return populatedMessage[0] || null;
  } catch (error: any) {
    logger.error(`Error getting populated message: ${error.message}`, {
      service: "chat-ms",
      error
    });
    throw error;
  }
};

/**
 * Get single conversation object matching getRecentChats structure
 */
export const getSingleConversation = async (userId: string, otherUserId: string): Promise<any> => {
  try {
    // Find the most recent message between these two users
    const conversation = await Message.aggregate([
      {
        $match: {
          $or: [
            { senderId: new mongoose.Types.ObjectId(userId), receiverId: new mongoose.Types.ObjectId(otherUserId) },
            { senderId: new mongoose.Types.ObjectId(otherUserId), receiverId: new mongoose.Types.ObjectId(userId) }
          ],
          deletedAt: null
        }
      },
      {
        $sort: { createdAt: -1 }
      },
      { $limit: 1 },
      {
        $lookup: {
          from: "users",
          localField: "senderId",
          foreignField: "_id",
          as: "senderInfo"
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "receiverId",
          foreignField: "_id",
          as: "receiverInfo"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "senderInfo.avatar",
          foreignField: "_id",
          as: "senderAvatarFile"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "receiverInfo.avatar",
          foreignField: "_id",
          as: "receiverAvatarFile"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "mediaUrl",
          foreignField: "_id",
          as: "mediaFile"
        }
      },
      {
        $project: {
          _id: new mongoose.Types.ObjectId(otherUserId), // Use other user's ID as conversation ID
          lastMessage: {
            _id: "$_id",
            content: "$content",
            messageType: "$messageType",
            mediaUrl: {
              $cond: [
                { $and: [{ $ne: ["$messageType", "text"] }, { $gt: [{ $size: "$mediaFile" }, 0] }] },
                {
                  $concat: [
                    global.config.FILE_BASE_URL,
                    { $arrayElemAt: ["$mediaFile.location", 0] }
                  ]
                },
                "$mediaUrl"
              ]
            },
            timestamp: "$createdAt",
            isRead: { $eq: ["$status", "read"] },
            sender: {
              _id: { $arrayElemAt: ["$senderInfo._id", 0] },
              username: { $arrayElemAt: ["$senderInfo.username", 0] },
              avatarUrl: {
                $cond: [
                  { $gt: [{ $size: "$senderAvatarFile" }, 0] },
                  {
                    $concat: [
                      global.config.FILE_BASE_URL,
                      { $arrayElemAt: ["$senderAvatarFile.location", 0] }
                    ]
                  },
                  null
                ]
              }
            },
            receiver: {
              _id: { $arrayElemAt: ["$receiverInfo._id", 0] },
              username: { $arrayElemAt: ["$receiverInfo.username", 0] },
              avatarUrl: {
                $cond: [
                  { $gt: [{ $size: "$receiverAvatarFile" }, 0] },
                  {
                    $concat: [
                      global.config.FILE_BASE_URL,
                      { $arrayElemAt: ["$receiverAvatarFile.location", 0] }
                    ]
                  },
                  null
                ]
              }
            }
          },
          user: {
            $cond: [
              { $eq: [{ $arrayElemAt: ["$senderInfo._id", 0] }, new mongoose.Types.ObjectId(userId)] },
              // If current user is sender, return receiver info
              {
                _id: { $arrayElemAt: ["$receiverInfo._id", 0] },
                username: { $arrayElemAt: ["$receiverInfo.username", 0] },
                avatarUrl: {
                  $cond: [
                    { $gt: [{ $size: "$receiverAvatarFile" }, 0] },
                    {
                      $concat: [
                        global.config.FILE_BASE_URL,
                        { $arrayElemAt: ["$receiverAvatarFile.location", 0] }
                      ]
                    },
                    null
                  ]
                }
              },
              // If current user is receiver, return sender info
              {
                _id: { $arrayElemAt: ["$senderInfo._id", 0] },
                username: { $arrayElemAt: ["$senderInfo.username", 0] },
                avatarUrl: {
                  $cond: [
                    { $gt: [{ $size: "$senderAvatarFile" }, 0] },
                    {
                      $concat: [
                        global.config.FILE_BASE_URL,
                        { $arrayElemAt: ["$senderAvatarFile.location", 0] }
                      ]
                    },
                    null
                  ]
                }
              }
            ]
          },
          lastMessageAt: "$createdAt",
          unreadCount: 0 // TODO: Calculate actual unread count if needed
        }
      }
    ]);

    if (conversation.length > 0) {
      return conversation[0];
    } else {
      // No conversation exists, create a new conversation structure with user details
      const otherUser = await mongoose.model("User").aggregate([
        {
          $match: { _id: new mongoose.Types.ObjectId(otherUserId) }
        },
        {
          $lookup: {
            from: "files",
            localField: "avatar",
            foreignField: "_id",
            as: "avatarFile"
          }
        },
        {
          $project: {
            _id: 1,
            username: 1,
            avatarUrl: {
              $cond: [
                { $gt: [{ $size: "$avatarFile" }, 0] },
                {
                  $concat: [
                    global.config.FILE_BASE_URL,
                    { $arrayElemAt: ["$avatarFile.location", 0] }
                  ]
                },
                null
              ]
            }
          }
        }
      ]);

      return {
        _id: otherUserId,
        lastMessage: null,
        user: otherUser[0] || null,
        lastMessageAt: new Date(),
        unreadCount: 0
      };
    }
  } catch (error: any) {
    logger.error(`Error getting single conversation: ${error.message}`, {
      service: "chat-ms",
      error
    });
    throw error;
  }
};

/**
 * Get or create conversation with populated user details
 */
export const getOrCreateConversation = async (senderId: string, receiverId: string): Promise<any> => {
  try {
    // Check if conversation exists
    const existingMessage = await Message.findOne({
      $or: [
        { senderId: senderId, receiverId: receiverId },
        { senderId: receiverId, receiverId: senderId }
      ],
      deletedAt: null
    }).sort({ createdAt: -1 });

    let conversationData;

    if (existingMessage) {
      // Get existing conversation with populated data
      conversationData = await Message.aggregate([
        {
          $match: {
            $or: [
              { senderId: new mongoose.Types.ObjectId(senderId), receiverId: new mongoose.Types.ObjectId(receiverId) },
              { senderId: new mongoose.Types.ObjectId(receiverId), receiverId: new mongoose.Types.ObjectId(senderId) }
            ],
            deletedAt: null
          }
        },
        { $sort: { createdAt: -1 } },
        { $limit: 1 },
        {
          $lookup: {
            from: "users",
            localField: "senderId",
            foreignField: "_id",
            as: "senderInfo"
          }
        },
        {
          $lookup: {
            from: "users",
            localField: "receiverId",
            foreignField: "_id",
            as: "receiverInfo"
          }
        },
        {
          $lookup: {
            from: "files",
            localField: "senderInfo.avatar",
            foreignField: "_id",
            as: "senderAvatarFile"
          }
        },
        {
          $lookup: {
            from: "files",
            localField: "receiverInfo.avatar",
            foreignField: "_id",
            as: "receiverAvatarFile"
          }
        },
        {
          $project: {
            _id: 1,
            content: 1,
            messageType: 1,
            mediaUrl: 1,
            status: 1,
            createdAt: 1,
            updatedAt: 1,
            sender: {
              _id: { $arrayElemAt: ["$senderInfo._id", 0] },
              username: { $arrayElemAt: ["$senderInfo.username", 0] },
              avatarUrl: {
                $cond: [
                  { $gt: [{ $size: "$senderAvatarFile" }, 0] },
                  {
                    $concat: [
                      global.config.FILE_BASE_URL,
                      { $arrayElemAt: ["$senderAvatarFile.location", 0] }
                    ]
                  },
                  null
                ]
              }
            },
            receiver: {
              _id: { $arrayElemAt: ["$receiverInfo._id", 0] },
              username: { $arrayElemAt: ["$receiverInfo.username", 0] },
              avatarUrl: {
                $cond: [
                  { $gt: [{ $size: "$receiverAvatarFile" }, 0] },
                  {
                    $concat: [
                      global.config.FILE_BASE_URL,
                      { $arrayElemAt: ["$receiverAvatarFile.location", 0] }
                    ]
                  },
                  null
                ]
              }
            },
            lastMessageAt: "$createdAt"
          }
        }
      ]);

      return conversationData[0] || null;
    } else {
      // Create new conversation structure with user details
      const userDetails = await mongoose.model("User").aggregate([
        {
          $match: {
            _id: { $in: [new mongoose.Types.ObjectId(senderId), new mongoose.Types.ObjectId(receiverId)] }
          }
        },
        {
          $lookup: {
            from: "files",
            localField: "avatar",
            foreignField: "_id",
            as: "avatarFile"
          }
        },
        {
          $project: {
            _id: 1,
            username: 1,
            avatarUrl: {
              $cond: [
                { $gt: [{ $size: "$avatarFile" }, 0] },
                {
                  $concat: [
                    global.config.FILE_BASE_URL,
                    { $arrayElemAt: ["$avatarFile.location", 0] }
                  ]
                },
                null
              ]
            }
          }
        }
      ]);

      const sender = userDetails.find(user => user._id.toString() === senderId);
      const receiver = userDetails.find(user => user._id.toString() === receiverId);

      return {
        _id: null,
        content: null,
        messageType: null,
        mediaUrl: null,
        status: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        sender,
        receiver,
        lastMessageAt: new Date(),
        isNewConversation: true
      };
    }
  } catch (error: any) {
    logger.error(`Error getting or creating conversation: ${error.message}`, {
      service: "chat-ms",
      error
    });
    throw error;
  }
};

/**
 * Mark a message as read and update the readAt timestamp
 */
export const markMessageAsRead = async (messageId: string, res?: any): Promise<IMessage | null> => {
  try {
    const message = await Message.findByIdAndUpdate(
      messageId,
      {
        status: MessageStatus.READ,
        readAt: new Date()
      },
      { new: true }
    );
    
    if (!message) {
      throw new Error(res.__("MESSAGE_NOT_FOUND"));
    }
    
    return message;
  } catch (error: any) {
    logger.error(`Error marking message as read: ${error.message}`, {
      service: "chat-ms",
      error
    });
    throw error;
  }
};

/**
 * Mark a message as deleted (soft delete)
 */
export const markMessageAsDeleted = async (messageId: string, userId: mongoose.Types.ObjectId, res?: any): Promise<IMessage | null> => {
  try {
    const message = await Message.findByIdAndUpdate(
      messageId,
      {
        status: MessageStatus.DELETED,
        deletedAt: new Date(),
        deletedBy: userId
      },
      { new: true }
    );
    
    if (!message) {
      throw new Error(res.__("MESSAGE_NOT_FOUND"));
    }
    
    return message;
  } catch (error: any) {
    logger.error(`Error deleting message: ${error.message}`, {
      service: "chat-ms",
      error
    });
    throw error;
  }
};

/**
 * Get recent conversations for a user
 */
export const getRecentConversations = async (userId: string, limit: number = 20, page: number = 1, res?: any, search?: string): Promise<any> => {
  try {
    const skip = (page - 1) * limit;

    // Find the most recent message for each unique conversation
    const conversations = await Message.aggregate([
      {
        $match: {
          $or: [
            { senderId: new mongoose.Types.ObjectId(userId) },
            { receiverId: new mongoose.Types.ObjectId(userId) },
          ],
          deletedAt: null
        }
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $group: {
          _id: {
            $cond: [
              { $eq: ["$senderId", new mongoose.Types.ObjectId(userId)] },
              "$receiverId",
              "$senderId"
            ]
          },
          lastMessage: { $first: "$$ROOT" }
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "user"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "user.avatar",
          foreignField: "_id",
          as: "avatarFile"
        }
      },
      // Add search filter based on username if search parameter is provided
      ...(search ? [{
        $match: {
          "user.username": { $regex: search, $options: "i" }
        }
      }] : []),
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          lastMessage: 1,
          user: {
            _id: { $arrayElemAt: ["$user._id", 0] },
            username: { $arrayElemAt: ["$user.username", 0] },
            avatarUrl: {
              $cond: [
                { $gt: [{ $size: "$avatarFile" }, 0] },
                {
                  $concat: [
                    global.config.FILE_BASE_URL,
                    { $arrayElemAt: ["$avatarFile.location", 0] }
                  ]
                },
                null
              ]
            }
          },
          lastMessageAt: "$lastMessage.createdAt"
        }
      }
    ]);

    // Get total count for pagination
    const totalCount = await Message.aggregate([
      {
        $match: {
          $or: [
            { senderId: new mongoose.Types.ObjectId(userId) },
            { receiverId: new mongoose.Types.ObjectId(userId) }
          ],
          deletedAt: null
        }
      },
      {
        $group: {
          _id: {
            $cond: [
              { $eq: ["$senderId", new mongoose.Types.ObjectId(userId)] },
              "$receiverId",
              "$senderId"
            ]
          }
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "user"
        }
      },
      // Add search filter for total count if search parameter is provided
      ...(search ? [{
        $match: {
          "user.username": { $regex: search, $options: "i" }
        }
      }] : []),
      { $count: "total" }
    ]);

    const total = totalCount[0]?.total || 0;

    return {
      conversations,
      pagination: {
        page,
        size: limit,
        totalCount: total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error: any) {
    logger.error(`Error fetching recent conversations: ${error.message}`, {
      service: "chat-ms",
      error
    });
    throw new Error(res.__("CONVERSATIONS_FETCH_FAILED"));
  }
};

/**
 * Get messages between two users with pagination and populated user details
 */
export const getConversationMessages = async (
  userId: string,
  receiverId: string,
  limit: number = 20,
  page: number = 1,
  res: any
): Promise<any> => {
  try {
    const skip = (page - 1) * limit;

    // Use aggregation to get messages with populated user details
    const messages = await Message.aggregate([
      {
        $match: {
          $or: [
            { senderId: new mongoose.Types.ObjectId(userId), receiverId: new mongoose.Types.ObjectId(receiverId) },
            { senderId: new mongoose.Types.ObjectId(receiverId), receiverId: new mongoose.Types.ObjectId(userId) }
          ],
          deletedAt: null
        }
      },
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: "users",
          localField: "senderId",
          foreignField: "_id",
          as: "senderInfo"
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "receiverId",
          foreignField: "_id",
          as: "receiverInfo"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "senderInfo.avatar",
          foreignField: "_id",
          as: "senderAvatarFile"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "receiverInfo.avatar",
          foreignField: "_id",
          as: "receiverAvatarFile"
        }
      },
      {
        $lookup: {
          from: "files",
          localField: "mediaUrl",
          foreignField: "_id",
          as: "mediaFile"
        }
      },
      {
        $project: {
          _id: 1,
          content: 1,
          messageType: 1,
          status: 1,
          readAt: 1,
          createdAt: 1,
          updatedAt: 1,
          mediaUrl: {
            $cond: [
              { $and: [{ $ne: ["$messageType", "text"] }, { $gt: [{ $size: "$mediaFile" }, 0] }] },
              {
                $concat: [
                  global.config.FILE_BASE_URL,
                  { $arrayElemAt: ["$mediaFile.location", 0] }
                ]
              },
              "$mediaUrl"
            ]
          },
          sender: {
            _id: { $arrayElemAt: ["$senderInfo._id", 0] },
            username: { $arrayElemAt: ["$senderInfo.username", 0] },
            avatarUrl: {
              $cond: [
                { $gt: [{ $size: "$senderAvatarFile" }, 0] },
                {
                  $concat: [
                    global.config.FILE_BASE_URL,
                    { $arrayElemAt: ["$senderAvatarFile.location", 0] }
                  ]
                },
                null
              ]
            }
          },
          receiver: {
            _id: { $arrayElemAt: ["$receiverInfo._id", 0] },
            username: { $arrayElemAt: ["$receiverInfo.username", 0] },
            avatarUrl: {
              $cond: [
                { $gt: [{ $size: "$receiverAvatarFile" }, 0] },
                {
                  $concat: [
                    global.config.FILE_BASE_URL,
                    { $arrayElemAt: ["$receiverAvatarFile.location", 0] }
                  ]
                },
                null
              ]
            }
          }
        }
      }
    ]);

    const totalCount = await Message.countDocuments({
      $or: [
        { senderId: userId, receiverId: receiverId },
        { senderId: receiverId, receiverId: userId }
      ],
      deletedAt: null
    });

    return {
      messages,
      pagination: {
        page,
        size: limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  } catch (error: any) {
    logger.error(`Error fetching conversation messages: ${error.message}`, {
      service: "chat-ms",
      error
    });
    throw new Error(res.__("CHAT_HISTORY_FETCH_FAILED"));
  }
};