import * as admin from "firebase-admin";

/**
 * Firebase Service for managing Firebase Admin SDK operations
 * Handles initialization and provides utility methods for Firebase operations
 */
class FirebaseService {
  private static instance: FirebaseService;
  private app: any = null;
  private isInitialized = false;

  private constructor() {}

  /**
   * Get singleton instance of FirebaseService
   */
  public static getInstance(): FirebaseService {
    if (!FirebaseService.instance) {
      FirebaseService.instance = new FirebaseService();
    }
    return FirebaseService.instance;
  }

  /**
   * Initialize Firebase Admin SDK
   */
  public initialize(): any {
    if (this.isInitialized && this.app) {
      return this.app;
    }

    try {
      // Check if Firebase is already initialized
      this.app = admin.app();
      this.isInitialized = true;
    } catch (error) {
      // Initialize Firebase if not already done
      const privateKey = global.config.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n');
      
      if (!global.config.FIREBASE_PROJECT_ID || !privateKey || !global.config.FIREBASE_CLIENT_EMAIL) {
        throw new Error("Firebase configuration is missing. Please check FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, and FIREBASE_CLIENT_EMAIL in config.");
      }

      this.app = admin.initializeApp({
        credential: admin.credential.cert({
          projectId: global.config.FIREBASE_PROJECT_ID,
          privateKey: privateKey,
          clientEmail: global.config.FIREBASE_CLIENT_EMAIL,
        }),
      });
      
      this.isInitialized = true;
      console.log("Firebase Admin SDK initialized successfully");
    }

    return this.app;
  }

  /**
   * Get Firebase Messaging instance
   */
  public getMessaging(): any {
    if (!this.isInitialized) {
      this.initialize();
    }
    return admin.messaging();
  }

  /**
   * Validate FCM token format
   */
  public isValidFCMToken(token: string): boolean {
    // Basic FCM token validation
    // FCM tokens are typically 152+ characters long and contain alphanumeric characters, hyphens, and underscores
    const fcmTokenRegex = /^[a-zA-Z0-9_-]{140,}$/;
    return fcmTokenRegex.test(token);
  }

  /**
   * Send notification to single device
   */
  public async sendToDevice(
    token: string,
    title: string,
    body: string,
    data?: { [key: string]: string }
  ): Promise<string> {
    const messaging = this.getMessaging();
    
    const message: any = {
      token,
      notification: {
        title,
        body,
      },
      data: data || {},
      android: {
        notification: {
          channelId: "sea_escape_notifications",
          priority: "high" as const,
          defaultSound: true,
        },
      },
      apns: {
        payload: {
          aps: {
            alert: {
              title,
              body,
            },
            sound: "default",
            badge: 1,
          },
        },
      },
    };

    return await messaging.send(message);
  }

  /**
   * Send notification to multiple devices
   */
  public async sendToMultipleDevices(
    tokens: string[],
    title: string,
    body: string,
    data?: { [key: string]: string }
  ): Promise<any> {
    const messaging = this.getMessaging();

    const message: any = {
      tokens,
      notification: {
        title,
        body,
      },
      data: data || {},
      android: {
        notification: {
          channelId: "sea_escape_notifications",
          priority: "high" as const,
          defaultSound: true,
        },
      },
      apns: {
        payload: {
          aps: {
            alert: {
              title,
              body,
            },
            sound: "default",
            badge: 1,
          },
        },
      },
    };

    return await messaging.sendMulticast(message);
  }

  /**
   * Check if Firebase is properly configured
   */
  public isConfigured(): boolean {
    return !!(
      global.config.FIREBASE_PROJECT_ID &&
      global.config.FIREBASE_PRIVATE_KEY &&
      global.config.FIREBASE_CLIENT_EMAIL
    );
  }
}

export default FirebaseService;
