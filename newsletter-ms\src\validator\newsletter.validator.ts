import { NextFunction } from "express";
import { StatusCodes } from "http-status-codes";
import <PERSON><PERSON> from "joi";
import mongoose from "mongoose";

// Schema for creating a newsletter
const createNewsletterSchema = Joi.object({
  header: Joi.string().required().messages({
    "string.empty": "NEWSLETTER.HEADER_REQUIRED",
    "any.required": "NEWSLETTER.HEADER_REQUIRED",
  }),
  content: Joi.string().required().messages({
    "string.empty": "NEWSLETTER.CONTENT_REQUIRED",
    "any.required": "NEWSLETTER.CONTENT_REQUIRED",
  }),
  // Remove image from Jo<PERSON> schema, as multer handles file upload
});

// Schema for updating a newsletter
const updateNewsletterSchema = Joi.object({
  header: Joi.string().optional().messages({
    "string.empty": "NEWSLETTER_HEADER_REQUIRED",
  }),
  content: Joi.string().optional().messages({
    "string.empty": "NEWSLETTER_CONTENT_REQUIRED",
  }),
  // Remove image from Jo<PERSON> schema, as multer handles file upload
});

// Schema for validating MongoDB ObjectId
const paramsSchema = Joi.object({
  id: Joi.string()
    .custom((value, helpers) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        return helpers.error("any.invalid");
      }
      return value;
    }, "MongoDB ObjectId validation")
    .required()
    .messages({
      "string.empty": "NEWSLETTER_ID_REQUIRED",
      "any.required": "NEWSLETTER_ID_REQUIRED",
      "any.invalid": "NEWSLETTER_INVALID_ID",
    }),
});

// Middleware to validate newsletter creation
export const validateNewsletterCreation = (
  req: any,
  res: any,
  next: NextFunction
) => {
  const { error } = createNewsletterSchema.validate(req.body, {
    abortEarly: false,
  });
  if (error) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: res.__(error.details[0].message),
      errors: error.details.map((err) => ({
        field: err.path[0],
        message: res.__(err.message),
      })),
    });
  }
  // Validate image presence via multer (req.file)
  if (!req.file) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: res.__("NEWSLETTER.IMAGE_REQUIRED"),
      errors: [
        { field: "image", message: res.__("NEWSLETTER.IMAGE_REQUIRED") },
      ],
    });
  }
  next();
};

// Middleware to validate newsletter update
export const validateNewsletterUpdate = (
  req: any,
  res: any,
  next: NextFunction
) => {
  const { error } = updateNewsletterSchema.validate(req.body, {
    abortEarly: false,
  });
  if (error) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: res.__(error.details[0].message),
      errors: error.details.map((err) => ({
        field: err.path[0],
        message: res.__(err.message),
      })),
    });
  }
  // If image is being updated, validate presence via multer (req.file)
  if (req.body.image !== undefined && !req.file) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: res.__("NEWSLETTER.IMAGE_REQUIRED"),
      errors: [
        { field: "image", message: res.__("NEWSLETTER.IMAGE_REQUIRED") },
      ],
    });
  }
  next();
};

// Middleware to validate URL parameters
export const validateNewsletterParams = (
  req: any,
  res: any,
  next: NextFunction
) => {
  const { error } = paramsSchema.validate(req.params, { abortEarly: false });
  if (error) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: res.__(error.details[0].message),
      errors: error.details.map((err) => ({
        field: err.path[0],
        message: res.__(err.message),
      })),
    });
  }
  next();
};
