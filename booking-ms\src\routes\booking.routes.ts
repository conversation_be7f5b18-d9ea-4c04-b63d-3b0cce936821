import { Router } from "express";
import {
  createBooking,
  getMyBookings,
  getBookingsByBoatAndUser,
  getBookingById,
  modifyBooking,
  cancelBooking,
  acceptBooking,
  rejectBooking,
  getBookingByBoatOwner,
  updateBookingStatus,
  getBookingStatus,
  getBookingsByBoatId,
} from "../controller/booking.controller";
import {
  createBookingValidator,
  updateBookingValidator,
  getBookingValidator,
  rejectBookingValidator,
  updateStatusValidator,
} from "../validator/booking.validator";
import { isBoatOwner } from "../middleware/boat.middleware";

const router = Router();

// User booking routes
router.post("/", createBookingValidator, createBooking);
router.get("/me", getMyBookings);
router.get("/boat/:boatId", getBookingsByBoatAndUser);
router.get("/boat-owner", getBookingByBoatOwner);
router.get("/boat/:boatId/bookings", getBookingsByBoatId); // New route for getting bookings by boat ID
router.get("/:id", getBookingValidator, getBookingById);
router.put("/:id", updateBookingValidator, modifyBooking);
router.delete("/:id", cancelBooking);

// New routes for payment integration
router.put("/update-status/:id", updateStatusValidator, updateBookingStatus);
router.get("/status/:id", getBookingStatus);

// Boat owner routes
router.post("/:id/accept", isBoatOwner, getBookingValidator, acceptBooking);
router.post("/:id/reject", isBoatOwner, rejectBookingValidator, rejectBooking);

export default router;
