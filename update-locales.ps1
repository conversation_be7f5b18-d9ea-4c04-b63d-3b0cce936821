$commonMessages = @{
    "TOKEN_REQUIRED" = "Authorization token is required."
    "FAIL_TOKEN_EXPIRED" = "Session is expired. Please log in again."
    "USER_NOT_FOUND" = "User not found."
    "ACCOUNT_INACTIVE" = "Account is inactive. Please contact support for assistance."
    "SOMETHING_WENT_WRONG" = "Something went wrong. Please try again later."
    "ACCESS_DENIED" = "Access denied. You don't have permission to perform this action."
    "FORBIDDEN" = "You are not authorized to access this resource."
    "INTERNAL_SERVER_ERROR" = "An internal server error occurred. Please try again later."
    "INVALID_ID" = "Invalid ID format provided."
    "INVALID_PAGINATION_PARAMS" = "Invalid pagination parameters."
    "INVALID_FILTER_PARAMS" = "Invalid filter parameters."
}

$commonMessagesES = @{
    "TOKEN_REQUIRED" = "Se requiere token de autorización."
    "FAIL_TOKEN_EXPIRED" = "La sesión ha caducado. Por favor, inicie sesión de nuevo."
    "USER_NOT_FOUND" = "Usuario no encontrado."
    "ACCOUNT_INACTIVE" = "La cuenta está inactiva. Por favor, contacte con soporte para obtener ayuda."
    "SOMETHING_WENT_WRONG" = "Algo salió mal. Por favor, inténtelo de nuevo más tarde."
    "ACCESS_DENIED" = "Acceso denegado. No tiene permiso para realizar esta acción."
    "FORBIDDEN" = "No está autorizado para acceder a este recurso."
    "INTERNAL_SERVER_ERROR" = "Se produjo un error interno del servidor. Por favor, inténtelo de nuevo más tarde."
    "INVALID_ID" = "Formato de ID proporcionado no válido."
    "INVALID_PAGINATION_PARAMS" = "Parámetros de paginación no válidos."
    "INVALID_FILTER_PARAMS" = "Parámetros de filtro no válidos."
}

$commonMessagesDE = @{
    "TOKEN_REQUIRED" = "Autorisierungstoken ist erforderlich."
    "FAIL_TOKEN_EXPIRED" = "Sitzung ist abgelaufen. Bitte melden Sie sich erneut an."
    "USER_NOT_FOUND" = "Benutzer nicht gefunden."
    "ACCOUNT_INACTIVE" = "Konto ist inaktiv. Bitte wenden Sie sich an den Support."
    "SOMETHING_WENT_WRONG" = "Etwas ist schief gelaufen. Bitte versuchen Sie es später erneut."
    "ACCESS_DENIED" = "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion."
    "FORBIDDEN" = "Sie sind nicht berechtigt, auf diese Ressource zuzugreifen."
    "INTERNAL_SERVER_ERROR" = "Ein interner Serverfehler ist aufgetreten. Bitte versuchen Sie es später erneut."
    "INVALID_ID" = "Ungültiges ID-Format angegeben."
    "INVALID_PAGINATION_PARAMS" = "Ungültige Paginierungsparameter."
    "INVALID_FILTER_PARAMS" = "Ungültige Filterparameter."
}

$microservices = Get-ChildItem -Directory -Path . -Filter "*-ms"

foreach ($ms in $microservices) {
    $localesPath = Join-Path $ms.FullName "src\locales"
    
    if (Test-Path $localesPath) {
        Write-Host "Processing $($ms.Name)..."
        
        # Process English locale
        $enFile = Join-Path $localesPath "en.json"
        if (Test-Path $enFile) {
            $content = Get-Content $enFile -Raw | ConvertFrom-Json -AsHashtable
            foreach ($key in $commonMessages.Keys) {
                if (-not $content.ContainsKey($key)) {
                    $content[$key] = $commonMessages[$key]
                }
            }
            $content | ConvertTo-Json -Depth 10 | Set-Content $enFile
        }
        
        # Process Spanish locale
        $esFile = Join-Path $localesPath "es.json"
        if (Test-Path $esFile) {
            $content = Get-Content $esFile -Raw | ConvertFrom-Json -AsHashtable
            foreach ($key in $commonMessagesES.Keys) {
                if (-not $content.ContainsKey($key)) {
                    $content[$key] = $commonMessagesES[$key]
                }
            }
            $content | ConvertTo-Json -Depth 10 | Set-Content $esFile
        }
        
        # Process German locale
        $deFile = Join-Path $localesPath "de.json"
        if (Test-Path $deFile) {
            $content = Get-Content $deFile -Raw | ConvertFrom-Json -AsHashtable
            foreach ($key in $commonMessagesDE.Keys) {
                if (-not $content.ContainsKey($key)) {
                    $content[$key] = $commonMessagesDE[$key]
                }
            }
            $content | ConvertTo-Json -Depth 10 | Set-Content $deFile
        }
    }
}

Write-Host "All locale files have been updated!"
