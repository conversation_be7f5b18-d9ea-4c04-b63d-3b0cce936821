import multer from "multer";
import path from "path";
import fs from "fs";

/**
 * Dynamic Multer upload service
 * @param destinationPath - The path where files should be stored
 * @returns multer instance with custom storage
 */
export const uploadMulter = (destinationPath: string) => {
  try {
    const storage = multer.diskStorage({
      destination: (req: any, file: any, cb: any) => {
        const uploadPath = path.resolve(
          __dirname,
          "../uploads/",
          destinationPath,
        );
        try {
          if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
          }
        } catch (error) {
          console.error("Error creating upload directory:", error);
        }
        cb(null, uploadPath);
      },
      filename: (req: any, file: any, cb: any) => {
        cb(null, `${Date.now()}-${file.originalname.replace(/\s+/g, "-")}`);
      },
    });

    return multer({ storage: storage });
  } catch (error) {
    console.log("error", error);
    return {};
  }
};
