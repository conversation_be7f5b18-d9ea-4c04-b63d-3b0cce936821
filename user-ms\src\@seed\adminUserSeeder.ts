import mongoose from "mongoose";
// Use require instead of import for pbkdf2-password-hash
// import { encrypt } from "../../../shared/helper/utils";
import User, { UserRole, UserStatus, UserCurrentRole, UserLanguage, UserCurrency } from "../models/User";
import logger from "../../../shared/services/logger.service";
import config from "../../../shared/config.json";

// Import the global config type
import "../../../shared/types/global";

// Define a simple encrypt function to avoid the dependency on shared/helper/utils
async function encrypt(password: string): Promise<string> {
  const passwordHash = require('pbkdf2-password-hash');
  
  return await passwordHash.hash(password, {
    iterations: 100,
    digest: "sha1",
    keylen: 16,
    saltlen: 16,
  });
}

/**
 * Admin User Seeder
 * Creates an admin user with predefined credentials
 */
async function seedAdminUser() {
  try {
    // Get environment and config
    const environment = process.env.NODE_ENV || "dev";
    const envConfig = JSON.parse(JSON.stringify(config))[environment];
    
    // Set global config
    global.config = envConfig;
    
    // Get database URL from config
    const mongoURI = process.env.MONGO_URL || envConfig.db;
    
    // Connect to MongoDB
    await mongoose.connect(mongoURI);
    
    // Check if any admin user already exists
    const existingAdmin = await User.findOne({ role: UserRole.Admin });
    
    if (existingAdmin) {
      console.log(`Admin user already exists with email: ${existingAdmin.email}. Skipping admin seeder.`);
      logger.info(`Admin user already exists with email: ${existingAdmin.email}. Skipping admin seeder.`);
      return;
    }
    
    // Check if user with same email exists
    const existingUser = await User.findOne({ email: "<EMAIL>" });
    
    if (existingUser) {
      console.log(`User <NAME_EMAIL> already exists. Skipping admin seeder.`);
      logger.info(`User <NAME_EMAIL> already exists. Skipping admin seeder.`);
      return;
    }
    
    // Hash the password
    const hashedPassword = await encrypt("123456");
    
    // Create admin user
    const adminUser = await User.create({
      username: "admin",
      email: "<EMAIL>",
      password: hashedPassword,
      role: UserRole.Admin,
      currentRole: UserCurrentRole.User, // Default current role
      status: UserStatus.Active,
      language: UserLanguage.English,
      currency: UserCurrency.USD,
      interestedIn: [],
    });
    
    console.log(`Admin user created successfully: ${adminUser.email}`);
    logger.info(`Admin user created successfully: ${adminUser.email}`);
    
  } catch (error) {
    logger.error("Admin seeder error:", error);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info("Admin seeder finished.");
  }
}

// Run the seeder if this file is directly executed
// if (require.main === module) {
  seedAdminUser()
    .then(() => {
      console.log("Admin user seeded successfully");
      process.exit(0);
    })
    .catch((error) => {
      logger.error("Failed to seed admin user:", error);
      process.exit(1);
    });
// }

export default seedAdminUser; 