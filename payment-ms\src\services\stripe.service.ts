import Stripe from "stripe";
import logger from "../../../shared/services/logger.service";
import { UserCurrency } from "../../../user-ms/src/models/User";

// Configuration
const Config = global.config;

// Currency conversion rates (simplified for available currencies)
const CURRENCY_CONVERSION_RATES: Record<UserCurrency, number> = {
  [UserCurrency.USD]: 1.0,
  [UserCurrency.EUR]: 0.85,
  [UserCurrency.GBP]: 0.73,
};

// Currency ISO codes mapping
const CURRENCY_ISO_CODES: Record<UserCurrency, string> = {
  [UserCurrency.USD]: "usd",
  [UserCurrency.EUR]: "eur",
  [UserCurrency.GBP]: "gbp",
};

class StripeService {
  private stripe: Stripe;

  constructor() {
    // Initialize Stripe with secret key
    this.stripe = new Stripe(Config?.STRIPE_SECRET_KEY || "sk_test_...", {
      apiVersion: "2023-10-16",
    });
  }

  /**
   * Create a Setup Intent for card setup (server-side only, no client secret needed)
   */
  async createSetupIntent(customerId?: string): Promise<{
    success: boolean;
    setupIntentId?: string;
    error?: string;
  }> {
    try {
      const setupIntentParams: Stripe.SetupIntentCreateParams = {
        usage: "off_session",
        payment_method_types: ["card"],
        confirm: true, // Auto-confirm server-side
      };

      if (customerId) {
        setupIntentParams.customer = customerId;
      }

      const setupIntent = await this.stripe.setupIntents.create(setupIntentParams);

      return {
        success: true,
        setupIntentId: setupIntent.id,
      };
    } catch (error: any) {
      logger.error("Failed to create Stripe setup intent", {
        service: "payment-ms",
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to create setup intent",
      };
    }
  }

  /**
   * Process payment using saved source (preferred method)
   */
  async processPaymentWithSource(
    amount: number,
    customerId: string,
    sourceId: string,
    options?: {
      currency?: string;
      description?: string;
      metadata?: Record<string, string>;
    },
  ): Promise<{
    success: boolean;
    paymentIntent?: {
      id: string;
      status: string;
      amount: number;
      currency: string;
      charges: any;
      metadata?: Record<string, string>;
    };
    error?: string;
  }> {
    try {
      const currency = options?.currency || "usd";
      const amountInCents = Math.round(amount * 100);

      // Create charge with source (sources use charges, not payment intents)
      const chargeParams: Stripe.ChargeCreateParams = {
        amount: amountInCents,
        currency: currency.toLowerCase(),
        customer: customerId,
        source: sourceId,
      };

      if (options?.description) {
        chargeParams.description = options.description;
      }

      if (options?.metadata) {
        chargeParams.metadata = options.metadata;
      }

      const charge = await this.stripe.charges.create(chargeParams);

      if (charge.status === "succeeded") {
        return {
          success: true,
          paymentIntent: {
            id: charge.id,
            status: charge.status,
            amount: charge.amount / 100,
            currency: charge.currency,
            charges: charge,
            metadata: charge.metadata,
          },
        };
      } else {
        return {
          success: false,
          error: `Payment failed with status: ${charge.status}`,
        };
      }
    } catch (error: any) {
      logger.error("Stripe payment with source failed", {
        service: "payment-ms",
        error: error.message,
        amount,
        customerId,
        sourceId,
      });

      return {
        success: false,
        error: error.message || "Payment with source failed",
      };
    }
  }

  /**
   * Process payment using card token (fallback method)
   */
  async processPaymentWithToken(
    amount: number,
    cardToken: string,
    options?: {
      currency?: string;
      description?: string;
      metadata?: Record<string, string>;
      customerId?: string;
    },
  ): Promise<{
    success: boolean;
    paymentIntent?: {
      id: string;
      status: string;
      amount: number;
      currency: string;
      charges: any;
      metadata?: Record<string, string>;
    };
    error?: string;
  }> {
    try {
      const currency = options?.currency || "usd";
      const amountInCents = Math.round(amount * 100);

      // First create a payment method from the token
      const paymentMethod = await this.stripe.paymentMethods.create({
        type: "card",
        card: {
          token: cardToken,
        },
      });

      // Then create and confirm payment intent
      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: amountInCents,
        currency: currency.toLowerCase(),
        payment_method: paymentMethod.id,
        confirmation_method: "automatic",
        confirm: true,
      };

      if (options?.customerId) {
        paymentIntentParams.customer = options.customerId;
      }

      if (options?.description) {
        paymentIntentParams.description = options.description;
      }

      if (options?.metadata) {
        paymentIntentParams.metadata = options.metadata;
      }

      const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams);

      if (paymentIntent.status === "succeeded") {
        return {
          success: true,
          paymentIntent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount / 100,
            currency: paymentIntent.currency,
            charges: paymentIntent.latest_charge || null,
            metadata: paymentIntent.metadata,
          },
        };
      } else {
        return {
          success: false,
          error: `Payment failed with status: ${paymentIntent.status}`,
        };
      }
    } catch (error: any) {
      logger.error("Stripe payment with token failed", {
        service: "payment-ms",
        error: error.message,
        amount,
      });

      return {
        success: false,
        error: error.message || "Payment with token failed",
      };
    }
  }

  /**
   * Process a payment using Stripe Payment Intent (Method 1 - Automatic Confirmation)
   */
  async processPayment(
    amount: number,
    paymentMethodId: string,
    customerId?: string,
    options?: {
      currency?: string;
      description?: string;
      orderId?: string;
      metadata?: Record<string, string>;
      isPaymentMethodToken?: boolean;
    },
  ): Promise<{
    success: boolean;
    paymentIntent?: {
      id: string;
      status: string;
      amount: number;
      currency: string;
      charges: any;
      metadata?: Record<string, string>;
    };
    error?: string;
  }> {
    try {
      const currency = options?.currency || "usd";

      // Convert amount to cents (Stripe expects amounts in smallest currency unit)
      const amountInCents = Math.round(amount * 100);

      // Method 1: Create and automatically confirm payment intent
      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: amountInCents,
        currency: currency.toLowerCase(),
        payment_method: paymentMethodId,
        confirmation_method: "automatic",
        confirm: true,
        // Remove return_url for server-side payments
      };

      if (customerId) {
        paymentIntentParams.customer = customerId;
      }

      if (options?.description) {
        paymentIntentParams.description = options.description;
      }

      if (options?.metadata) {
        paymentIntentParams.metadata = options.metadata;
      }

      const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams);

      if (paymentIntent.status === "succeeded") {
        return {
          success: true,
          paymentIntent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount / 100, // Convert back to dollars
            currency: paymentIntent.currency,
            charges: paymentIntent.latest_charge || null,
            metadata: paymentIntent.metadata,
          },
        };
      } else if (paymentIntent.status === "requires_action") {
        // Handle 3D Secure or other authentication
        return {
          success: false,
          error: "Payment requires additional authentication. Please use frontend integration for 3D Secure.",
        };
      } else {
        return {
          success: false,
          error: `Payment failed with status: ${paymentIntent.status}`,
        };
      }
    } catch (error: any) {
      logger.error("Stripe payment processing failed", {
        service: "payment-ms",
        error: error.message,
        amount,
      });

      return {
        success: false,
        error: error.message || "Payment processing failed",
      };
    }
  }

  /**
   * Alternative Method 2: Direct Charge API (Simpler approach)
   */
  async processDirectCharge(
    amount: number,
    paymentMethodId: string,
    customerId?: string,
    options?: {
      currency?: string;
      description?: string;
      metadata?: Record<string, string>;
    },
  ): Promise<{
    success: boolean;
    charge?: {
      id: string;
      status: string;
      amount: number;
      currency: string;
      paid: boolean;
      metadata?: Record<string, string>;
    };
    error?: string;
  }> {
    try {
      const currency = options?.currency || "usd";
      const amountInCents = Math.round(amount * 100);

      const chargeParams: Stripe.ChargeCreateParams = {
        amount: amountInCents,
        currency: currency.toLowerCase(),
        source: paymentMethodId, // Can be payment method ID or card token
        description: options?.description || "Sea Escape booking payment",
      };

      if (customerId) {
        chargeParams.customer = customerId;
      }

      if (options?.metadata) {
        chargeParams.metadata = options.metadata;
      }

      const charge = await this.stripe.charges.create(chargeParams);

      return {
        success: charge.paid,
        charge: {
          id: charge.id,
          status: charge.status,
          amount: charge.amount / 100,
          currency: charge.currency,
          paid: charge.paid,
          metadata: charge.metadata,
        },
      };
    } catch (error: any) {
      logger.error("Stripe direct charge failed", {
        service: "payment-ms",
        error: error.message,
        amount,
      });

      return {
        success: false,
        error: error.message || "Direct charge failed",
      };
    }
  }

  /**
   * Alternative Method 3: Two-step Payment Intent (Create then Confirm)
   */
  async createPaymentIntent(
    amount: number,
    currency: string = "usd",
    options?: {
      customerId?: string;
      description?: string;
      metadata?: Record<string, string>;
    },
  ): Promise<{
    success: boolean;
    paymentIntent?: Stripe.PaymentIntent;
    clientSecret?: string;
    error?: string;
  }> {
    try {
      const amountInCents = Math.round(amount * 100);

      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: amountInCents,
        currency: currency.toLowerCase(),
        automatic_payment_methods: {
          enabled: true,
        },
      };

      if (options?.customerId) {
        paymentIntentParams.customer = options.customerId;
      }

      if (options?.description) {
        paymentIntentParams.description = options.description;
      }

      if (options?.metadata) {
        paymentIntentParams.metadata = options.metadata;
      }

      const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams);

      return {
        success: true,
        paymentIntent,
        clientSecret: paymentIntent.client_secret!,
      };
    } catch (error: any) {
      logger.error("Failed to create payment intent", {
        service: "payment-ms",
        error: error.message,
        amount,
      });

      return {
        success: false,
        error: error.message || "Failed to create payment intent",
      };
    }
  }

  /**
   * Confirm a payment intent with payment method
   */
  async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethodId: string,
  ): Promise<{
    success: boolean;
    paymentIntent?: Stripe.PaymentIntent;
    error?: string;
  }> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.confirm(paymentIntentId, {
        payment_method: paymentMethodId,
      });

      return {
        success: paymentIntent.status === "succeeded",
        paymentIntent,
      };
    } catch (error: any) {
      logger.error("Failed to confirm payment intent", {
        service: "payment-ms",
        error: error.message,
        paymentIntentId,
      });

      return {
        success: false,
        error: error.message || "Failed to confirm payment intent",
      };
    }
  }

  /**
   * Alternative Method 4: Payment with saved payment method
   */
  async processPaymentWithSavedMethod(
    amount: number,
    customerId: string,
    paymentMethodId: string,
    options?: {
      currency?: string;
      description?: string;
      metadata?: Record<string, string>;
    },
  ): Promise<{
    success: boolean;
    paymentIntent?: {
      id: string;
      status: string;
      amount: number;
      currency: string;
      charges: any;
      metadata?: Record<string, string>;
    };
    error?: string;
  }> {
    try {
      const currency = options?.currency || "usd";
      const amountInCents = Math.round(amount * 100);

      // First, attach the payment method to customer if not already attached
      try {
        await this.stripe.paymentMethods.attach(paymentMethodId, {
          customer: customerId,
        });
      } catch (attachError: any) {
        // Payment method might already be attached, continue
        if (!attachError.message.includes("already been attached")) {
          throw attachError;
        }
      }

      // Create payment intent with customer and payment method
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: amountInCents,
        currency: currency.toLowerCase(),
        customer: customerId,
        payment_method: paymentMethodId,
        confirmation_method: "automatic",
        confirm: true,
        description: options?.description,
        metadata: options?.metadata,
      });

      if (paymentIntent.status === "succeeded") {
        return {
          success: true,
          paymentIntent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount / 100,
            currency: paymentIntent.currency,
            charges: paymentIntent.latest_charge || null,
            metadata: paymentIntent.metadata,
          },
        };
      } else {
        return {
          success: false,
          error: `Payment failed with status: ${paymentIntent.status}`,
        };
      }
    } catch (error: any) {
      logger.error("Payment with saved method failed", {
        service: "payment-ms",
        error: error.message,
        amount,
        customerId,
      });

      return {
        success: false,
        error: error.message || "Payment with saved method failed",
      };
    }
  }

  /**
   * Retrieve a payment intent by ID
   */
  async findPaymentIntent(paymentIntentId: string): Promise<{
    success: boolean;
    paymentIntent?: any;
    error?: string;
  }> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      return {
        success: true,
        paymentIntent,
      };
    } catch (error: any) {
      logger.error("Failed to find Stripe payment intent", {
        service: "payment-ms",
        paymentIntentId,
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to find payment intent",
      };
    }
  }

  /**
   * Refund a payment
   */
  async refundPayment(
    paymentIntentId: string,
    amount?: number,
  ): Promise<{
    success: boolean;
    refund?: any;
    error?: string;
  }> {
    try {
      const refundParams: Stripe.RefundCreateParams = {
        payment_intent: paymentIntentId,
      };

      if (amount) {
        refundParams.amount = Math.round(amount * 100); // Convert to cents
      }

      const refund = await this.stripe.refunds.create(refundParams);

      return {
        success: true,
        refund,
      };
    } catch (error: any) {
      logger.error("Failed to refund Stripe payment", {
        service: "payment-ms",
        paymentIntentId,
        amount,
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to refund payment",
      };
    }
  }

  /**
   * Create a Stripe customer
   */
  async createCustomer(customerData: {
    email: string;
    name?: string;
    metadata?: Record<string, string>;
  }): Promise<{
    success: boolean;
    customer?: Stripe.Customer;
    error?: string;
  }> {
    try {
      const customer = await this.stripe.customers.create({
        email: customerData.email,
        name: customerData.name,
        metadata: customerData.metadata,
      });

      return {
        success: true,
        customer,
      };
    } catch (error: any) {
      logger.error("Failed to create Stripe customer", {
        service: "payment-ms",
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to create customer",
      };
    }
  }

  /**
   * Create a payment method for a customer
   */
  async createPaymentMethod(
    customerId: string,
    paymentMethodId: string,
  ): Promise<{
    success: boolean;
    paymentMethod?: Stripe.PaymentMethod;
    error?: string;
  }> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      return {
        success: true,
        paymentMethod,
      };
    } catch (error: any) {
      logger.error("Failed to create Stripe payment method", {
        service: "payment-ms",
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to create payment method",
      };
    }
  }

  /**
   * Validate a card by creating a payment method
   */
  async validateCard(cardData: {
    number: string;
    expiry: string;
    cvv: string;
    name: string;
    email: string;
  }): Promise<{
    isValid: boolean;
    paymentMethodId?: string;
    lastFourDigits?: string;
    cardType?: string;
    error?: string;
  }> {
    try {
      const [expiryMonth, expiryYear] = cardData.expiry.split("/");

      // Create a payment method with the card details
      const paymentMethod = await this.stripe.paymentMethods.create({
        type: "card",
        card: {
          number: cardData.number,
          exp_month: parseInt(expiryMonth),
          exp_year: parseInt(`20${expiryYear}`),
          cvc: cardData.cvv,
        },
        billing_details: {
          name: cardData.name,
          email: cardData.email,
        },
      });

      return {
        isValid: true,
        paymentMethodId: paymentMethod.id,
        lastFourDigits: paymentMethod.card?.last4,
        cardType: paymentMethod.card?.brand,
      };
    } catch (error: any) {
      logger.error("Stripe card validation failed", {
        service: "payment-ms",
        error: error.message,
      });

      return {
        isValid: false,
        error: error.message || "Card validation failed",
      };
    }
  }

  /**
   * Create a Stripe Express account for withdrawals (replaces Braintree merchant account)
   */
  async createExpressAccount(accountDetails: {
    firstName: string;
    lastName: string;
    email: string;
    accountNumber: string;
    routingNumber?: string;
    bankName: string;
    currency?: UserCurrency;
  }): Promise<{
    success: boolean;
    accountId?: string;
    error?: string;
  }> {
    try {
      const environment = process.env.NODE_ENV || "dev";
      const currency = accountDetails.currency || UserCurrency.USD;
      const currencyIsoCode = CURRENCY_ISO_CODES[currency];

      logger.info("Creating Stripe Express account", {
        service: "payment-ms",
        environment,
        firstName: accountDetails.firstName,
        lastName: accountDetails.lastName,
        bankName: accountDetails.bankName,
        currency: currencyIsoCode
      });

      if (environment === "production") {
        // In production, create a real Express account
        const account = await this.stripe.accounts.create({
          type: "express",
          country: "US", // This should be dynamic based on user location
          email: accountDetails.email,
          capabilities: {
            card_payments: { requested: true },
            transfers: { requested: true },
          },
          business_type: "individual",
          individual: {
            first_name: accountDetails.firstName,
            last_name: accountDetails.lastName,
            email: accountDetails.email,
          },
          external_account: {
            object: "bank_account",
            country: "US",
            currency: currencyIsoCode,
            account_holder_name: `${accountDetails.firstName} ${accountDetails.lastName}`,
            account_number: accountDetails.accountNumber,
            routing_number: accountDetails.routingNumber,
          },
        });

        return {
          success: true,
          accountId: account.id,
        };
      } else {
        // In development, simulate account creation
        const accountId = `acct_${currency}_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

        return {
          success: true,
          accountId: accountId,
        };
      }
    } catch (error: any) {
      logger.error("Error creating Stripe Express account", {
        service: "payment-ms",
        error: error.message
      });

      return {
        success: false,
        error: error.message || "Failed to create Express account",
      };
    }
  }

  /**
   * Convert amount from one currency to another
   */
  private convertCurrency(
    amount: number,
    fromCurrency: UserCurrency,
    toCurrency: UserCurrency
  ): number {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    // Convert to USD first (as base currency)
    const amountInUSD =
      fromCurrency === UserCurrency.USD
        ? amount
        : amount / CURRENCY_CONVERSION_RATES[fromCurrency];

    // Then convert from USD to target currency
    const convertedAmount =
      toCurrency === UserCurrency.USD
        ? amountInUSD
        : amountInUSD * CURRENCY_CONVERSION_RATES[toCurrency];

    return parseFloat(convertedAmount.toFixed(2));
  }

  /**
   * Process a withdrawal to bank account using Stripe transfers
   */
  async processWithdrawalToBank(
    amount: number,
    bankDetails: {
      accountHolderName: string;
      bankName: string;
      accountNumber: string;
      routingNumber?: string;
      currency?: UserCurrency;
      email?: string;
    },
    stripeAccountId?: string,
  ): Promise<{
    success: boolean;
    transfer?: {
      id: string;
      status: string;
      amount: string;
      currency: string;
      createdAt: Date;
    };
    error?: string;
  }> {
    try {
      const environment = process.env.NODE_ENV || "dev";
      const currency = bankDetails.currency || UserCurrency.USD;
      const currencyIsoCode = CURRENCY_ISO_CODES[currency];

      // Convert amount to the specified currency if needed
      const convertedAmount = this.convertCurrency(
        amount,
        UserCurrency.USD, // Assuming base amount is in USD
        currency
      );

      logger.info("Processing bank withdrawal via Stripe", {
        service: "payment-ms",
        environment,
        originalAmount: amount,
        convertedAmount,
        currency,
        bankDetails: {
          accountHolderName: bankDetails.accountHolderName,
          bankName: bankDetails.bankName,
          accountNumberEnding: bankDetails.accountNumber.slice(-4),
        },
      });

      let transferId;
      let transferStatus;

      if (environment === "production") {
        // PRODUCTION: Use Stripe's actual API
        try {
          if (!stripeAccountId) {
            throw new Error("Stripe account ID is required for production withdrawals");
          }

          // Create a transfer to the connected account
          const transfer = await this.stripe.transfers.create({
            amount: Math.round(convertedAmount * 100), // Convert to cents
            currency: currencyIsoCode,
            destination: stripeAccountId,
            description: `Withdrawal to ${bankDetails.bankName} account ending in ${bankDetails.accountNumber.slice(-4)}`,
          });

          transferId = transfer.id;
          transferStatus = "pending"; // Stripe transfers are initially pending
        } catch (apiError: any) {
          logger.error("Stripe API error in production", {
            service: "payment-ms",
            error: apiError.message
          });
          throw new Error(`Stripe API error: ${apiError.message}`);
        }
      } else {
        // DEVELOPMENT/SANDBOX: Simulate the withdrawal
        const rand = Math.random();
        if (rand > 0.9) {
          // 10% chance of failure for testing error handling
          throw new Error("Simulated withdrawal failure in sandbox environment");
        }

        transferId = `tr_sandbox_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
        transferStatus = "pending"; // In real API this would initially be pending
      }

      // Return success result
      return {
        success: true,
        transfer: {
          id: transferId,
          status: transferStatus,
          amount: convertedAmount.toString(),
          currency: currencyIsoCode,
          createdAt: new Date(),
        },
      };
    } catch (error: any) {
      logger.error("Failed to process bank withdrawal via Stripe", {
        service: "payment-ms",
        amount,
        currency: bankDetails.currency,
        bankDetails: {
          accountHolderName: bankDetails.accountHolderName,
          bankName: bankDetails.bankName,
          accountNumberEnding: bankDetails.accountNumber.slice(-4),
        },
        error: error.message,
      });

      return {
        success: false,
        error: error.message || "Failed to process bank withdrawal",
      };
    }
  }
}

export default new StripeService();
