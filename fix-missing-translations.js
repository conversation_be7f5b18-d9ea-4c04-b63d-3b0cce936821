const fs = require('fs');
const path = require('path');

// All microservices to process
const MICROSERVICES = [
  'auth-ms',
  'user-ms',
  'boat-ms',
  'booking-ms',
  'wishlist-ms',
  'affiliate-ms',
  'notification-ms',
  'newsletter-ms',
  'faq-ms',
  'card-ms',
  'contact-us-ms',
  'privacy-policy-ms',
  'terms-condition-ms',
  'about-us-ms',
  'social-media-ms',
  'payment-ms',
  'wallet-ms',
  'reviews-ms',
  'chat-ms',
  'changelogs-ms',
  'mail-ms'
];

const LOCALES = ['en', 'es', 'de'];

// Translation mappings for common patterns
const TRANSLATION_PATTERNS = {
  // Common endings and patterns
  '_SUCCESS': {
    es: '_EXITOSO',
    de: '_ERFOLGREICH'
  },
  '_FAILED': {
    es: '_FALLIDO',
    de: '_FEHLGESCHLAGEN'
  },
  '_REQUIRED': {
    es: '_REQUERIDO',
    de: '_ERFORDERLICH'
  },
  '_NOT_FOUND': {
    es: '_NO_ENCONTRADO',
    de: '_NICHT_GEFUNDEN'
  },
  '_CREATED': {
    es: '_CREADO',
    de: '_ERSTELLT'
  },
  '_UPDATED': {
    es: '_ACTUALIZADO',
    de: '_AKTUALISIERT'
  },
  '_DELETED': {
    es: '_ELIMINADO',
    de: '_GELÖSCHT'
  },
  '_FETCHED': {
    es: '_OBTENIDO',
    de: '_ABGERUFEN'
  }
};

// Common word translations
const WORD_TRANSLATIONS = {
  'successfully': { es: 'exitosamente', de: 'erfolgreich' },
  'failed': { es: 'falló', de: 'fehlgeschlagen' },
  'required': { es: 'requerido', de: 'erforderlich' },
  'not found': { es: 'no encontrado', de: 'nicht gefunden' },
  'created': { es: 'creado', de: 'erstellt' },
  'updated': { es: 'actualizado', de: 'aktualisiert' },
  'deleted': { es: 'eliminado', de: 'gelöscht' },
  'fetched': { es: 'obtenido', de: 'abgerufen' },
  'invalid': { es: 'inválido', de: 'ungültig' },
  'error': { es: 'error', de: 'Fehler' },
  'please': { es: 'por favor', de: 'bitte' },
  'try again': { es: 'inténtalo de nuevo', de: 'versuchen Sie es erneut' },
  'later': { es: 'más tarde', de: 'später' }
};

function analyzeTranslationGaps() {
  console.log('🔍 ANALYZING TRANSLATION GAPS ACROSS ALL MICROSERVICES\n');
  
  const gaps = {};
  
  // Include shared locales
  const allServices = ['shared', ...MICROSERVICES];
  
  allServices.forEach(service => {
    console.log(`📁 Analyzing ${service}...`);
    
    const localeFiles = {};
    const allKeys = new Set();
    
    // Read all locale files for this service
    LOCALES.forEach(locale => {
      const filePath = service === 'shared' 
        ? path.join(__dirname, 'shared', 'locales', `${locale}.json`)
        : path.join(__dirname, service, 'src', 'locales', `${locale}.json`);
      
      if (fs.existsSync(filePath)) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          const messages = JSON.parse(content);
          localeFiles[locale] = messages;
          
          // Collect all unique keys
          Object.keys(messages).forEach(key => allKeys.add(key));
          
          console.log(`  ✅ ${locale}.json: ${Object.keys(messages).length} messages`);
        } catch (error) {
          console.log(`  ❌ Error reading ${filePath}: ${error.message}`);
          localeFiles[locale] = {};
        }
      } else {
        console.log(`  ⚠️  ${locale}.json not found`);
        localeFiles[locale] = {};
      }
    });
    
    // Find missing translations
    const missingTranslations = {};
    
    LOCALES.forEach(locale => {
      missingTranslations[locale] = [];
      allKeys.forEach(key => {
        if (!localeFiles[locale][key]) {
          missingTranslations[locale].push(key);
        }
      });
    });
    
    // Report gaps
    let hasGaps = false;
    LOCALES.forEach(locale => {
      if (missingTranslations[locale].length > 0) {
        hasGaps = true;
        console.log(`  🔍 ${locale}.json missing ${missingTranslations[locale].length} translations:`);
        missingTranslations[locale].forEach(key => {
          console.log(`    - ${key}`);
        });
      }
    });
    
    if (!hasGaps) {
      console.log(`  ✅ ${service} - All translations complete!`);
    }
    
    gaps[service] = {
      localeFiles,
      allKeys: Array.from(allKeys),
      missingTranslations
    };
  });
  
  return gaps;
}

function generateTranslation(englishText, targetLocale) {
  if (!englishText) return '';
  
  let translation = englishText;
  
  // Apply word-by-word translations
  Object.entries(WORD_TRANSLATIONS).forEach(([english, translations]) => {
    const regex = new RegExp(`\\b${english}\\b`, 'gi');
    translation = translation.replace(regex, translations[targetLocale] || english);
  });
  
  return translation;
}

function fixMissingTranslations(gaps) {
  console.log('\n🔧 FIXING MISSING TRANSLATIONS...\n');
  
  let totalFixed = 0;
  
  Object.entries(gaps).forEach(([service, data]) => {
    const { localeFiles, missingTranslations } = data;
    
    let serviceFixed = 0;
    
    LOCALES.forEach(locale => {
      if (locale === 'en') return; // Skip English as it's the source
      
      const missing = missingTranslations[locale];
      if (missing.length === 0) return;
      
      console.log(`📝 Fixing ${missing.length} missing translations in ${service}/${locale}.json...`);
      
      missing.forEach(key => {
        const englishText = localeFiles['en'][key];
        if (englishText) {
          const translation = generateTranslation(englishText, locale);
          localeFiles[locale][key] = translation;
          serviceFixed++;
          totalFixed++;
          console.log(`  + ${key}: "${translation}"`);
        }
      });
      
      // Write the updated file
      const filePath = service === 'shared' 
        ? path.join(__dirname, 'shared', 'locales', `${locale}.json`)
        : path.join(__dirname, service, 'src', 'locales', `${locale}.json`);
      
      try {
        fs.writeFileSync(filePath, JSON.stringify(localeFiles[locale], null, 2), 'utf8');
        console.log(`  ✅ Updated ${service}/${locale}.json`);
      } catch (error) {
        console.log(`  ❌ Error writing ${filePath}: ${error.message}`);
      }
    });
    
    if (serviceFixed > 0) {
      console.log(`  🎯 Fixed ${serviceFixed} translations in ${service}\n`);
    }
  });
  
  console.log(`\n🎉 COMPLETED! Fixed ${totalFixed} missing translations across all microservices.`);
  return totalFixed;
}

// Main execution
try {
  const gaps = analyzeTranslationGaps();
  const fixedCount = fixMissingTranslations(gaps);
  
  console.log('\n📊 FINAL SUMMARY:');
  console.log('==================');
  console.log(`Total translations fixed: ${fixedCount}`);
  console.log('All microservices now have complete translations!');
  
} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}
