import { StatusCodes } from "http-status-codes";
import logger from "../../../shared/services/logger.service";
import Newsletter, { NewsletterStatus } from "../models/newsletter.model";
import { createActivityLog } from "../../../shared/models/ActivityLog";
import { saveUploadedImage } from "../services/file.service";
import mongoose from "mongoose";

/**
 * Create a new newsletter
 */
export const createNewsletter = async (req: any, res: any): Promise<any> => {
  try {
    const { header, content } = req.body;

    // Handle image upload
    if (!req.file) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("NEWSLETTER_IMAGE_REQUIRED"),
      });
    }

    // Save image and get its ID
    const imageId = await saveUploadedImage(req.file, req.user._id);

    const newsletter = await Newsletter.create({
      imageId,
      header,
      content,
      status: NewsletterStatus.Active,
    });

    // Log the creation
    await createActivityLog(
      "newsletters",
      newsletter._id,
      "CREATE",
      [],
      req.user._id,
    );

    // Populate image details
    await newsletter.populate("imageId", "location");

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("NEWSLETTER_CREATED_SUCCESS"),
      data: newsletter,
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Get all active newsletters with pagination
 */
export const getNewsletterList = async (req: any, res: any): Promise<any> => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const pipeline = [
      // Match stage - find active newsletters
      { $match: { status: NewsletterStatus.Active } },

      // Sort by creation date
      { $sort: { createdAt: -1 } },

      // Pagination
      { $skip: skip },
      { $limit: Number(limit) },

      // Lookup image details
      {
        $lookup: {
          from: "files",
          localField: "imageId",
          foreignField: "_id",
          as: "image",
        },
      },

      // Unwind the image array since we expect only one image
      { $unwind: "$image" },

      // Project final fields with image URL
      {
        $project: {
          _id: 1,
          header: 1,
          content: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          imageUrl: {
            $concat: [global.config.FILE_BASE_URL, "$image.location"],
          },
        },
      },
    ] as any[];

    const newsletters = await Newsletter.aggregate(pipeline);
    const total = await Newsletter.countDocuments({
      status: NewsletterStatus.Active,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("NEWSLETTER_FETCH_SUCCESS"),
      data: {
        newsletters,
        pagination: {
          page: Number(page),
          size: Number(limit),
          totalCount: total,
          totalPages: Math.ceil(total / Number(limit)),
        },
      },
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Get newsletter by ID
 */
export const getNewsletterById = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;

    const pipeline = [
      // Match stage - find specific active newsletter
      {
        $match: {
          _id: new mongoose.Types.ObjectId(id),
          status: NewsletterStatus.Active,
        },
      },

      // Lookup image details
      {
        $lookup: {
          from: "files",
          localField: "imageId",
          foreignField: "_id",
          as: "image",
        },
      },

      // Unwind the image array since we expect only one image
      { $unwind: "$image" },

      // Project final fields with image URL
      {
        $project: {
          _id: 1,
          header: 1,
          content: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          imageUrl: {
            $concat: [global.config.FILE_BASE_URL, "$image.location"],
          },
        },
      },
    ] as any[];

    const results = await Newsletter.aggregate(pipeline);

    if (!results.length) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("NEWSLETTER_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("NEWSLETTER_FETCH_ONE_SUCCESS"),
      data: results[0],
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Update newsletter
 */
export const updateNewsletter = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const { header, content } = req.body;

    const newsletter = await Newsletter.findOne({
      _id: id,
      status: NewsletterStatus.Active,
    });

    if (!newsletter) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("NEWSLETTER_NOT_FOUND"),
      });
    }

    const updateData: any = { header, content };

    // Handle image update if provided
    if (req.file) {
      updateData.imageId = await saveUploadedImage(req.file, req.user._id);
    }

    // Log the changes
    await createActivityLog(
      "newsletters",
      newsletter._id,
      "UPDATE",
      [],
      req.user._id,
    );

    const updatedNewsletter = await Newsletter.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true },
    ).populate("imageId", "location");

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("NEWSLETTER_UPDATED_SUCCESS"),
      data: updatedNewsletter,
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Soft delete newsletter
 */
export const deleteNewsletter = async (req: any, res: any): Promise<any> => {
  try {
    const { id } = req.params;
    const newsletter = await Newsletter.findOne({
      _id: id,
      status: NewsletterStatus.Active,
    });

    if (!newsletter) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("NEWSLETTER_NOT_FOUND"),
      });
    }

    // Soft delete by updating status
    await Newsletter.findByIdAndUpdate(id, {
      status: NewsletterStatus.Deleted,
    });

    // Log the deletion
    await createActivityLog(
      "newsletters",
      newsletter._id,
      "DELETE",
      [],
      req.user._id,
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("NEWSLETTER_DELETED_SUCCESS"),
    });
  } catch (error) {
    logger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
