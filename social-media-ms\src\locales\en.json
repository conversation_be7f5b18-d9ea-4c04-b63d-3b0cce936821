{"SOCIAL_MEDIA_CREATED": "Social Media Profile created successfully", "SOCIAL_MEDIA_UPDATED": "Social Media Profile updated successfully", "SOCIAL_MEDIA_DELETED": "Social Media Profile deleted successfully", "SOCIAL_MEDIA_NOT_FOUND": "Social Media Profile not found", "SOCIAL_MEDIA_VERSION_EXISTS": "Social Media Profile with this version already exists", "SOCIAL_MEDIA_VERSION_REQUIRED": "Version is required", "SOCIAL_MEDIA_VERSION_LENGTH": "Version must be between 1 and 20 characters", "SOCIAL_MEDIA_PLATFORM_REQUIRED": "Platform name is required", "SOCIAL_MEDIA_PLATFORM_INVALID": "Invalid social media platform", "SOCIAL_MEDIA_USERNAME_REQUIRED": "Username is required", "SOCIAL_MEDIA_USERNAME_LENGTH": "Username must be between 1 and 100 characters", "SOCIAL_MEDIA_URL_INVALID": "Invalid URL format", "SOCIAL_MEDIA_PLATFORMS_REQUIRED": "At least one platform is required", "SOCIAL_MEDIA_ID_REQUIRED": "Social Media Profile ID is required", "SOCIAL_MEDIA_INVALID_ID": "Invalid Social Media Profile ID format", "CANNOT_DELETE_ACTIVE_SOCIAL_MEDIA": "Cannot delete active Social Media Profile", "INVALID_ID": "Invalid ID format", "INTERNAL_SERVER_ERROR": "An internal server error occurred", "FORBIDDEN": "You are not authorized to access this resource.", "TOKEN_REQUIRED": "Authentication token is required.", "INVALID_PLATFORMS_DATA": "Invalid platforms data format", "VALIDATION_ERROR": "Validation error occurred", "SOCIAL_MEDIA_PROFILES_RETRIEVED": "Social media profiles retrieved successfully", "SOCIAL_MEDIA_RETRIEVED": "Social media profile retrieved successfully", "FAIL_TOKEN_EXPIRED": "Session is expired. Please log in again", "USER_NOT_FOUND": "User not found", "ACCOUNT_INACTIVE": "Account is inactive or deleted. Please contact support for assistance", "SOMETHING_WENT_WRONG": "Something went wrong. Please try again later", "ACCESS_DENIED": "Access denied. You don't have permission to perform this action", "INVALID_PAGINATION_PARAMS": "Invalid pagination parameters", "INVALID_FILTER_PARAMS": "Invalid filter parameters"}