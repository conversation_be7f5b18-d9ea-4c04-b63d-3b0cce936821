import express from "express";
import cors from "cors";
import * as config from "../shared/config.json";
import i18n from "./src/services/i18n.service";
import HandleErrorMessage from "../shared/middleware/validator";

import connectDB from "../shared/db";
import { authenticateJWT } from "../shared/middleware/auth";
import logger from "../shared/services/logger.service";
import responseFormatter from "../shared/middleware/responseFormatter.middleware";
const app = express();

require("dotenv").config();

const environment = process.env.NODE_ENV! || "dev";

const envConfig: any = JSON.parse(JSON.stringify(config))[environment];

try {
  connectDB();
  logger.info("Database connected successfully", { service: "card-ms" });
} catch (error: any) {
  logger.error(`Database connection failed: ${error.message}`, {
    service: "card-ms",
    error,
  });
  process.exit(1);
}

global.config = envConfig;

// Enable CORS for all origins
app.use(cors({ origin: "*" }));

app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(i18n.init);

import routes from "./src/routes/card.routes";

// Apply response formatter middleware
app.use(responseFormatter);

// Authenticate all routes except Swagger docs
app.use("/", authenticateJWT, routes);

// Error handling for celebrate validation errors
app.use(HandleErrorMessage);

const PORT = envConfig.services["card-ms"].PORT || 3109;

app.listen(PORT, () => {
  logger.info(`Card microservice is running on port ${PORT}`, {
    service: "card-ms",
    port: PORT,
  });
  console.log(`Card microservice is running on port ${PORT}`);
});

export default app;
