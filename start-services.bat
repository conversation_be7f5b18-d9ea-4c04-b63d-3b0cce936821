@echo off
setlocal

:: Start Sea Escape Microservices Script for Windows
:: This batch file starts all microservices sequentially
:: Usage: start-services.bat [environment]
:: Example: start-services.bat development

:: Set default environment if not provided
set ENV=dev
if not "%~1"=="" set ENV=%~1
echo Using environment: %ENV%

echo Starting Sea Escape Microservices...
echo -----------------------------------

:: Start API Gateway first
echo Starting API Gateway...
start "API Gateway" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./api/index.ts"
timeout /t 3 /nobreak > nul
echo API Gateway started successfully.

:: Start Core Services (Auth and User)
echo Starting Auth Microservice...
start "Auth Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./auth-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Auth Microservice started successfully.

echo Starting User Microservice...
start "User Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./user-ms/index.ts"
timeout /t 3 /nobreak > nul
echo User Microservice started successfully.

:: Start Business Logic Microservices
echo Starting Boat Microservice...
start "Boat Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./boat-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Boat Microservice started successfully.

echo Starting Booking Microservice...
start "Booking Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./booking-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Booking Microservice started successfully.

echo Starting Payment Microservice...
start "Payment Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./payment-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Payment Microservice started successfully.

echo Starting Wallet Microservice...
start "Wallet Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./wallet-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Wallet Microservice started successfully.

:: Start Supporting Microservices
echo Starting Wishlist Microservice...
start "Wishlist Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./wishlist-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Wishlist Microservice started successfully.

echo Starting Affiliate Microservice...
start "Affiliate Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./affiliate-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Affiliate Microservice started successfully.

echo Starting Card Microservice...
start "Card Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./card-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Card Microservice started successfully.

:: Start Content Microservices
echo Starting FAQ Microservice...
start "FAQ Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./faq-ms/index.ts"
timeout /t 3 /nobreak > nul
echo FAQ Microservice started successfully.

echo Starting Contact Us Microservice...
start "Contact Us Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./contact-us-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Contact Us Microservice started successfully.

echo Starting Privacy Policy Microservice...
start "Privacy Policy Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./privacy-policy-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Privacy Policy Microservice started successfully.

echo Starting Terms & Conditions Microservice...
start "Terms & Conditions Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./terms-condition-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Terms & Conditions Microservice started successfully.

echo Starting About Us Microservice...
start "About Us Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./about-us-ms/index.ts"
timeout /t 3 /nobreak > nul
echo About Us Microservice started successfully.

:: Start Communication Microservices
echo Starting Notification Microservice...
start "Notification Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./notification-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Notification Microservice started successfully.

echo Starting Newsletter Microservice...
start "Newsletter Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./newsletter-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Newsletter Microservice started successfully.

echo Starting Social Media Microservice...
start "Social Media Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./social-media-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Social Media Microservice started successfully.

echo Starting Reviews Microservice...
start "Reviews Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./reviews-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Reviews Microservice started successfully.

echo Starting Changelogs Microservice...
start "Changelogs Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./changelogs-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Changelogs Microservice started successfully.

echo Starting Mail Microservice...
start "Mail Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./mail-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Mail Microservice started successfully.

echo Starting Chat Microservice...
start "Chat Microservice" cmd /c "set NODE_ENV=%ENV% && npx ts-node --files ./chat-ms/index.ts"
timeout /t 3 /nobreak > nul
echo Chat Microservice started successfully.

echo.
echo All Sea Escape Microservices have been started with environment: %ENV%
echo Check individual console windows for any errors.
echo.
echo Press any key to exit this window (services will continue running)...
pause > nul

endlocal 