{"TOKEN_REQUIRED": "Authentication token is required.", "FAIL_TOKEN_EXPIRED": "Session is expired. Please log in again.", "USER_NOT_FOUND": "User not found.", "ACCOUNT_INACTIVE": "Account is inactive or deleted. Please contact support for assistance.", "SOMETHING_WENT_WRONG": "Something went wrong. Please try again later.", "ACCESS_DENIED": "Access denied.", "INTERNAL_SERVER_ERROR": "Internal server error occurred.", "INVALID_ID": "Invalid ID provided.", "INVALID_STATUS": "Invalid status provided.", "INVALID_PAGINATION_PARAMS": "Invalid pagination parameters.", "INVALID_FILTER_PARAMS": "Invalid filter parameters.", "ACCOUNT_VERIFICATION_PENDING": "Account is pending or inactive. A new OTP has been sent for verification.", "ACCOUNT_ALREADY_EXISTS": "Account already exists.", "ACCOUNT_VERIFICATION_SUCCESS": "OTP verified successfully.", "ACCOUNT_REGISTERED_SUCCESSFULLY": "User registered successfully.", "ACCOUNT_PENDING": "Account verification is pending.", "ACCOUNT_DELETED_SUCCESSFULLY": "Account deleted successfully.", "INVALID_CREDENTIALS": "Invalid email or password.", "LOGIN_SUCCESS": "Login successful.", "LOGOUT_SUCCESS": "Logout successful.", "INVALID_OTP": "Invalid OTP. Please try again.", "OTP_EXPIRED": "OTP has expired. Please request a new OTP.", "OTP_SENT": "OTP sent successfully. Please check your email.", "PASSWORD_RESET_SUCCESS": "Password reset successful.", "REFRESH_TOKEN_REQUIRED": "Refresh token is required.", "TOKEN_REFRESHED": "<PERSON><PERSON> refreshed successfully.", "REFRESH_TOKEN_EXPIRED": "Refresh token has expired.", "INVALID_REFRESH_TOKEN": "Invalid refresh token.", "CURRENT_PASSWORD_INCORRECT": "Current password is incorrect.", "SUCCESS_CHANGE_PASSWORD": "Password changed successfully.", "PROFILE_UPDATED_SUCCESSFULLY": "Profile updated successfully.", "USER_ROLE_CHANGED_SUCCESSFULLY": "User role changed successfully.", "DEVICE_ID_UPDATED_SUCCESSFULLY": "Device ID updated successfully.", "USER_PROFILE_FETCH_FAILED": "Unable to retrieve user profile information.", "DOCUMENT_UPLOAD_REQUIRED": "At least one document must be uploaded.", "DOCUMENT_UPLOAD_SUCCESS": "Documents uploaded successfully.", "DOCUMENT_NOT_FOUND": "Document not found.", "DOCUMENT_DELETED_SUCCESS": "Document deleted successfully.", "DOCUMENT_VERIFIED_SUCCESS": "Document verified successfully.", "INVALID_DOCUMENT_ID": "Invalid document ID.", "DOCUMENT_USE_USER_API": "Please use user API for document operations.", "NO_REWARDS_FOUND": "No rewards found for this user.", "REWARDS_FETCH_SUCCESS": "Rewards fetched successfully.", "BOAT_CREATED_SUCCESS": "Boat created successfully.", "BOAT_UPDATED_SUCCESS": "Boat updated successfully.", "BOAT_DELETED_SUCCESS": "Boat deleted successfully.", "BOAT_NOT_FOUND": "Boat not found.", "BOAT_NOT_OWNER": "You are not the owner of this boat.", "BOAT_INVALID_DOCUMENT_IDS": "Invalid document IDs provided.", "BOAT_AVAILABILITY_SET": "Boat availability set successfully.", "BOAT_TYPES_FETCHED": "Boat types fetched successfully.", "BOAT_LOCATIONS_FETCHED": "Boat locations fetched successfully.", "ACTIVITY_CREATED_SUCCESS": "Activity created successfully.", "ACTIVITY_UPDATED_SUCCESS": "Activity updated successfully.", "ACTIVITY_DELETED_SUCCESS": "Activity deleted successfully.", "ACTIVITY_NOT_FOUND": "Activity not found.", "ACTIVITY_NOT_OWNER": "You are not the owner of this activity.", "ACTIVITY_INVALID_ID": "Invalid activity ID.", "PLACE_CREATED_SUCCESS": "Place created successfully.", "PLACE_UPDATED_SUCCESS": "Place updated successfully.", "PLACE_DELETED_SUCCESS": "Place deleted successfully.", "PLACE_NOT_FOUND": "Place not found.", "FACILITY_CREATED_SUCCESS": "Facility created successfully.", "FACILITY_UPDATED_SUCCESS": "Facility updated successfully.", "FACILITY_DELETED_SUCCESS": "Facility deleted successfully.", "FACILITY_NOT_FOUND": "Facility not found.", "FACILITY_IMAGE_ADDED": "Facility image added successfully.", "WISHLIST_ADDED_SUCCESS": "It<PERSON> added to wishlist successfully.", "WISHLIST_REMOVED_SUCCESS": "<PERSON><PERSON> removed from wishlist successfully.", "WISHLIST_FETCH_SUCCESS": "Wishlist fetched successfully.", "WISHLIST_INVALID_BOAT_ID": "Invalid item ID.", "WISHLIST_BOAT_ID_REQUIRED": "Item ID is required.", "WISHLIST_ALREADY_IN_WISHLIST": "This item is already in your wishlist.", "WISHLIST_NOT_IN_WISHLIST": "This item is not in your wishlist.", "AFFILIATE_ALREADY_EXISTS": "Affiliate application already exists.", "AFFILIATE_CREATED_SUCCESS": "Affiliate application created successfully.", "AFFILIATE_UPDATED_SUCCESS": "Affiliate updated successfully.", "AFFILIATE_DELETED_SUCCESS": "Affiliate deleted successfully.", "AFFILIATE_NOT_FOUND": "Affiliate not found.", "AFFILIATE_FETCH_SUCCESS": "Affiliate data fetched successfully.", "AFFILIATE_APPROVED_SUCCESS": "Affiliate approved successfully.", "AFFILIATE_REJECTED_SUCCESS": "Affiliate rejected successfully.", "AFFILIATE_ALREADY_APPROVED": "Affiliate is already approved.", "AFFILIATE_ALREADY_REJECTED": "Affiliate is already rejected.", "AFFILIATE_REJECTION_REASON_REQUIRED": "Rejection reason is required.", "AFFILIATE_INVALID_STATUS": "Invalid affiliate status.", "AFFILIATE_NOT_APPROVED": "Affiliate is not approved.", "AFFILIATE_FETCH_BOATS_SUCCESS": "Affiliated boats fetched successfully.", "AFFILIATE_NO_REJECTED_APPLICATION": "No rejected affiliate application found.", "AFFILIATE_BOATS_FETCH_SUCCESS": "Affiliate boats fetched successfully.", "AFFILIATE_BOAT_DETAILS_FETCH_SUCCESS": "Affiliate boat details fetched successfully.", "AFFILIATE_EARNINGS_FETCH_SUCCESS": "Affiliate earnings fetched successfully.", "BOAT_NOT_LINKED_TO_AFFILIATE": "This boat is not linked to the specified affiliate.", "BOAT_INVALID_ID": "Invalid boat ID provided.", "BOOKING_CREATED_SUCCESS": "Booking created successfully.", "BOOKING_UPDATED_SUCCESS": "Booking updated successfully.", "BOOKING_CANCELLED_SUCCESS": "Booking cancelled successfully.", "BOOKING_ACCEPTED_SUCCESS": "Booking accepted successfully.", "BOOKING_REJECTED_SUCCESS": "Booking rejected successfully.", "BOOKING_NOT_FOUND": "Booking not found.", "BOOKING_NOT_OWNER": "You are not authorized to access this booking.", "BOOKING_FETCH_SUCCESS": "Booking data fetched successfully.", "BOOKING_STATUS_UPDATED": "Booking status updated successfully.", "BOOKING_NOT_READY_FOR_PAYMENT": "This booking is not ready for payment processing.", "UNAUTHORIZED_BOOKING_PAYMENT": "You are not authorized to make payment for this booking.", "BOOKING_PATRON_TYPE_REQUIRED_FOR_BOATS": "Patron type is required for boat bookings.", "INVALID_BOAT_ID": "Invalid boat ID provided.", "PAYMENT_SUCCESSFUL": "Payment processed successfully.", "PAYMENT_FAILED": "Payment processing failed. Please try again or use a different payment method.", "PAYMENT_INVALID_METHOD": "Invalid payment method specified.", "PAYMENT_METHOD_NOT_SUPPORTED": "The selected payment method is not supported.", "PAYMENT_FETCH_SUCCESS": "Payment details fetched successfully.", "WALLET_NOT_FOUND": "Wallet not found for this user.", "WALLET_PAYMENT_FAILED_INSUFFICIENT_BALANCE": "Insufficient funds in wallet to complete this payment.", "WALLET_CREATED_SUCCESS": "<PERSON><PERSON> created successfully.", "WALLET_UPDATED_SUCCESS": "Wallet updated successfully.", "WALLET_FETCH_SUCCESS": "Wallet data fetched successfully.", "CARD_NOT_FOUND": "The selected payment card was not found or is inactive.", "DEFAULT_CARD_NOT_FOUND": "No default payment card found. Please add a card or select a card for payment.", "CARD_PAYMENT_FAILED": "Card payment failed. Please check your card details and try again.", "CARD_ADDED_SUCCESS": "Card added successfully.", "CARD_UPDATED_SUCCESS": "Card updated successfully.", "CARD_DELETED_SUCCESS": "Card deleted successfully.", "CARD_FETCH_SUCCESS": "Card data fetched successfully.", "REVIEW_ADDED_SUCCESSFULLY": "Review added successfully.", "REVIEW_UPDATED_SUCCESS": "Review updated successfully.", "REVIEW_DELETED_SUCCESSFULLY": "Review deleted successfully.", "REVIEW_NOT_FOUND": "Review not found.", "REVIEW_ALREADY_EXISTS": "You have already reviewed this item.", "REVIEW_FETCH_SUCCESS": "Reviews fetched successfully.", "NOTIFICATION_LIST_SUCCESS": "Notifications retrieved successfully.", "NOTIFICATION_NOT_FOUND": "Notification not found.", "NOTIFICATION_MARK_READ_SUCCESS": "Notification marked as read.", "NOTIFICATION_MARK_ALL_READ_SUCCESS": "All notifications marked as read.", "NOTIFICATION_ERROR_GET": "Error getting notifications.", "NOTIFICATION_ERROR_MARK_READ": "Error marking notification as read.", "NOTIFICATION_ERROR_MARK_ALL_READ": "Error marking all notifications as read.", "FAQ_CREATED": "FAQ created successfully.", "FAQ_UPDATED": "FAQ updated successfully.", "FAQ_DELETED": "FAQ deleted successfully.", "FAQ_NOT_FOUND": "FAQ not found.", "FAQ_FETCHED": "FAQ fetched successfully.", "FAQS_FETCHED": "FAQs fetched successfully.", "NEWSLETTER_CREATED_SUCCESS": "Newsletter created successfully.", "NEWSLETTER_UPDATED_SUCCESS": "Newsletter updated successfully.", "NEWSLETTER_DELETED_SUCCESS": "Newsletter deleted successfully.", "NEWSLETTER_NOT_FOUND": "Newsletter not found.", "NEWSLETTER_FETCH_SUCCESS": "Newsletters fetched successfully.", "NEWSLETTER_FETCH_ONE_SUCCESS": "Newsletter fetched successfully.", "NEWSLETTER_IMAGE_REQUIRED": "Newsletter image is required.", "MAIL_FETCH_SUCCESS": "Mail data fetched successfully.", "MAIL_NOT_FOUND": "Mail not found.", "CHAT_HISTORY_FETCHED": "Chat history fetched successfully.", "CONVERSATIONS_FETCHED": "Conversations fetched successfully.", "RECEIVER_ID_REQUIRED": "Receiver ID is required.", "CHANGELOG_FETCH_SUCCESS": "Changelog fetched successfully.", "CHANGELOG_NOT_FOUND": "Changelog not found.", "ABOUT_US_CREATED": "About Us created successfully.", "ABOUT_US_UPDATED": "About Us updated successfully.", "ABOUT_US_DELETED": "About Us deleted successfully.", "ABOUT_US_NOT_FOUND": "About Us not found.", "ABOUT_US_VERSION_EXISTS": "About Us version already exists.", "CANNOT_DELETE_ACTIVE_ABOUT_US": "Cannot delete active About Us.", "SOCIAL_MEDIA_CREATED": "Social media created successfully.", "SOCIAL_MEDIA_UPDATED": "Social media updated successfully.", "SOCIAL_MEDIA_DELETED": "Social media deleted successfully.", "SOCIAL_MEDIA_NOT_FOUND": "Social media not found.", "SOCIAL_MEDIA_VERSION_EXISTS": "Social media version already exists.", "CANNOT_DELETE_ACTIVE_SOCIAL_MEDIA": "Cannot delete active social media.", "INVALID_CURRENCY": "Invalid currency specified. Please choose a valid currency.", "BOAT_OWNER_NOT_FOUND": "Unable to locate the boat owner's account.", "AFFILIATE_CODE_NOT_FOUND": "Affiliate code does not exist. Please check the code and try again.", "AFFILIATE_CODE_NOT_ACTIVE": "Affiliate code is not active. Only approved affiliate codes can be used.", "AFFILIATE_CODE_EXPIRED": "Affiliate code has expired and can no longer be used.", "AFFILIATE_CODE_VALID": "Affiliate code is valid and active.", "AFFILIATE_CODE_VALIDATION_ERROR": "Error occurred while validating affiliate code. Please try again.", "AFFILIATE_CODE_INVALID_FORMAT": "Affiliate code format is invalid. Code must be 6-20 alphanumeric characters.", "REFERRAL_NAME_NONE": "No referral name provided.", "REFERRAL_NAME_NOT_FOUND": "Referral name not found or not active. Please check the name and try again.", "REFERRAL_NAME_VALID": "Referral name is valid and active.", "REFERRAL_NAME_VALIDATION_ERROR": "Error occurred while validating referral name. Please try again."}