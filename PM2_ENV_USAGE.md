# PM2 Environment Configuration Guide

## Overview

This guide explains how to use the environment-specific PM2 deployment features that have been added to the Sea Escape backend. These features allow you to dynamically set the environment (dev, test, production) when starting or restarting the application with PM2.

## Available Scripts

The following npm scripts have been added or modified to support environment-specific deployments:

### Basic PM2 Commands with Environment Support

```bash
# Start all services with a specific environment
npm run pm2:start:env --env=production  # Use production environment
npm run pm2:start:env --env=test        # Use test environment
npm run pm2:start:env                   # Uses dev environment by default

# Restart all services with a specific environment
npm run pm2:restart:env --env=production
npm run pm2:restart:env --env=test
npm run pm2:restart:env                  # Uses dev environment by default
```

### Optimized Startup with Environment Support

The optimized startup scripts now accept an environment parameter:

```bash
# Start services in optimized sequence with specific environment
npm run pm2:start:optimized --env=production
npm run pm2:start:optimized --env=test
npm run pm2:start:optimized              # Uses dev environment by default
```

### Regular Startup with Environment Support

The regular startup scripts also accept an environment parameter:

```bash
# Start all services at once with specific environment
npm run pm2:start:regular --env=production
npm run pm2:start:regular --env=test
npm run pm2:start:regular                # Uses dev environment by default
```

## Direct Script Usage

You can also run the startup scripts directly with an environment parameter:

### Windows

```bash
# Using the optimized batch file directly
start-pm2-optimized.bat production
start-pm2-optimized.bat test
start-pm2-optimized.bat                  # Uses dev environment by default

# Using the regular batch file directly
start-pm2.bat production
start-pm2.bat test
start-pm2.bat                           # Uses dev environment by default
```

### Linux/macOS

```bash
# Using the optimized shell script directly
./start-pm2-optimized.sh production
./start-pm2-optimized.sh test
./start-pm2-optimized.sh                 # Uses dev environment by default

# Using the regular shell script directly
./start-pm2.sh production
./start-pm2.sh test
./start-pm2.sh                          # Uses dev environment by default
```

## How It Works

1. The scripts accept an environment parameter that defaults to "dev" if not specified
2. This parameter is passed to PM2 using the `--env` flag
3. PM2 uses the corresponding environment variables defined in `ecosystem.config.js`
4. Each microservice will start with the appropriate environment configuration

## Environment Configuration

The environment configurations are defined in `ecosystem.config.js`. Each microservice has the following environment configurations:

```javascript
env: {                 // Default environment (dev)
  NODE_ENV: "dev"
},
env_test: {            // Test environment
  NODE_ENV: "test"
},
env_production: {      // Production environment
  NODE_ENV: "production"
}
```

## Examples

### Starting in Production Mode

```bash
npm run pm2:start:optimized --env=production
```

This command will:

1. Pass "production" as an argument to the optimized startup script
2. The script will use this value to set the environment for each PM2 command
3. Each microservice will start with its production environment configuration

### Restarting in Test Mode

```bash
npm run pm2:restart:env --env=test
```

This command will restart all services using the test environment configuration.

## Troubleshooting

If you encounter issues with environment settings:

1. Verify that the environment name matches one defined in `ecosystem.config.js` (dev, test, production)
2. Check PM2 logs with `npm run pm2:logs` to see if there are any environment-related errors
3. Ensure that the NODE_ENV variable is being properly set and recognized by your application