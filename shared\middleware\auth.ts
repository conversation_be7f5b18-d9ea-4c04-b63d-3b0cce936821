import { NextFunction } from "express";
import jwt from "jsonwebtoken";
import logger from "../services/logger.service";
import User, { UserStatus } from "../../user-ms/src/models/User";
import { StatusCodes } from "http-status-codes";

export const authenticateJWT = async (
  req: any,
  res: any,
  next: NextFunction
) => {
  const authHeader = req.headers.authorization || req.headers["authorization"];

  if (!authHeader) {
    return res
      .status(StatusCodes.UNAUTHORIZED)
      .json({ status: false, message: res.__("TOKEN_REQUIRED") });
  }

  const token = authHeader.split(" ")[1];

  if (!token) {
    return res
      .status(StatusCodes.UNAUTHORIZED)
      .json({ status: false, message: res.__("TOKEN_REQUIRED") });
  }

  try {
    const decoded = jwt.verify(token, global.config.JWT_SECRET);
console.log("decoded", decoded)
    const user = await User.findById(decoded._id);
    if (!user) {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ status: false, message: res.__("USER_NOT_FOUND") });
    }
    if (
      user.status === UserStatus.Inactive ||
      user.status === UserStatus.Deleted
    ) {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ status: false, message: res.__("ACCOUNT_INACTIVE") });
    }

    req.user = user;
    next();
  } catch (error: any) {
    logger.error(error);
    if (error.message == "jwt malformed") {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .send({ status: false, message: res.__("TOKEN_REQUIRED") });
    } else {
      if (error.name == "TokenExpiredError") {
        return res.status(StatusCodes.UNAUTHORIZED).send({
          status: false,
          message: res.__("FAIL_TOKEN_EXPIRED"),
        });
      } else {
        return res.status(401).send({ status: false, message: error.message });
      }
    }
  }
};
