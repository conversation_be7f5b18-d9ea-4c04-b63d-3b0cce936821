import { Router } from "express";
import {
  forgotPasswordSendOTP,
  login,
  register,
  resetPassword,
  verifyAccount,
  getRefreshToken,
  sendOtp
} from "../controller";
import authValidator from "../validator/auth.validator";

const routes = Router();

routes.post("/register", authValidator.validateRegister, register);
routes.post("/login", authValidator.validateLogin, login);
routes.post(
  "/forgot-password",
  authValidator.validateForgetPassword,
  forgotPasswordSendOTP,
);
routes.post(
  "/reset-password",
  authValidator.validateResetPassword,
  resetPassword,
);

routes.post('/send-otp', authValidator.validateForgetPassword, sendOtp);

routes.post('/verify-otp', verifyAccount);

routes.post('/refresh-token', getRefreshToken)

export default routes;
