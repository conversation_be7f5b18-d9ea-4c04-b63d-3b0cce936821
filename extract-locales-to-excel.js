const fs = require('fs');
const path = require('path');
const XLSX = require('xlsx');

// All microservices to process
const MICROSERVICES = [
  'auth-ms',
  'user-ms',
  'boat-ms',
  'booking-ms',
  'wishlist-ms',
  'affiliate-ms',
  'notification-ms',
  'newsletter-ms',
  'faq-ms',
  'card-ms',
  'contact-us-ms',
  'privacy-policy-ms',
  'terms-condition-ms',
  'about-us-ms',
  'social-media-ms',
  'payment-ms',
  'wallet-ms',
  'reviews-ms',
  'chat-ms',
  'changelogs-ms',
  'mail-ms'
];

const LOCALES = ['en', 'es', 'de'];

function extractLocaleData() {
  console.log('🔍 EXTRACTING LOCALE DATA FROM ALL MICROSERVICES\n');

  const localeData = {
    en: [],
    es: [],
    de: []
  };

  // Track duplicate keys for analysis
  const duplicateTracker = {
    en: new Map(),
    es: new Map(),
    de: new Map()
  };

  // Process shared locales first
  console.log('📁 Processing shared locales...');
  LOCALES.forEach(locale => {
    const filePath = path.join(__dirname, 'shared', 'locales', `${locale}.json`);
    if (fs.existsSync(filePath)) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const messages = JSON.parse(content);

        Object.keys(messages).forEach(key => {
          // Track for duplicate detection
          if (duplicateTracker[locale].has(key)) {
            const existing = duplicateTracker[locale].get(key);
            existing.microservices.push('shared');
            existing.values.push(messages[key]);
          } else {
            duplicateTracker[locale].set(key, {
              microservices: ['shared'],
              values: [messages[key]]
            });
          }

          localeData[locale].push({
            Key: key,
            Value: messages[key],
            'Microservice Name': 'shared'
          });
        });

        console.log(`  ✅ ${locale}.json: ${Object.keys(messages).length} messages`);
      } catch (error) {
        console.log(`  ❌ Error reading shared/${locale}.json: ${error.message}`);
      }
    } else {
      console.log(`  ⚠️  shared/${locale}.json not found`);
    }
  });

  // Process each microservice
  MICROSERVICES.forEach(microservice => {
    console.log(`📁 Processing ${microservice}...`);

    LOCALES.forEach(locale => {
      const filePath = path.join(__dirname, microservice, 'src', 'locales', `${locale}.json`);

      if (fs.existsSync(filePath)) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          const messages = JSON.parse(content);

          Object.keys(messages).forEach(key => {
            // Track for duplicate detection
            if (duplicateTracker[locale].has(key)) {
              const existing = duplicateTracker[locale].get(key);
              existing.microservices.push(microservice);
              existing.values.push(messages[key]);
            } else {
              duplicateTracker[locale].set(key, {
                microservices: [microservice],
                values: [messages[key]]
              });
            }

            localeData[locale].push({
              Key: key,
              Value: messages[key],
              'Microservice Name': microservice
            });
          });

          console.log(`  ✅ ${locale}.json: ${Object.keys(messages).length} messages`);
        } catch (error) {
          console.log(`  ❌ Error reading ${microservice}/${locale}.json: ${error.message}`);
        }
      } else {
        console.log(`  ⚠️  ${microservice}/${locale}.json not found`);
      }
    });
  });

  // Analyze and report duplicates
  analyzeDuplicates(duplicateTracker);

  return localeData;
}

function analyzeDuplicates(duplicateTracker) {
  console.log('\n🔍 ANALYZING DUPLICATE KEYS...\n');

  let totalDuplicates = 0;

  LOCALES.forEach(locale => {
    const duplicates = [];

    duplicateTracker[locale].forEach((data, key) => {
      if (data.microservices.length > 1) {
        duplicates.push({
          key,
          microservices: data.microservices,
          values: data.values
        });
      }
    });

    if (duplicates.length > 0) {
      console.log(`📋 ${locale.toUpperCase()} - Found ${duplicates.length} duplicate keys:`);
      duplicates.forEach(dup => {
        totalDuplicates++;
        console.log(`  🔑 ${dup.key}`);
        console.log(`     Used in: ${dup.microservices.join(', ')}`);

        // Check if values are different
        const uniqueValues = [...new Set(dup.values)];
        if (uniqueValues.length > 1) {
          console.log(`     ⚠️  Different values found:`);
          dup.values.forEach((value, index) => {
            console.log(`       ${dup.microservices[index]}: "${value}"`);
          });
        } else {
          console.log(`     ✅ Same value across all microservices: "${uniqueValues[0]}"`);
        }
        console.log('');
      });
    } else {
      console.log(`✅ ${locale.toUpperCase()} - No duplicate keys found`);
    }
  });

  if (totalDuplicates === 0) {
    console.log('🎉 No duplicate keys found across all languages!');
  } else {
    console.log(`📊 Total duplicate keys found: ${totalDuplicates}`);
  }
}

function removeDuplicatesAndCreateUnique(localeData) {
  console.log('\n🔧 CREATING UNIQUE KEY DATASETS...\n');

  const uniqueData = {
    en: [],
    es: [],
    de: []
  };

  LOCALES.forEach(locale => {
    const seen = new Map();
    const data = localeData[locale];

    data.forEach(item => {
      const key = item.Key;

      if (seen.has(key)) {
        // Handle duplicate - combine microservice names
        const existing = seen.get(key);
        const combinedMicroservices = `${existing['Microservice Name']}, ${item['Microservice Name']}`;
        existing['Microservice Name'] = combinedMicroservices;

        // If values are different, note it
        if (existing.Value !== item.Value) {
          existing.Value = `${existing.Value} | ${item.Value}`;
          existing['Microservice Name'] += ' (DIFFERENT VALUES)';
        }
      } else {
        // First occurrence of this key
        seen.set(key, { ...item });
        uniqueData[locale].push(item);
      }
    });

    console.log(`✅ ${locale.toUpperCase()}: ${data.length} total → ${uniqueData[locale].length} unique keys`);
  });

  return uniqueData;
}

function createExcelSheets(localeData) {
  console.log('\n📊 CREATING EXCEL SHEETS...\n');

  // Create unique dataset (removing duplicates)
  const uniqueData = removeDuplicatesAndCreateUnique(localeData);

  // Create two workbooks: one with all data, one with unique keys only
  createCompleteWorkbook(localeData);
  createUniqueWorkbook(uniqueData);
}

function createCompleteWorkbook(localeData) {
  console.log('\n📋 Creating COMPLETE workbook (includes all duplicates)...');

  const workbook = XLSX.utils.book_new();

  LOCALES.forEach(locale => {
    const data = localeData[locale];
    console.log(`  Creating ${locale.toUpperCase()} sheet with ${data.length} entries...`);

    // Sort data by microservice, then by key
    data.sort((a, b) => {
      if (a['Microservice Name'] !== b['Microservice Name']) {
        return a['Microservice Name'].localeCompare(b['Microservice Name']);
      }
      return a.Key.localeCompare(b.Key);
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Set column widths
    const columnWidths = [
      { wch: 45 }, // Key column
      { wch: 80 }, // Value column
      { wch: 25 }  // Microservice Name column
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, `${locale.toUpperCase()}_Complete`);
  });

  // Generate filename with timestamp
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const completeFilename = `sea-escape-locales-complete-${timestamp}.xlsx`;

  // Write the file
  XLSX.writeFile(workbook, completeFilename);

  console.log(`  ✅ Complete Excel file created: ${completeFilename}`);
  return completeFilename;
}

function createUniqueWorkbook(uniqueData) {
  console.log('\n🎯 Creating UNIQUE workbook (duplicates merged)...');

  const workbook = XLSX.utils.book_new();

  LOCALES.forEach(locale => {
    const data = uniqueData[locale];
    console.log(`  Creating ${locale.toUpperCase()} sheet with ${data.length} unique entries...`);

    // Sort data by microservice, then by key
    data.sort((a, b) => {
      if (a['Microservice Name'] !== b['Microservice Name']) {
        return a['Microservice Name'].localeCompare(b['Microservice Name']);
      }
      return a.Key.localeCompare(b.Key);
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Set column widths
    const columnWidths = [
      { wch: 45 }, // Key column
      { wch: 80 }, // Value column
      { wch: 35 }  // Microservice Name column (wider for combined names)
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, `${locale.toUpperCase()}_Unique`);
  });

  // Generate filename with timestamp
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const uniqueFilename = `sea-escape-locales-unique-${timestamp}.xlsx`;

  // Write the file
  XLSX.writeFile(workbook, uniqueFilename);

  console.log(`  ✅ Unique Excel file created: ${uniqueFilename}`);

  // Print summary
  console.log('\n📈 SUMMARY:');
  LOCALES.forEach(locale => {
    console.log(`${locale.toUpperCase()}: ${uniqueData[locale].length} unique messages`);
  });

  // Print microservice breakdown for unique data
  console.log('\n📋 MICROSERVICE BREAKDOWN (Unique Keys):');
  const microserviceCounts = {};

  LOCALES.forEach(locale => {
    uniqueData[locale].forEach(item => {
      // Handle combined microservice names
      const microservices = item['Microservice Name'].split(', ');
      microservices.forEach(ms => {
        const cleanMs = ms.replace(' (DIFFERENT VALUES)', '');
        if (!microserviceCounts[cleanMs]) {
          microserviceCounts[cleanMs] = { en: 0, es: 0, de: 0 };
        }
        microserviceCounts[cleanMs][locale]++;
      });
    });
  });

  Object.keys(microserviceCounts).sort().forEach(ms => {
    const counts = microserviceCounts[ms];
    console.log(`${ms}: EN=${counts.en}, ES=${counts.es}, DE=${counts.de}`);
  });

  return uniqueFilename;
}

// Main execution
try {
  console.log('🚀 STARTING ENHANCED LOCALE EXTRACTION WITH DUPLICATE MANAGEMENT\n');

  const localeData = extractLocaleData();
  createExcelSheets(localeData);

  console.log('\n🎉 EXTRACTION COMPLETED SUCCESSFULLY!');
  console.log('📁 Two Excel files have been generated:');
  console.log('   1. Complete version (includes all duplicates for reference)');
  console.log('   2. Unique version (duplicates merged, recommended for use)');
  console.log('\n✨ The unique version is recommended for translation management');
  console.log('   as it eliminates duplicate keys while preserving all information.');

} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}
