import express from "express";
import { celebrate } from "celebrate";
import * as cardController from "../controller/card.controller";
import * as cardValidator from "../validator/card.validator";

const router = express.Router();

// Create a new card
router.post(
  "/",
  celebrate(cardValidator.createCardSchema),
  cardController.createCard,
);

// Get all cards for current user
router.get(
  "/",
  celebrate(cardValidator.getCardsSchema),
  cardController.getMyCards,
);

// Get card by ID
router.get(
  "/:id",
  celebrate(cardValidator.getCardByIdSchema),
  cardController.getCardById,
);

// Update card
router.put(
  "/:id",
  celebrate(cardValidator.updateCardSchema),
  cardController.updateCard,
);

// Delete card
router.delete(
  "/:id",
  celebrate(cardValidator.deleteCardSchema),
  cardController.deleteCard,
);

// Create payment intent for booking
router.post(
  "/payment-intent",
  celebrate(cardValidator.createPaymentIntentSchema),
  cardController.createPaymentIntent,
);

export default router;
