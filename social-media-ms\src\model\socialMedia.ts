import mongoose from "mongoose";

export interface ISocialMediaPlatform {
  platform: string;
  username: string;
  url?: string;
  icon?: mongoose.Types.ObjectId; // Reference to Files model
  isActive: boolean;
}

export interface ISocialMediaProfile {
  version: string;
  platforms: ISocialMediaPlatform[];
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

const socialMediaPlatformSchema = new mongoose.Schema<ISocialMediaPlatform>({
  platform: {
    type: String,
    required: true,
    trim: true,
    enum: ["Twitter", "Instagram", "Facebook", "Snapchat", "Other"],
  },
  username: {
    type: String,
    required: true,
    trim: true,
  },
  url: {
    type: String,
    trim: true,
  },
  icon: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'File',
    required: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
});

const socialMediaProfileSchema = new mongoose.Schema<ISocialMediaProfile>(
  {
    version: {
      type: String,
      required: true,
      trim: true,
    },
    platforms: [socialMediaPlatformSchema],
    isActive: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  },
);

// Only one social media profile can be active at a time
socialMediaProfileSchema.pre("save", async function (next) {
  if (this.isActive) {
    await this.model("SocialMediaProfile").updateMany(
      { _id: { $ne: this._id } },
      { isActive: false },
    );
  }
  next();
});

const SocialMediaProfile = mongoose.model<ISocialMediaProfile>(
  "SocialMediaProfile",
  socialMediaProfileSchema,
);

export default SocialMediaProfile;
